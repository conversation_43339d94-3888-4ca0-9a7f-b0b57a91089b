#!/usr/bin/env python3
"""
Simple Desktop v1.0.0 - 修复版本使用示例
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🚀 Simple Desktop v1.0.0 - 修复版本")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)
    
    # 创建搜索窗口
    from simple_desktop.ui.search_window import FloatingSearchWindow
    search_window = FloatingSearchWindow()
    
    # 创建改进的热键管理器
    from simple_desktop.core.improved_hotkey_manager import ImprovedHotkeyManager
    hotkey_manager = ImprovedHotkeyManager(search_window)
    
    # 启动热键监听
    hotkey_success = hotkey_manager.start_listening()
    
    if hotkey_success:
        print("✅ 热键监听启动成功")
        print("💡 双击Ctrl键切换搜索窗口")
    else:
        print("❌ 热键监听启动失败")
    
    # 显示初始窗口
    search_window.show_window()
    
    try:
        # 运行应用程序
        exit_code = app.exec()
        print(f"应用程序退出，退出代码: {exit_code}")
    except KeyboardInterrupt:
        print("用户中断应用程序")
    finally:
        # 清理
        if hotkey_success:
            hotkey_manager.stop_listening()

if __name__ == "__main__":
    main()
