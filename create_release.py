#!/usr/bin/env python3
"""
Simple Desktop 发布包创建脚本
创建完整的发布包，包含所有必要文件
"""

import os
import shutil
import zipfile
import datetime
from pathlib import Path

def create_release_package():
    """创建发布包"""
    print("=" * 60)
    print("Simple Desktop 发布包创建程序")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "dist/SimpleDesktop.exe",
        "dist/Everything64.dll", 
        "dist/ic.ico",
        "dist/install.bat"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("✗ 缺少必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\n请先运行构建脚本生成可执行文件")
        return False
    
    # 创建发布目录
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    release_dir = Path(f"release/SimpleDesktop_v1.0.0_{timestamp}")
    release_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"创建发布目录: {release_dir}")
    
    # 复制核心文件
    core_files = [
        ("dist/SimpleDesktop.exe", "SimpleDesktop.exe"),
        ("dist/Everything64.dll", "Everything64.dll"),
        ("dist/ic.ico", "ic.ico"),
        ("dist/install.bat", "install.bat")
    ]
    
    print("\n复制核心文件...")
    for src, dst in core_files:
        src_path = Path(src)
        dst_path = release_dir / dst
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
            print(f"✓ {src} -> {dst}")
        else:
            print(f"✗ {src} 不存在")
    
    # 复制文档文件
    doc_files = [
        ("BUILD_README.md", "构建说明.md"),
        ("RELEASE_NOTES.md", "发布说明.md"),
        ("README.md", "README.md")
    ]
    
    print("\n复制文档文件...")
    for src, dst in doc_files:
        src_path = Path(src)
        dst_path = release_dir / dst
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
            print(f"✓ {src} -> {dst}")
        else:
            print(f"⚠ {src} 不存在，跳过")
    
    # 创建使用说明
    usage_guide = """# Simple Desktop 使用说明

## 快速开始

### 方式一：直接运行
1. 双击 `SimpleDesktop.exe` 即可运行
2. 程序会最小化到系统托盘
3. 使用热键 `Ctrl + Space` 调出搜索窗口

### 方式二：安装到系统
1. 右键以管理员身份运行 `install.bat`
2. 程序将安装到系统目录
3. 自动创建桌面和开始菜单快捷方式

## 基本操作

### 搜索文件
- 在搜索框中输入关键词
- 支持文件名、路径、扩展名搜索
- 实时显示搜索结果

### 快捷键
- `Ctrl + Space`: 显示/隐藏搜索窗口
- `ESC`: 隐藏搜索窗口
- `Enter`: 打开选中的文件
- `↑/↓`: 选择搜索结果

### 高级功能
- 右键托盘图标可访问设置
- 支持自定义热键
- 支持多种搜索过滤器

## 系统要求
- Windows 10/11 (64位)
- Everything 搜索引擎（推荐）

## 技术支持
如有问题，请查看"发布说明.md"中的故障排除部分。
"""
    
    with open(release_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(usage_guide)
    print("✓ 创建使用说明.txt")
    
    # 创建版本信息文件
    version_info = f"""Simple Desktop v1.0.0
构建时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
平台: Windows 64位
Python版本: 3.12+
GUI框架: PySide6
搜索引擎: Everything SDK

文件清单:
- SimpleDesktop.exe (主程序)
- Everything64.dll (搜索引擎)
- ic.ico (程序图标)
- install.bat (安装脚本)
- 使用说明.txt (使用指南)
- 发布说明.md (详细说明)
"""
    
    with open(release_dir / "版本信息.txt", "w", encoding="utf-8") as f:
        f.write(version_info)
    print("✓ 创建版本信息.txt")
    
    # 创建压缩包
    zip_name = f"SimpleDesktop_v1.0.0_{timestamp}.zip"
    zip_path = Path("release") / zip_name
    
    print(f"\n创建压缩包: {zip_name}")
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in release_dir.rglob("*"):
            if file_path.is_file():
                arcname = file_path.relative_to(release_dir.parent)
                zipf.write(file_path, arcname)
                print(f"  + {arcname}")
    
    # 计算文件大小
    exe_size = Path("dist/SimpleDesktop.exe").stat().st_size
    zip_size = zip_path.stat().st_size
    
    print("\n" + "=" * 60)
    print("发布包创建完成！")
    print("=" * 60)
    print(f"发布目录: {release_dir}")
    print(f"压缩包: {zip_path}")
    print(f"可执行文件大小: {exe_size / 1024 / 1024:.1f} MB")
    print(f"压缩包大小: {zip_size / 1024 / 1024:.1f} MB")
    
    print("\n包含文件:")
    for file_path in release_dir.rglob("*"):
        if file_path.is_file():
            size = file_path.stat().st_size
            print(f"  - {file_path.name} ({size / 1024:.1f} KB)")
    
    print(f"\n✓ 发布包已准备就绪！")
    print(f"✓ 可以分发压缩包: {zip_name}")
    
    return True

def main():
    """主函数"""
    success = create_release_package()
    if not success:
        input("\n按回车键退出...")
        return 1
    
    input("\n按回车键退出...")
    return 0

if __name__ == "__main__":
    exit(main())
