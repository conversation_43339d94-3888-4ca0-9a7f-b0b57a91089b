"""
诊断搜索问题的测试脚本
1. 后缀筛选功能问题
2. 文件名搜索问题（MUN.md文件）
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_suffix_filtering():
    """测试后缀筛选功能"""
    print("🔍 测试后缀筛选功能")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.profile_manager import profile_manager
        
        engine = FileSearchEngine(profile_id=0)
        
        # 获取扫描目录
        scan_dirs = profile_manager.get_profile_scan_directories(0)
        print(f"扫描目录: {scan_dirs}")
        
        # 测试1: 使用search_with_suffix方法
        print("\n📝 测试1: search_with_suffix方法")
        results = engine.search_with_suffix("微信", ".lnk", limit=10)
        print(f"search_with_suffix('微信', '.lnk') 结果数: {len(results)}")
        for i, result in enumerate(results[:5]):
            print(f"  {i+1}. {result.filename} ({result.suffix}) - {result.filepath}")
        
        # 测试2: 模拟UI中的后缀筛选逻辑
        print("\n📝 测试2: 模拟UI后缀筛选")
        query = "微信"
        suffix_filter = ".lnk"
        
        # 这是UI中的逻辑
        if suffix_filter:
            if not suffix_filter.startswith("."):
                suffix_filter = f".{suffix_filter}"
            search_query = f"{query} ext:{suffix_filter[1:]}"
        else:
            search_query = query
            
        print(f"构建的查询: '{search_query}'")
        
        # 使用新的搜索方法
        results = engine.search(search_query, limit=10)
        print(f"新搜索方法结果数: {len(results)}")
        for i, result in enumerate(results[:5]):
            print(f"  {i+1}. {result.filename} ({result.suffix}) - {result.filepath}")
        
        # 测试3: 检查简化查询提取
        print("\n📝 测试3: 简化查询提取")
        simple_query = engine._extract_simple_query(search_query)
        print(f"原始查询: '{search_query}'")
        print(f"简化查询: '{simple_query}'")
        
        # 测试4: 直接使用Everything SDK
        print("\n📝 测试4: 直接Everything SDK测试")
        everything_results = engine.everything_sdk.search(
            query=search_query,
            max_results=10,
            match_case=False,
            match_whole_word=False,
            use_regex=False
        )
        print(f"Everything SDK直接查询结果数: {len(everything_results)}")
        for i, result in enumerate(everything_results[:5]):
            print(f"  {i+1}. {result.filename} - {result.full_path}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_mun_file_search():
    """测试MUN.md文件搜索"""
    print("\n\n🔍 测试MUN.md文件搜索")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.profile_manager import profile_manager
        
        engine = FileSearchEngine(profile_id=0)
        
        # 获取扫描目录
        scan_dirs = profile_manager.get_profile_scan_directories(0)
        print(f"扫描目录: {scan_dirs}")
        
        # 检查目标文件是否存在
        target_file = Path.home() / "Desktop" / "MUN" / "MUN.md"
        print(f"\n📁 目标文件: {target_file}")
        print(f"文件存在: {'✅' if target_file.exists() else '❌'}")
        
        if target_file.exists():
            print(f"文件大小: {target_file.stat().st_size} bytes")
            print(f"父目录: {target_file.parent}")
        
        # 检查父目录是否在扫描范围内
        desktop_path = str(Path.home() / "Desktop")
        print(f"\n📂 桌面路径: {desktop_path}")
        print(f"桌面在扫描目录中: {'✅' if desktop_path in scan_dirs else '❌'}")
        
        # 测试1: 搜索"MUN"
        print("\n📝 测试1: 搜索'MUN'")
        results = engine.search("MUN", limit=20)
        print(f"搜索'MUN'结果数: {len(results)}")
        
        found_target = False
        for i, result in enumerate(results):
            print(f"  {i+1}. {result.filename} ({result.item_type}) - {result.filepath}")
            if "MUN.md" in result.filename:
                found_target = True
                print(f"      ✅ 找到目标文件!")
        
        if not found_target:
            print("  ❌ 未找到MUN.md文件")
        
        # 测试2: 搜索完整文件名
        print("\n📝 测试2: 搜索'MUN.md'")
        results = engine.search("MUN.md", limit=20)
        print(f"搜索'MUN.md'结果数: {len(results)}")
        for i, result in enumerate(results[:10]):
            print(f"  {i+1}. {result.filename} ({result.item_type}) - {result.filepath}")
        
        # 测试3: 使用Everything SDK直接搜索
        print("\n📝 测试3: Everything SDK直接搜索")
        everything_results = engine.everything_sdk.search(
            query="MUN",
            max_results=50,
            match_case=False,
            match_whole_word=False,
            use_regex=False
        )
        print(f"Everything SDK搜索'MUN'结果数: {len(everything_results)}")
        
        found_in_everything = False
        for i, result in enumerate(everything_results):
            if "MUN" in result.filename or "MUN" in result.full_path:
                print(f"  {i+1}. {result.filename} - {result.full_path}")
                if "MUN.md" in result.filename:
                    found_in_everything = True
                    print(f"      ✅ Everything SDK找到目标文件!")
        
        if not found_in_everything:
            print("  ❌ Everything SDK也未找到MUN.md文件")
        
        # 测试4: 检查目录过滤逻辑
        print("\n📝 测试4: 检查目录过滤逻辑")
        if target_file.exists():
            target_path = str(target_file)
            print(f"目标文件路径: {target_path}")
            
            # 检查是否通过目录过滤
            from simple_desktop.search.filters import DirectoryFilter
            dir_filter = DirectoryFilter(scan_dirs)
            
            # 创建模拟的SearchResult
            from simple_desktop.search.models import SearchResult
            mock_result = SearchResult(
                filename="MUN.md",
                filepath=target_path,
                item_type="file",
                size=0,
                suffix=".md"
            )
            
            should_include = dir_filter.should_include(mock_result)
            print(f"目录过滤器结果: {'✅ 通过' if should_include else '❌ 被过滤'}")
            
            # 详细检查路径匹配
            print("\n路径匹配详情:")
            target_normalized = os.path.normpath(target_path)
            for scan_dir in scan_dirs:
                scan_normalized = os.path.normpath(scan_dir)
                matches = target_normalized.startswith(scan_normalized)
                print(f"  {scan_normalized} -> {'✅' if matches else '❌'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_ui_integration():
    """测试UI集成问题"""
    print("\n\n🔍 测试UI集成")
    print("=" * 50)
    
    try:
        # 模拟UI中的搜索逻辑
        print("📝 模拟UI搜索逻辑")
        
        # 模拟search_window.py中的perform_search方法
        query = "MUN"
        suffix_filter = ".md"
        
        if query:
            search_query = query
            if suffix_filter:
                if not suffix_filter.startswith("."):
                    suffix_filter = f".{suffix_filter}"
                search_query = f"{query} ext:{suffix_filter[1:]}"
        
        print(f"UI构建的查询: '{search_query}'")
        
        # 使用搜索引擎
        from simple_desktop.search.engine import FileSearchEngine
        engine = FileSearchEngine(profile_id=0)
        
        results = engine.search(search_query, limit=20)
        print(f"UI搜索结果数: {len(results)}")
        for i, result in enumerate(results[:10]):
            print(f"  {i+1}. {result.filename} ({result.suffix}) - {result.filepath}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("🚀 开始诊断搜索问题")
    print("=" * 80)
    
    test_suffix_filtering()
    test_mun_file_search()
    test_ui_integration()
    
    print("\n" + "=" * 80)
    print("✅ 诊断完成")


if __name__ == "__main__":
    main()
