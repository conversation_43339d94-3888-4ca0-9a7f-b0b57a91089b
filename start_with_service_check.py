#!/usr/bin/env python3
"""
带服务检查的 SimpleDesktop 启动脚本
用于测试自动启动 Everything 服务功能
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_admin_privileges():
    """检查是否有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    """主函数"""
    print("🚀 SimpleDesktop 启动 (带服务检查)")
    print("=" * 60)
    
    # 检查管理员权限
    if not check_admin_privileges():
        print("⚠️ 警告: 当前没有管理员权限")
        print("   某些服务管理功能可能无法正常工作")
        print("   建议以管理员身份运行此程序")
        print()
    
    # 预先检查和启动服务
    print("🔧 预检查 Everything 服务...")
    try:
        from simple_desktop.core.everything_service import get_service_manager
        
        service_manager = get_service_manager()
        
        print("1. 检查服务状态...")
        status = service_manager.get_service_status()
        print(f"   服务已安装: {status.get('installed', False)}")
        print(f"   服务运行中: {status.get('running', False)}")
        print(f"   服务状态: {status.get('state', 'UNKNOWN')}")
        
        if status.get('error'):
            print(f"   错误信息: {status['error']}")
        
        print("\n2. 确保服务运行...")
        if service_manager.ensure_service_running():
            print("   ✅ Everything 服务已就绪")
        else:
            print("   ❌ Everything 服务启动失败")
            print("   程序将继续运行，但搜索功能可能不可用")
        
        print("\n3. 验证 SDK 连接...")
        time.sleep(2)  # 等待服务初始化
        
        from simple_desktop.core.everything_sdk import is_everything_available
        if is_everything_available():
            print("   ✅ Everything SDK 连接成功")
        else:
            print("   ⚠️ Everything SDK 连接失败，可能需要更多时间")
            print("   等待 3 秒后重试...")
            time.sleep(3)
            if is_everything_available():
                print("   ✅ Everything SDK 连接成功 (重试后)")
            else:
                print("   ❌ Everything SDK 连接失败")
        
    except Exception as e:
        print(f"❌ 服务检查失败: {e}")
        print("程序将继续启动，但可能遇到搜索功能问题")
    
    print("\n" + "=" * 60)
    print("🎯 启动 SimpleDesktop 主程序...")
    print("=" * 60)
    
    # 启动主程序
    try:
        from simple_desktop.app import main as app_main
        return app_main()
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
        return 0
    except Exception as e:
        print(f"\n💥 程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
