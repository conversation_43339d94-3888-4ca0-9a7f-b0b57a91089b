"""
Simple Desktop 主应用程序
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication, QSystemTrayIcon, QMenu, QMessageBox
from PySide6.QtCore import Qt, QCoreApplication, QTimer
from PySide6.QtGui import QIcon, QAction

from .ui.search_window import FloatingSearchWindow
from .core.alt_hotkey import hotkey_manager
from .core.config import config_manager
from .core.profile_manager import profile_manager
from .core.everything_sdk import is_everything_available

# Qt兼容的热键管理器已直接导入


class SimpleDesktopApp:
    """Simple Desktop 应用程序主类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.app = QApplication(sys.argv)
        self.app.setQuitOnLastWindowClosed(False)  # 关闭窗口不退出程序

        # 设置应用程序信息
        QCoreApplication.setApplicationName("Simple Desktop")
        QCoreApplication.setApplicationVersion("1.0.0")
        QCoreApplication.setOrganizationName("Simple Desktop Team")

        # 检查Everything是否可用
        self.check_everything_availability()

        # 创建主搜索窗口
        self.search_window = FloatingSearchWindow(app_manager=self)

        # 设置线程安全的热键处理
        self.setup_hotkey_handling()

        # 创建系统托盘
        self.setup_tray_icon()

        # 连接窗口信号
        self.search_window.window_hidden.connect(self.on_window_hidden)
        self.search_window.profile_changed.connect(self.on_profile_changed)

    def setup_hotkey_handling(self):
        """设置热键处理"""
        print("[DEBUG] ===== 热键处理设置开始 =====")
        print("[QT-APP] 设置Qt兼容的热键处理")

        # 直接使用Qt兼容的热键管理器
        print(f"[DEBUG] 设置窗口切换回调: {self.toggle_search_window}")
        hotkey_manager.set_callback(self.toggle_search_window)

        print(f"[DEBUG] 设置Profile切换回调: {self.switch_profile}")
        print(f"[DEBUG] Profile切换回调类型: {type(self.switch_profile)}")
        hotkey_manager.set_profile_switch_callback(self.switch_profile)

        print("[QT-APP] Qt兼容热键回调已设置")
        print("[DEBUG] ===== 热键处理设置完成 =====")

    def check_everything_availability(self):
        """检查Everything是否可用"""
        if not is_everything_available():
            QMessageBox.warning(
                None,
                "Everything 不可用",
                "检测到Everything服务未运行或不可用。\n\n"
                "Simple Desktop需要Everything来提供文件搜索功能。\n"
                "请确保Everything已安装并正在运行。\n\n"
                "程序将继续运行，但搜索功能可能不可用。",
                QMessageBox.StandardButton.Ok
            )
    
    def setup_tray_icon(self):
        """设置系统托盘图标"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            print("系统托盘不可用")
            return
        
        # 创建托盘图标
        icon = self.create_app_icon()
        self.tray_icon = QSystemTrayIcon(icon)
        
        # 创建托盘菜单
        tray_menu = QMenu()
        
        # 显示搜索窗口
        show_action = QAction("显示搜索窗口", self.app)
        show_action.triggered.connect(self.show_search_window)
        tray_menu.addAction(show_action)
        
        tray_menu.addSeparator()
        
        # Profile快速切换子菜单
        profile_menu = tray_menu.addMenu("切换Profile")
        for i in range(10):
            profile_name = profile_manager.get_profile_name(i)
            action = QAction(f"{i}: {profile_name}", self.app)
            action.triggered.connect(lambda checked, pid=i: self.switch_profile(pid))
            profile_menu.addAction(action)
        
        tray_menu.addSeparator()
        
        # 设置菜单
        settings_menu = tray_menu.addMenu("设置")
        
        # 快捷键开关
        hotkey_action = QAction("启用快捷键", self.app)
        hotkey_action.setCheckable(True)
        hotkey_action.setChecked(config_manager.is_hotkey_enabled())
        hotkey_action.triggered.connect(self.toggle_hotkey)
        settings_menu.addAction(hotkey_action)
        
        settings_menu.addSeparator()
        
        # 关于
        about_action = QAction("关于", self.app)
        about_action.triggered.connect(self.show_about)
        settings_menu.addAction(about_action)
        
        tray_menu.addSeparator()
        
        # 退出应用
        quit_action = QAction("退出", self.app)
        quit_action.triggered.connect(self.quit_app)
        tray_menu.addAction(quit_action)
        
        # 设置托盘图标和菜单
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.on_tray_activated)
        
        # 显示托盘图标
        self.tray_icon.show()
        self.tray_icon.showMessage(
            "Simple Desktop",
            "文件搜索工具已启动\n双击Ctrl显示搜索窗口",
            QSystemTrayIcon.MessageIcon.Information,
            3000
        )
    
    def create_app_icon(self) -> QIcon:
        """创建应用程序图标"""
        # 尝试加载图标文件
        icon_path = Path(__file__).parent.parent / "assets" / "icon.ico"
        if icon_path.exists():
            return QIcon(str(icon_path))
        
        # 如果图标文件不存在，使用系统默认图标
        return self.app.style().standardIcon(
            self.app.style().StandardPixmap.SP_ComputerIcon
        )
    
    def on_tray_activated(self, reason):
        """处理托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.Trigger:
            # 单击托盘图标显示搜索窗口
            self.show_search_window()
        elif reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            # 双击托盘图标也显示搜索窗口
            self.show_search_window()
    
    def show_search_window(self):
        """显示搜索窗口"""
        self.search_window.show_window()
    
    def toggle_search_window(self):
        """切换搜索窗口显示状态"""
        if self.search_window.isVisible():
            self.search_window.hide_window()
        else:
            self.search_window.show_window()
    
    def switch_profile(self, profile_id: int):
        """切换搜索Profile"""
        import time
        current_time = time.strftime('%H:%M:%S', time.localtime())

        print(f"[DEBUG] ===== Profile切换开始 =====")
        print(f"[DEBUG] 回调执行: switch_profile({profile_id}) - 时间: {current_time}")
        print(f"[DEBUG] 快捷键触发: 切换到Profile {profile_id}")

        try:
            # 获取当前Profile状态
            current_profile = profile_manager.current_profile_id
            print(f"[DEBUG] 当前Profile: {current_profile}")
            print(f"[DEBUG] 目标Profile: {profile_id}")

            # 确保搜索窗口可见
            window_was_visible = self.search_window.isVisible()
            print(f"[DEBUG] 搜索窗口当前状态: {'可见' if window_was_visible else '隐藏'}")

            if not window_was_visible:
                print(f"[DEBUG] 显示搜索窗口...")
                self.search_window.show_window()
                print(f"[DEBUG] 搜索窗口已显示")

            # 切换profile
            print(f"[DEBUG] 执行Profile切换: {current_profile} -> {profile_id}")
            profile_manager.switch_profile(profile_id)

            # 验证切换结果
            new_profile = profile_manager.current_profile_id
            print(f"[DEBUG] 切换后Profile: {new_profile}")

            if new_profile == profile_id:
                print(f"[DEBUG] Profile切换成功: {current_profile} -> {profile_id}")
                print(f"[DEBUG] 已通过快捷键切换到Profile {profile_id}")
            else:
                print(f"[ERROR] Profile切换失败: 期望{profile_id}, 实际{new_profile}")

            print(f"[DEBUG] ===== Profile切换完成 =====")

        except Exception as e:
            print(f"[ERROR] Profile切换异常: {e}")
            import traceback
            traceback.print_exc()
            print(f"[DEBUG] ===== Profile切换异常结束 =====")
    
    def on_window_hidden(self):
        """处理窗口隐藏事件"""
        # 可以在这里添加窗口隐藏后的处理逻辑
        pass
    
    def on_profile_changed(self, profile_id: int):
        """处理Profile改变事件"""
        print(f"Profile已切换到: {profile_id}")
    
    def toggle_hotkey(self, enabled: bool):
        """切换快捷键启用状态"""
        config_manager.set_hotkey_enabled(enabled)
        if enabled:
            if hotkey_manager.start_listening():
                print("快捷键已启用")
            else:
                print("快捷键启用失败")
        else:
            hotkey_manager.stop_listening()
            print("快捷键已禁用")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            None,
            "关于 Simple Desktop",
            "<h3>Simple Desktop v1.0.0</h3>"
            "<p>基于Everything SDK的文件搜索工具</p>"
            "<p>提供快速、轻量的本地文件搜索体验</p>"
            "<br>"
            "<p><b>特性:</b></p>"
            "<ul>"
            "<li>秒级搜索响应</li>"
            "<li>10个独立Profile配置</li>"
            "<li>文件类型智能过滤</li>"
            "<li>全局快捷键支持</li>"
            "<li>独立浮动窗口</li>"
            "</ul>"
            "<br>"
            "<p>© 2024 Simple Desktop Team</p>"
        )
    
    def quit_app(self):
        """退出应用程序"""
        print("正在退出应用程序...")
        
        # 停止快捷键监听
        hotkey_manager.stop_listening()
        
        # 隐藏托盘图标
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
        
        # 关闭搜索窗口
        if self.search_window:
            self.search_window.close()
        
        # 退出应用
        self.app.quit()
    
    def run(self):
        """运行应用程序"""
        # 启动快捷键监听
        if config_manager.is_hotkey_enabled():
            if hotkey_manager.start_listening():
                print("全局快捷键已启动")
            else:
                print("快捷键启动失败")
        
        # 显示搜索窗口
        QTimer.singleShot(500, self.show_search_window)  # 延迟显示，确保托盘图标已创建
        
        print("Simple Desktop v1.0.0 已启动")
        print("- 双击Ctrl键显示/隐藏搜索窗口")
        print("- Ctrl+0~9切换Profile")
        print("- 系统托盘提供快速访问")
        
        # 启动事件循环
        return self.app.exec()


def main():
    """主函数"""
    import time
    current_time = time.strftime('%H:%M:%S', time.localtime())

    print("=" * 80)
    print(f"[DEBUG] Simple Desktop 启动调试模式 - 时间: {current_time}")
    print("=" * 80)
    print("[DEBUG] 启动参数:")
    print(f"[DEBUG] Python版本: {sys.version}")
    print(f"[DEBUG] 当前目录: {os.getcwd()}")
    print("=" * 80)

    try:
        print("[DEBUG] 创建SimpleDesktopApp实例...")
        app = SimpleDesktopApp()

        print("[DEBUG] 启动应用程序...")
        print("[DEBUG] 热键调试模式已启用")
        print("[DEBUG] 请按 Ctrl+0~9 测试Profile切换功能")
        print("=" * 80)

        sys.exit(app.run())
    except KeyboardInterrupt:
        print("\n[DEBUG] 程序已退出")
        sys.exit(0)
    except Exception as e:
        print(f"[ERROR] 程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
