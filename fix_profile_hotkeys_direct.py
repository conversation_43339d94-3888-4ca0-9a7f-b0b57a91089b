#!/usr/bin/env python3
"""
直接修复Simple Desktop的Profile热键问题
"""

import sys
import time
import threading
import ctypes
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_current_hotkey_manager():
    """检查当前热键管理器的问题"""
    print("🔍 检查当前热键管理器")
    print("=" * 50)
    
    try:
        from simple_desktop.core.hotkey import HotkeyManager
        
        # 查看源代码
        import inspect
        source = inspect.getsource(HotkeyManager)
        
        print("✅ 热键管理器源代码分析:")
        
        # 检查关键方法
        if "_message_loop" in source:
            print("   ✅ 包含消息循环方法")
        else:
            print("   ❌ 缺少消息循环方法")
        
        if "WM_HOTKEY" in source:
            print("   ✅ 包含热键消息处理")
        else:
            print("   ❌ 缺少热键消息处理")
        
        if "profile_switch_callback" in source:
            print("   ✅ 包含Profile切换回调")
        else:
            print("   ❌ 缺少Profile切换回调")
        
        # 检查消息循环实现
        if "GetMessageW" in source or "GetMessage" in source:
            print("   ⚠️ 使用了可能有问题的GetMessage调用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def create_fixed_hotkey_manager():
    """创建修复的热键管理器"""
    print("\n🔧 创建修复的热键管理器")
    print("=" * 50)
    
    fixed_code = '''
"""
修复的热键管理器 - 解决Profile切换问题
"""

import time
import threading
import ctypes
import ctypes.wintypes as wintypes
from typing import Optional, Callable

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_CONTROL = 0x11
VK_0 = 0x30
DOUBLE_CLICK_INTERVAL = 0.5

class FixedHotkeyManager:
    """修复的热键管理器"""
    
    def __init__(self):
        """初始化热键管理器"""
        self.is_listening = False
        self.toggle_callback: Optional[Callable[[], None]] = None
        self.profile_switch_callback: Optional[Callable[[int], None]] = None
        
        # 双击Ctrl检测
        self.last_ctrl_press_time = 0
        self.ctrl_press_count = 0
        self.ctrl_check_thread = None
        self.stop_event = threading.Event()
        
        # Windows API函数
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
        # 注册的热键ID
        self.registered_hotkeys = set()
        
        # 消息循环线程
        self.message_thread = None
        
        print("[HOTKEY] 修复的热键管理器已初始化")
    
    def set_callback(self, callback: Callable[[], None]):
        """设置窗口切换回调"""
        self.toggle_callback = callback
        print("[HOTKEY] 窗口切换回调已设置")
    
    def set_profile_switch_callback(self, callback: Callable[[int], None]):
        """设置Profile切换回调"""
        self.profile_switch_callback = callback
        print("[HOTKEY] Profile切换回调已设置")
    
    def start_listening(self) -> bool:
        """开始监听快捷键"""
        if self.is_listening:
            return True
        
        try:
            print("[HOTKEY] 开始注册Profile热键...")
            
            # 注册Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            successful_registrations = 0
            
            for i in range(10):
                hotkey_id = 1000 + i
                vk_code = VK_0 + i
                
                # 尝试注册热键
                result = self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
                if result:
                    self.registered_hotkeys.add(hotkey_id)
                    successful_registrations += 1
                    print(f"[HOTKEY] Ctrl+{i} 注册成功")
                else:
                    error_code = ctypes.GetLastError()
                    print(f"[HOTKEY] Ctrl+{i} 注册失败 (错误: {error_code})")
            
            print(f"[HOTKEY] Profile热键注册完成: {successful_registrations}/10")
            
            if successful_registrations == 0:
                print("[ERROR] 没有成功注册任何Profile热键")
                return False
            
            # 启动双击Ctrl检测线程
            self.stop_event.clear()
            self.ctrl_check_thread = threading.Thread(target=self._ctrl_monitor_thread, daemon=True)
            self.ctrl_check_thread.start()
            
            # 启动消息循环线程
            self.message_thread = threading.Thread(target=self._message_loop, daemon=True)
            self.message_thread.start()
            
            self.is_listening = True
            print("[HOTKEY] 热键监听已启动")
            return True
            
        except Exception as e:
            print(f"[ERROR] 热键监听启动失败: {e}")
            self.stop_listening()
            return False
    
    def stop_listening(self):
        """停止监听快捷键"""
        if not self.is_listening:
            return
        
        print("[HOTKEY] 停止热键监听...")
        self.is_listening = False
        self.stop_event.set()
        
        # 注销热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()
        
        # 等待线程结束
        if self.ctrl_check_thread and self.ctrl_check_thread.is_alive():
            self.ctrl_check_thread.join(timeout=1.0)
        
        print("[HOTKEY] 热键监听已停止")
    
    def _message_loop(self):
        """Windows消息循环 - 修复版本"""
        print("[HOTKEY] 消息循环线程已启动")
        
        # 使用正确的Windows API调用
        msg = wintypes.MSG()
        bRet = wintypes.BOOL()
        
        while self.is_listening:
            try:
                # 使用PeekMessage而不是GetMessage，避免阻塞
                bRet = self.user32.PeekMessageW(
                    ctypes.byref(msg),
                    None,
                    0,
                    0,
                    1  # PM_REMOVE
                )
                
                if bRet:
                    # 处理热键消息
                    if msg.message == WM_HOTKEY:
                        hotkey_id = msg.wParam
                        print(f"[HOTKEY] 收到热键消息: ID={hotkey_id}")
                        self._handle_hotkey(hotkey_id)
                    
                    # 分发消息
                    self.user32.TranslateMessage(ctypes.byref(msg))
                    self.user32.DispatchMessageW(ctypes.byref(msg))
                else:
                    # 没有消息，短暂休眠
                    time.sleep(0.01)
                
            except Exception as e:
                print(f"[ERROR] 消息循环错误: {e}")
                time.sleep(0.1)
        
        print("[HOTKEY] 消息循环线程已退出")
    
    def _handle_hotkey(self, hotkey_id: int):
        """处理热键消息"""
        if 1000 <= hotkey_id <= 1009:
            # Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            profile_id = hotkey_id - 1000
            print(f"[HOTKEY] 检测到Profile热键: Ctrl+{profile_id}")
            
            if self.profile_switch_callback:
                try:
                    print(f"[HOTKEY] 执行Profile切换回调: Profile {profile_id}")
                    self.profile_switch_callback(profile_id)
                except Exception as e:
                    print(f"[ERROR] Profile切换回调错误: {e}")
            else:
                print("[WARN] Profile切换回调未设置")
    
    def _ctrl_monitor_thread(self):
        """监控Ctrl键双击的线程"""
        last_state = False
        
        while not self.stop_event.is_set():
            try:
                # 检查Ctrl键状态
                ctrl_state = self.user32.GetAsyncKeyState(VK_CONTROL)
                is_pressed = bool(ctrl_state & 0x8000)
                
                # 边沿触发：检测从未按下到按下的转换
                if is_pressed and not last_state:
                    current_time = time.time()
                    
                    # 如果有之前的按键记录，检查双击
                    if self.ctrl_press_count > 0:
                        time_diff = current_time - self.last_ctrl_press_time
                        
                        if time_diff <= DOUBLE_CLICK_INTERVAL:
                            # 双击Ctrl检测到
                            if self.toggle_callback:
                                try:
                                    self.toggle_callback()
                                    print("[HOTKEY] 双击Ctrl检测到")
                                except Exception as e:
                                    print(f"[ERROR] 切换回调错误: {e}")
                            
                            # 重置状态
                            self.ctrl_press_count = 0
                            self.last_ctrl_press_time = 0
                            last_state = is_pressed
                            continue
                    
                    # 记录新的按键
                    self.last_ctrl_press_time = current_time
                    self.ctrl_press_count = 1
                
                # 重置超时的计数
                current_time = time.time()
                if (self.ctrl_press_count > 0 and 
                    current_time - self.last_ctrl_press_time > DOUBLE_CLICK_INTERVAL):
                    self.ctrl_press_count = 0
                
                last_state = is_pressed
                
                # 短暂休眠
                time.sleep(0.05)
                
            except Exception as e:
                print(f"[ERROR] Ctrl监控错误: {e}")
                break

# 创建全局实例
hotkey_manager = FixedHotkeyManager()
'''
    
    try:
        # 保存修复的热键管理器
        fixed_file = project_root / "simple_desktop" / "core" / "fixed_hotkey.py"
        
        with open(fixed_file, 'w', encoding='utf-8') as f:
            f.write(fixed_code)
        
        print(f"✅ 修复的热键管理器已保存到: {fixed_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建修复版本失败: {e}")
        return False

def modify_app_to_use_fixed_hotkey():
    """修改app.py使用修复的热键管理器"""
    print("\n🔧 修改app.py使用修复的热键管理器")
    print("=" * 50)
    
    try:
        # 读取当前的app.py
        app_file = project_root / "simple_desktop" / "app.py"
        
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经使用了修复版本
        if "from .core.fixed_hotkey import hotkey_manager" in content:
            print("✅ app.py已经使用修复的热键管理器")
            return True
        
        # 替换导入语句
        if "from .core.hotkey import hotkey_manager" in content:
            new_content = content.replace(
                "from .core.hotkey import hotkey_manager",
                "from .core.fixed_hotkey import hotkey_manager"
            )
            
            # 保存修改后的文件
            with open(app_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ app.py已修改为使用修复的热键管理器")
            return True
        else:
            print("⚠️ 在app.py中未找到热键管理器导入语句")
            return False
        
    except Exception as e:
        print(f"❌ 修改app.py失败: {e}")
        return False

def test_fixed_hotkey_manager():
    """测试修复的热键管理器"""
    print("\n🧪 测试修复的热键管理器")
    print("=" * 50)
    
    try:
        from simple_desktop.core.fixed_hotkey import hotkey_manager
        
        print("✅ 修复的热键管理器导入成功")
        
        # 设置测试回调
        profile_switches = []
        
        def test_profile_callback(profile_id):
            profile_switches.append(profile_id)
            print(f"[TEST] Profile切换回调触发: {profile_id}")
        
        hotkey_manager.set_profile_switch_callback(test_profile_callback)
        
        # 启动热键监听
        success = hotkey_manager.start_listening()
        
        if success:
            print("✅ 修复的热键管理器启动成功")
            
            print("\n📝 测试说明:")
            print("1. 修复的热键管理器已启动")
            print("2. 请按 Ctrl+0 到 Ctrl+9 测试Profile切换")
            print("3. 观察控制台输出")
            print("4. 测试将在20秒后自动结束")
            
            # 等待测试
            time.sleep(20)
            
            print(f"\n📊 测试结果:")
            print(f"   Profile切换次数: {len(profile_switches)}")
            
            if profile_switches:
                print("   ✅ Profile热键功能正常")
                print(f"   切换的Profile: {profile_switches}")
            else:
                print("   ❌ Profile热键功能仍有问题")
            
            # 停止监听
            hotkey_manager.stop_listening()
            
            return len(profile_switches) > 0
        else:
            print("❌ 修复的热键管理器启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试修复的热键管理器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - Profile热键直接修复")
    print("=" * 80)
    
    # 执行修复步骤
    steps = [
        ("检查当前热键管理器", check_current_hotkey_manager),
        ("创建修复的热键管理器", create_fixed_hotkey_manager),
        ("修改app.py使用修复版本", modify_app_to_use_fixed_hotkey),
        ("测试修复的热键管理器", test_fixed_hotkey_manager),
    ]
    
    passed_steps = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            if step_func():
                passed_steps += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 Profile热键直接修复完成！")
    print(f"完成步骤: {passed_steps}/{total_steps}")
    
    if passed_steps >= 3:  # 至少前3步成功
        print("\n🎉 修复基本完成！")
        print("\n💡 下一步:")
        print("1. 重新启动Simple Desktop: python main.py")
        print("2. 测试Ctrl+0~9热键是否工作")
        print("3. 观察控制台输出确认热键响应")
        
        print("\n📋 修复内容:")
        print("- 使用PeekMessage替代GetMessage避免阻塞")
        print("- 修复Windows API调用问题")
        print("- 增强错误处理和日志输出")
        print("- 保持与原有功能的兼容性")
    else:
        print(f"\n⚠️ 修复未完成，请检查相关问题")

if __name__ == "__main__":
    main()
