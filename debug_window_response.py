#!/usr/bin/env python3
"""
调试Simple Desktop窗口响应问题
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def step1_test_window_methods():
    """步骤1：测试窗口方法的基本功能"""
    print("=" * 60)
    print("步骤1：测试窗口方法的基本功能")
    print("=" * 60)
    
    try:
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        print("🔍 测试基本窗口方法:")
        
        # 测试显示窗口
        print("1. 测试show_window()方法:")
        initial_visible = search_window.isVisible()
        print(f"   初始可见性: {initial_visible}")
        
        search_window.show_window()
        app.processEvents()  # 处理Qt事件
        time.sleep(0.5)  # 等待动画完成
        
        after_show = search_window.isVisible()
        print(f"   调用show_window()后: {after_show}")
        print(f"   窗口位置: {search_window.pos()}")
        print(f"   窗口大小: {search_window.size()}")
        print(f"   窗口状态: {search_window.windowState()}")
        
        # 测试隐藏窗口
        print("\n2. 测试hide_window()方法:")
        search_window.hide_window()
        app.processEvents()
        time.sleep(0.5)
        
        after_hide = search_window.isVisible()
        print(f"   调用hide_window()后: {after_hide}")
        
        # 测试切换窗口
        print("\n3. 测试toggle_window()方法:")
        for i in range(3):
            before_toggle = search_window.isVisible()
            print(f"   切换 #{i+1} - 切换前: {before_toggle}")
            
            search_window.toggle_window()
            app.processEvents()
            time.sleep(0.5)
            
            after_toggle = search_window.isVisible()
            print(f"   切换 #{i+1} - 切换后: {after_toggle}")
            
            if before_toggle == after_toggle:
                print(f"   ❌ 切换 #{i+1} 失败：状态未改变")
                return False, search_window
            else:
                print(f"   ✅ 切换 #{i+1} 成功")
        
        print("\n✅ 基本窗口方法测试通过")
        return True, search_window
        
    except Exception as e:
        print(f"❌ 窗口方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def step2_test_thread_safety():
    """步骤2：测试线程安全性"""
    print("\n" + "=" * 60)
    print("步骤2：测试线程安全性")
    print("=" * 60)
    
    try:
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer, QThread, Signal, QObject
        import threading
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        search_window = FloatingSearchWindow()
        
        print("🔍 测试线程安全性:")
        
        # 测试从主线程调用
        print("1. 主线程调用测试:")
        main_thread_id = threading.get_ident()
        print(f"   主线程ID: {main_thread_id}")
        
        search_window.show_window()
        app.processEvents()
        time.sleep(0.5)
        
        main_thread_result = search_window.isVisible()
        print(f"   主线程调用结果: {main_thread_result}")
        
        # 测试从其他线程调用（模拟热键回调）
        print("\n2. 其他线程调用测试:")
        
        other_thread_results = []
        
        def thread_callback():
            thread_id = threading.get_ident()
            print(f"   回调线程ID: {thread_id}")
            
            try:
                before = search_window.isVisible()
                print(f"   回调前窗口状态: {before}")
                
                # 直接调用toggle_window（模拟热键回调）
                search_window.toggle_window()
                
                # 等待一下让Qt处理
                time.sleep(0.1)
                
                after = search_window.isVisible()
                print(f"   回调后窗口状态: {after}")
                
                other_thread_results.append({
                    'thread_id': thread_id,
                    'before': before,
                    'after': after,
                    'success': before != after
                })
                
            except Exception as e:
                print(f"   ❌ 线程回调异常: {e}")
                other_thread_results.append({
                    'thread_id': thread_id,
                    'error': str(e),
                    'success': False
                })
        
        # 在其他线程中执行回调
        thread = threading.Thread(target=thread_callback)
        thread.start()
        thread.join()
        
        # 处理Qt事件
        app.processEvents()
        time.sleep(0.5)
        
        # 分析结果
        if other_thread_results:
            result = other_thread_results[0]
            if result.get('success', False):
                print("   ✅ 其他线程调用成功")
                return True
            else:
                print("   ❌ 其他线程调用失败")
                if 'error' in result:
                    print(f"   错误: {result['error']}")
                return False
        else:
            print("   ❌ 没有收到线程回调结果")
            return False
        
    except Exception as e:
        print(f"❌ 线程安全性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step3_test_qt_signal_approach():
    """步骤3：测试Qt信号方法"""
    print("\n" + "=" * 60)
    print("步骤3：测试Qt信号方法")
    print("=" * 60)
    
    try:
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer, QObject, Signal
        import threading
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        search_window = FloatingSearchWindow()
        
        print("🔍 测试Qt信号方法:")
        
        # 创建信号发射器
        class HotkeySignalEmitter(QObject):
            toggle_requested = Signal()
        
        signal_emitter = HotkeySignalEmitter()
        
        # 连接信号到窗口方法
        signal_emitter.toggle_requested.connect(search_window.toggle_window)
        
        print("1. 信号连接已建立")
        
        # 测试信号发射
        toggle_count = 0
        
        def test_signal_emission():
            nonlocal toggle_count
            
            for i in range(3):
                before = search_window.isVisible()
                print(f"   信号测试 #{i+1} - 发射前: {before}")
                
                # 发射信号
                signal_emitter.toggle_requested.emit()
                
                # 处理Qt事件
                app.processEvents()
                time.sleep(0.5)
                
                after = search_window.isVisible()
                print(f"   信号测试 #{i+1} - 发射后: {after}")
                
                if before != after:
                    toggle_count += 1
                    print(f"   ✅ 信号测试 #{i+1} 成功")
                else:
                    print(f"   ❌ 信号测试 #{i+1} 失败")
        
        test_signal_emission()
        
        print(f"\n📊 信号测试结果: {toggle_count}/3 次成功")
        
        if toggle_count >= 2:
            print("✅ Qt信号方法工作正常")
            return True, signal_emitter
        else:
            print("❌ Qt信号方法存在问题")
            return False, None
        
    except Exception as e:
        print(f"❌ Qt信号方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def step4_test_esc_key_binding():
    """步骤4：测试ESC键绑定"""
    print("\n" + "=" * 60)
    print("步骤4：测试ESC键绑定")
    print("=" * 60)
    
    try:
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QKeyEvent
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        search_window = FloatingSearchWindow()
        
        print("🔍 测试ESC键绑定:")
        
        # 显示窗口
        search_window.show_window()
        app.processEvents()
        time.sleep(0.5)
        
        print(f"1. 窗口显示状态: {search_window.isVisible()}")
        
        # 模拟ESC键按下事件
        print("2. 模拟ESC键按下:")
        
        esc_event = QKeyEvent(
            QKeyEvent.Type.KeyPress,
            Qt.Key.Key_Escape,
            Qt.KeyboardModifier.NoModifier
        )
        
        # 发送ESC键事件到窗口
        app.sendEvent(search_window, esc_event)
        app.processEvents()
        time.sleep(0.5)
        
        after_esc = search_window.isVisible()
        print(f"   ESC键后窗口状态: {after_esc}")
        
        if not after_esc:
            print("   ✅ ESC键功能正常")
            return True
        else:
            print("   ❌ ESC键功能失效")
            
            # 检查是否有keyPressEvent方法
            if hasattr(search_window, 'keyPressEvent'):
                print("   ✅ keyPressEvent方法存在")
                
                # 检查方法内容
                import inspect
                source = inspect.getsource(search_window.keyPressEvent)
                if 'Key_Escape' in source:
                    print("   ✅ ESC键处理代码存在")
                else:
                    print("   ❌ ESC键处理代码缺失")
            else:
                print("   ❌ keyPressEvent方法不存在")
            
            return False
        
    except Exception as e:
        print(f"❌ ESC键绑定测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step5_test_focus_and_activation():
    """步骤5：测试焦点和激活逻辑"""
    print("\n" + "=" * 60)
    print("步骤5：测试焦点和激活逻辑")
    print("=" * 60)
    
    try:
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        search_window = FloatingSearchWindow()
        
        print("🔍 测试焦点和激活逻辑:")
        
        # 测试窗口显示和焦点
        print("1. 测试窗口显示和焦点:")
        
        search_window.show_window()
        app.processEvents()
        time.sleep(0.5)
        
        print(f"   窗口可见: {search_window.isVisible()}")
        print(f"   窗口激活: {search_window.isActiveWindow()}")
        print(f"   窗口焦点: {search_window.hasFocus()}")
        print(f"   窗口标志: {search_window.windowFlags()}")
        print(f"   窗口状态: {search_window.windowState()}")
        
        # 测试强制激活
        print("\n2. 测试强制激活:")
        
        search_window.activateWindow()
        search_window.raise_()
        app.processEvents()
        time.sleep(0.5)
        
        print(f"   强制激活后 - 激活: {search_window.isActiveWindow()}")
        print(f"   强制激活后 - 焦点: {search_window.hasFocus()}")
        
        # 测试搜索框焦点
        if hasattr(search_window, 'search_input'):
            print("\n3. 测试搜索框焦点:")
            
            search_window.search_input.setFocus()
            app.processEvents()
            
            print(f"   搜索框焦点: {search_window.search_input.hasFocus()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 焦点和激活测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step6_create_improved_hotkey_integration():
    """步骤6：创建改进的热键集成"""
    print("\n" + "=" * 60)
    print("步骤6：创建改进的热键集成")
    print("=" * 60)
    
    try:
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.core.hotkey import HotkeyManager
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QObject, Signal, QTimer
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        search_window = FloatingSearchWindow()
        
        print("🔍 创建改进的热键集成:")
        
        # 创建线程安全的信号桥接器
        class ThreadSafeHotkeyBridge(QObject):
            toggle_window_signal = Signal()
            
            def __init__(self):
                super().__init__()
                self.toggle_window_signal.connect(self.safe_toggle_window)
                self.search_window = None
            
            def set_search_window(self, window):
                self.search_window = window
            
            def safe_toggle_window(self):
                """线程安全的窗口切换方法"""
                if self.search_window:
                    try:
                        print(f"🎯 安全切换窗口 - 当前状态: {self.search_window.isVisible()}")
                        self.search_window.toggle_window()
                        print(f"   切换后状态: {self.search_window.isVisible()}")
                    except Exception as e:
                        print(f"   ❌ 安全切换失败: {e}")
            
            def hotkey_callback(self):
                """热键回调函数（可能在其他线程中调用）"""
                print("🔥 热键回调被触发，发射信号...")
                self.toggle_window_signal.emit()
        
        # 创建桥接器
        bridge = ThreadSafeHotkeyBridge()
        bridge.set_search_window(search_window)
        
        print("1. ✅ 线程安全桥接器创建成功")
        
        # 测试桥接器
        print("\n2. 测试桥接器功能:")
        
        search_window.show_window()
        app.processEvents()
        time.sleep(0.5)
        
        for i in range(3):
            print(f"   测试 #{i+1}:")
            bridge.hotkey_callback()
            app.processEvents()
            time.sleep(0.5)
        
        print("\n3. 集成到热键管理器:")
        
        hotkey_manager = HotkeyManager()
        hotkey_manager.set_callback(bridge.hotkey_callback)
        
        success = hotkey_manager.start_listening()
        
        if success:
            print("   ✅ 改进的热键集成启动成功")
            
            print("\n📝 改进版测试说明:")
            print("   1. 使用线程安全的Qt信号机制")
            print("   2. 热键回调通过信号桥接到主线程")
            print("   3. 请尝试双击Ctrl键测试")
            print("   4. 测试将在10秒后自动结束")
            
            # 显示窗口进行测试
            search_window.show_window()
            
            # 等待测试
            start_time = time.time()
            while time.time() - start_time < 10:
                app.processEvents()
                time.sleep(0.1)
            
            hotkey_manager.stop_listening()
            search_window.hide_window()
            
            print("\n✅ 改进的热键集成测试完成")
            return True, bridge
        else:
            print("   ❌ 改进的热键集成启动失败")
            return False, None
        
    except Exception as e:
        print(f"❌ 改进的热键集成创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主函数"""
    print("🔍 Simple Desktop v1.0.0 - 窗口响应问题调试")
    print("=" * 80)
    
    # 执行调试步骤
    steps = [
        ("测试窗口方法的基本功能", step1_test_window_methods),
        ("测试线程安全性", step2_test_thread_safety),
        ("测试Qt信号方法", step3_test_qt_signal_approach),
        ("测试ESC键绑定", step4_test_esc_key_binding),
        ("测试焦点和激活逻辑", step5_test_focus_and_activation),
        ("创建改进的热键集成", step6_create_improved_hotkey_integration),
    ]
    
    passed_steps = 0
    total_steps = len(steps)
    results = {}
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = step_func()
            if isinstance(result, tuple):
                success = result[0]
                results[step_name] = result
            else:
                success = result
                results[step_name] = (result,)
            
            if success:
                passed_steps += 1
                print(f"✅ {step_name} 通过")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
            results[step_name] = (False, str(e))
    
    print("\n" + "=" * 80)
    print("🎯 窗口响应问题调试完成！")
    print(f"通过步骤: {passed_steps}/{total_steps}")
    
    # 分析结果并提供解决方案
    print("\n📊 问题分析:")
    
    if results.get("测试窗口方法的基本功能", (False,))[0]:
        print("✅ 基本窗口方法工作正常")
    else:
        print("❌ 基本窗口方法存在问题")
    
    if results.get("测试线程安全性", (False,))[0]:
        print("✅ 线程调用窗口方法正常")
    else:
        print("❌ 线程安全性问题 - 这可能是主要原因")
    
    if results.get("测试Qt信号方法", (False,))[0]:
        print("✅ Qt信号机制工作正常")
    else:
        print("❌ Qt信号机制存在问题")
    
    if results.get("测试ESC键绑定", (False,))[0]:
        print("✅ ESC键功能正常")
    else:
        print("❌ ESC键功能失效")
    
    print("\n💡 解决方案建议:")
    
    if not results.get("测试线程安全性", (False,))[0]:
        print("1. 🔧 线程安全问题:")
        print("   - 热键回调在非主线程中执行")
        print("   - Qt窗口操作必须在主线程中进行")
        print("   - 建议使用Qt信号机制桥接线程")
    
    if results.get("创建改进的热键集成", (False,))[0]:
        print("2. ✅ 使用改进的线程安全热键集成")
        print("   - 已创建线程安全的信号桥接器")
        print("   - 热键回调通过Qt信号传递到主线程")
        print("   - 建议在实际应用中使用此方案")
    
    if not results.get("测试ESC键绑定", (False,))[0]:
        print("3. 🔧 ESC键修复:")
        print("   - 检查keyPressEvent方法实现")
        print("   - 确保ESC键事件正确处理")
        print("   - 可能需要重新实现键盘事件处理")

if __name__ == "__main__":
    main()
