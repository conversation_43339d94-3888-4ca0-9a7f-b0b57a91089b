#!/usr/bin/env python3
"""
测试按钮内边距调整效果
"""

import sys
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PySide6.QtCore import Qt

class ButtonTestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("按钮内边距测试")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("按钮内边距对比测试")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 原始样式按钮
        original_layout = QHBoxLayout()
        original_label = QLabel("原始样式 (padding: 6px 12px):")
        original_btn = QPushButton("操作方式")
        original_btn.setFixedSize(65, 28)
        original_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(248, 249, 250, 0.9);
                border: 1px solid rgba(222, 226, 230, 0.7);
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 11px;
                color: #34495e;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: rgba(233, 236, 239, 0.95);
                border-color: rgba(173, 181, 189, 0.9);
                color: #2c3e50;
            }
        """)
        
        original_layout.addWidget(original_label)
        original_layout.addWidget(original_btn)
        original_layout.addStretch()
        layout.addLayout(original_layout)
        
        # 调整后样式按钮 (减少内边距)
        adjusted_layout = QHBoxLayout()
        adjusted_label = QLabel("调整后样式 (padding: 4px 6px):")
        adjusted_btn = QPushButton("操作方式")
        adjusted_btn.setFixedSize(65, 28)
        adjusted_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(248, 249, 250, 0.9);
                border: 1px solid rgba(222, 226, 230, 0.7);
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 11px;
                color: #34495e;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: rgba(233, 236, 239, 0.95);
                border-color: rgba(173, 181, 189, 0.9);
                color: #2c3e50;
            }
        """)
        
        adjusted_layout.addWidget(adjusted_label)
        adjusted_layout.addWidget(adjusted_btn)
        adjusted_layout.addStretch()
        layout.addLayout(adjusted_layout)
        
        # 调整后样式按钮 (增加宽度)
        wider_layout = QHBoxLayout()
        wider_label = QLabel("增加宽度 (70px, padding: 4px 6px):")
        wider_btn = QPushButton("操作方式")
        wider_btn.setFixedSize(70, 28)
        wider_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(248, 249, 250, 0.9);
                border: 1px solid rgba(222, 226, 230, 0.7);
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 11px;
                color: #34495e;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: rgba(233, 236, 239, 0.95);
                border-color: rgba(173, 181, 189, 0.9);
                color: #2c3e50;
            }
        """)
        
        wider_layout.addWidget(wider_label)
        wider_layout.addWidget(wider_btn)
        wider_layout.addStretch()
        layout.addLayout(wider_layout)
        
        # 其他按钮对比
        other_layout = QHBoxLayout()
        other_label = QLabel("其他按钮对比:")
        
        type_btn = QPushButton("类型")
        type_btn.setFixedSize(45, 28)
        type_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(248, 249, 250, 0.9);
                border: 1px solid rgba(222, 226, 230, 0.7);
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 11px;
                color: #34495e;
                font-weight: 600;
            }
        """)
        
        path_btn = QPushButton("目录")
        path_btn.setFixedSize(45, 28)
        path_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(248, 249, 250, 0.9);
                border: 1px solid rgba(222, 226, 230, 0.7);
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 11px;
                color: #34495e;
                font-weight: 600;
            }
        """)
        
        action_btn = QPushButton("打开文件")
        action_btn.setFixedSize(80, 28)
        action_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(248, 249, 250, 0.9);
                border: 1px solid rgba(222, 226, 230, 0.7);
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 11px;
                color: #34495e;
                font-weight: 600;
            }
        """)
        
        other_layout.addWidget(other_label)
        other_layout.addWidget(type_btn)
        other_layout.addWidget(path_btn)
        other_layout.addWidget(action_btn)
        other_layout.addStretch()
        layout.addLayout(other_layout)
        
        layout.addStretch()
        
        # 说明文字
        info_label = QLabel("观察按钮文字是否完全显示，选择最佳的内边距设置")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("color: #666; font-size: 12px; margin: 10px;")
        layout.addWidget(info_label)
        
        self.setLayout(layout)

def main():
    app = QApplication(sys.argv)
    
    window = ButtonTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
