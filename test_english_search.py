# -*- coding: utf-8 -*-
"""
测试英文模糊查询问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_english_fuzzy_search():
    """测试英文模糊搜索"""
    print("测试英文模糊搜索")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        engine = FileSearchEngine(profile_id=0)
        sdk = get_everything_sdk()
        
        # 测试用例：搜索包含cursor的文件，使用cur作为查询
        test_cases = [
            ("cur", "cursor"),  # 搜索cur，期望找到cursor相关文件
            ("doc", "document"), # 搜索doc，期望找到document相关文件
            ("app", "application"), # 搜索app，期望找到application相关文件
            ("win", "window"), # 搜索win，期望找到window相关文件
        ]
        
        for query, expected_word in test_cases:
            print(f"\n测试查询: '{query}' (期望找到包含'{expected_word}'的文件)")
            
            # 测试1: 当前搜索引擎
            print("  当前搜索引擎:")
            engine_results = engine.search(query, limit=10)
            print(f"    结果数: {len(engine_results)}")
            
            found_expected = False
            for result in engine_results:
                if expected_word.lower() in result.filename.lower():
                    print(f"    ✅ 找到: {result.filename}")
                    found_expected = True
                    break
            
            if not found_expected:
                print(f"    ❌ 未找到包含'{expected_word}'的文件")
                # 显示前几个结果
                for i, result in enumerate(engine_results[:3]):
                    print(f"      {i+1}. {result.filename}")
            
            # 测试2: 检查增强查询
            enhanced_query = engine._build_enhanced_query(query)
            print(f"    增强查询: '{enhanced_query}'")
            
            # 测试3: 直接Everything SDK测试不同查询模式
            print("  Everything SDK测试:")
            
            test_queries = [
                query,           # 原始查询
                f"{query}*",     # 前缀匹配
                f"*{query}*",    # 包含匹配
            ]
            
            for test_query in test_queries:
                sdk_results = sdk.search(
                    query=test_query,
                    max_results=10,
                    match_case=False,
                    match_whole_word=False,
                    use_regex=False
                )
                
                found_in_sdk = False
                for result in sdk_results:
                    if expected_word.lower() in result.filename.lower():
                        print(f"    ✅ SDK查询'{test_query}'找到: {result.filename}")
                        found_in_sdk = True
                        break
                
                if not found_in_sdk and len(sdk_results) > 0:
                    print(f"    ❌ SDK查询'{test_query}'未找到目标 (结果数: {len(sdk_results)})")
                elif not found_in_sdk:
                    print(f"    ❌ SDK查询'{test_query}'无结果")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_current_query_building():
    """测试当前的查询构建逻辑"""
    print(f"\n\n测试查询构建逻辑")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        test_queries = [
            "cur",      # 英文短词
            "cursor",   # 英文完整词
            "远征",     # 中文词
            "33",       # 数字
            "app.exe",  # 带扩展名
        ]
        
        for query in test_queries:
            print(f"\n原始查询: '{query}'")
            
            # 检查是否包含中文
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
            is_numeric = query.isdigit()
            
            print(f"  包含中文: {has_chinese}")
            print(f"  是数字: {is_numeric}")
            
            # 测试各个方法
            simple_query = engine._extract_simple_query(query)
            enhanced_query = engine._build_enhanced_query(query)

            # 创建空的FilterCriteria来测试optimized_query
            from simple_desktop.search.filters import FilterCriteria
            criteria = FilterCriteria()
            optimized_query = engine._build_optimized_query(query, criteria)
            
            print(f"  简化查询: '{simple_query}'")
            print(f"  增强查询: '{enhanced_query}'")
            print(f"  优化查询: '{optimized_query}'")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("开始测试英文模糊查询问题")
    print("=" * 80)
    
    test_english_fuzzy_search()
    test_current_query_building()
    
    print("\n" + "=" * 80)
    print("测试完成")


if __name__ == "__main__":
    main()
