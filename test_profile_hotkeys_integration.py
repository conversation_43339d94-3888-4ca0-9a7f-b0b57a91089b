#!/usr/bin/env python3
"""
测试Profile热键的完整集成功能
"""

import sys
import time
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_main_app_profile_hotkeys():
    """测试main.py中的Profile热键功能"""
    print("🧪 测试main.py中的Profile热键功能")
    print("=" * 60)
    
    try:
        # 启动main.py应用程序
        print("1. 启动Simple Desktop应用程序...")
        
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print(f"   ✅ 应用程序已启动 (PID: {process.pid})")
        
        # 等待应用程序启动
        print("2. 等待应用程序初始化...")
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("   ✅ 应用程序正在运行")
        else:
            print("   ❌ 应用程序启动失败")
            stdout, stderr = process.communicate()
            print(f"   标准输出: {stdout}")
            print(f"   错误输出: {stderr}")
            return False
        
        print("\n📝 Profile热键测试说明:")
        print("1. Simple Desktop应用程序已启动")
        print("2. 请尝试以下Profile热键:")
        print("   - Ctrl+0: 切换到Profile 0")
        print("   - Ctrl+1: 切换到Profile 1")
        print("   - Ctrl+2: 切换到Profile 2")
        print("3. 观察搜索窗口的标签页是否正确切换")
        print("4. 测试将监控15秒")
        
        # 监控应用程序输出
        start_time = time.time()
        profile_switches_detected = 0
        
        while time.time() - start_time < 15:
            if process.poll() is not None:
                print("\n🔥 应用程序已退出")
                break
            time.sleep(0.5)
        
        # 如果进程还在运行，终止它
        if process.poll() is None:
            print("\n⏹️ 终止应用程序...")
            process.terminate()
            
            try:
                process.wait(timeout=5)
                print("   ✅ 应用程序已正常终止")
            except subprocess.TimeoutExpired:
                print("   ⚠️ 强制终止应用程序")
                process.kill()
                process.wait()
        
        # 获取应用程序输出
        try:
            stdout, stderr = process.communicate(timeout=2)
            
            print("\n📊 应用程序输出分析:")
            if stdout:
                print("标准输出:")
                lines = stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        print(f"  {line}")
                        
                        # 检查Profile切换相关的日志
                        if "快捷键触发: 切换到Profile" in line:
                            profile_switches_detected += 1
                        elif "已通过快捷键切换到Profile" in line:
                            profile_switches_detected += 1
                        elif "Failed to register hotkey Ctrl+" in line:
                            print(f"    ⚠️ 热键注册失败: {line}")
                
                if profile_switches_detected > 0:
                    print(f"\n✅ 检测到 {profile_switches_detected} 次Profile切换")
                else:
                    print("\n⚠️ 未检测到Profile切换日志")
            
            if stderr:
                print("错误输出:")
                for line in stderr.strip().split('\n'):
                    if line.strip():
                        print(f"  {line}")
        except subprocess.TimeoutExpired:
            print("   ⚠️ 获取输出超时")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_profile_ui_integration():
    """测试Profile UI集成"""
    print("\n🧪 测试Profile UI集成")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.core.profile_manager import profile_manager
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        print("✅ 搜索窗口创建成功")
        
        # 显示窗口
        search_window.show_window()
        app.processEvents()
        time.sleep(1)
        
        print("✅ 搜索窗口已显示")
        
        # 测试Profile切换
        print("\n测试Profile切换:")
        
        for i in range(3):
            print(f"   切换到Profile {i}...")
            
            # 切换Profile
            profile_manager.switch_profile(i)
            app.processEvents()
            time.sleep(0.5)
            
            # 检查当前Profile
            current_profile = profile_manager.current_profile_id
            
            if current_profile == i:
                print(f"     ✅ Profile {i} 切换成功")
                
                # 检查UI是否更新
                if hasattr(search_window, 'current_profile_id'):
                    ui_profile = search_window.current_profile_id
                    if ui_profile == i:
                        print(f"     ✅ UI已更新到Profile {i}")
                    else:
                        print(f"     ⚠️ UI未更新 (显示: {ui_profile}, 期望: {i})")
                
            else:
                print(f"     ❌ Profile {i} 切换失败")
        
        # 隐藏窗口
        search_window.hide()
        
        return True
        
    except Exception as e:
        print(f"❌ Profile UI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_profile_hotkey_usage_guide():
    """创建Profile热键使用指南"""
    print("\n📝 创建Profile热键使用指南")
    print("=" * 60)
    
    guide_content = '''# Simple Desktop Profile热键使用指南

## 🎯 Profile热键功能

Simple Desktop支持通过Ctrl+数字键快速切换不同的Profile配置。

### 热键组合

- **Ctrl+0**: 切换到Profile 0 (默认桌面)
- **Ctrl+1**: 切换到Profile 1 (标签1)
- **Ctrl+2**: 切换到Profile 2 (标签2)
- **Ctrl+3**: 切换到Profile 3 (标签3)
- **Ctrl+4**: 切换到Profile 4 (标签4)
- **Ctrl+5**: 切换到Profile 5 (标签5)
- **Ctrl+6**: 切换到Profile 6 (标签6)
- **Ctrl+7**: 切换到Profile 7 (标签7)
- **Ctrl+8**: 切换到Profile 8 (标签8)
- **Ctrl+9**: 切换到Profile 9 (标签9)

### 功能特点

1. **自动显示窗口**: 如果搜索窗口当前隐藏，按Profile热键会自动显示窗口
2. **即时切换**: Profile切换是即时的，无需等待
3. **UI更新**: 切换后搜索窗口的标签页会相应更新
4. **全局热键**: 在任何应用程序中都可以使用这些热键

### 使用场景

- **工作环境**: 为不同的工作项目配置不同的搜索路径
- **文件分类**: 按文件类型或用途分组搜索
- **快速访问**: 为常用文件夹创建专门的Profile

### 故障排除

如果Profile热键不工作：

1. **检查权限**: 确保应用程序有足够的权限注册全局热键
2. **检查冲突**: 确认热键没有被其他应用程序占用
3. **重启应用**: 尝试重新启动Simple Desktop
4. **管理员权限**: 尝试以管理员身份运行应用程序

### 日志输出

当Profile热键工作时，您会在控制台看到类似的输出：

```
快捷键触发: 切换到Profile 1
Switched from Profile 0 to Profile 1
已通过快捷键切换到Profile 1
```

### 配置Profile

每个Profile可以配置：
- 搜索路径
- 文件类型过滤
- 排序方式
- 显示选项

通过搜索窗口的设置菜单可以配置各个Profile的参数。
'''
    
    try:
        guide_file = project_root / "profile_hotkey_usage_guide.md"
        
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"✅ 使用指南已保存到: {guide_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建使用指南失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - Profile热键集成测试")
    print("=" * 80)
    
    # 执行测试步骤
    tests = [
        ("Profile UI集成测试", test_profile_ui_integration),
        ("main.py Profile热键测试", test_main_app_profile_hotkeys),
        ("创建使用指南", create_profile_hotkey_usage_guide),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 完成")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 Profile热键集成测试完成！")
    print(f"完成测试: {passed_tests}/{total_tests}")
    
    if passed_tests >= 2:
        print("\n🎉 Profile热键功能基本正常！")
        print("\n💡 使用方法:")
        print("1. 启动Simple Desktop: python main.py")
        print("2. 使用Ctrl+0~9切换Profile")
        print("3. 观察搜索窗口标签页的变化")
        
        print("\n📋 注意事项:")
        print("- 如果看到'Failed to register hotkey'消息，这可能是正常的")
        print("- 热键功能可能仍然工作，即使显示注册失败")
        print("- 某些热键可能被系统或其他应用占用")
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 项测试失败")
        print("请检查Profile热键功能的实现")

if __name__ == "__main__":
    main()
