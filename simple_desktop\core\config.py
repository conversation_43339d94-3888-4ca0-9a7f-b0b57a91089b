"""
配置管理器
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class ProfileConfig:
    """Profile配置"""
    name: str
    scan_directories: List[str]
    enabled_file_types: List[str]
    search_delay: int = 200  # 搜索延迟（毫秒）
    max_results: int = 50


@dataclass
class AppConfig:
    """应用配置"""
    current_profile_id: int = 0
    default_action: str = "open_file"  # "open_file" or "open_directory"
    window_position: Optional[Dict[str, int]] = None
    hotkey_enabled: bool = True
    tray_enabled: bool = True
    profiles: Dict[int, ProfileConfig] = None
    
    def __post_init__(self):
        if self.profiles is None:
            self.profiles = {}


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.config_dir = Path.home() / ".simple_desktop"
        self.config_file = self.config_dir / "config.json"
        self.config = AppConfig()
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 加载配置
        self.load_config()
        
        # 确保默认Profile存在
        self._ensure_default_profiles()
    
    def load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 重建配置对象
                self.config = AppConfig(
                    current_profile_id=data.get('current_profile_id', 0),
                    default_action=data.get('default_action', 'open_file'),
                    window_position=data.get('window_position'),
                    hotkey_enabled=data.get('hotkey_enabled', True),
                    tray_enabled=data.get('tray_enabled', True),
                    profiles={}
                )
                
                # 重建Profile配置
                profiles_data = data.get('profiles', {})
                for profile_id_str, profile_data in profiles_data.items():
                    profile_id = int(profile_id_str)
                    self.config.profiles[profile_id] = ProfileConfig(
                        name=profile_data.get('name', f'Profile {profile_id}'),
                        scan_directories=profile_data.get('scan_directories', []),
                        enabled_file_types=profile_data.get('enabled_file_types', []),
                        search_delay=profile_data.get('search_delay', 200),
                        max_results=profile_data.get('max_results', 50)
                    )
                    
            except Exception as e:
                print(f"Failed to load config: {e}")
                self.config = AppConfig()
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 转换为可序列化的字典
            config_dict = {
                'current_profile_id': self.config.current_profile_id,
                'default_action': self.config.default_action,
                'window_position': self.config.window_position,
                'hotkey_enabled': self.config.hotkey_enabled,
                'tray_enabled': self.config.tray_enabled,
                'profiles': {}
            }
            
            # 转换Profile配置
            for profile_id, profile in self.config.profiles.items():
                config_dict['profiles'][str(profile_id)] = asdict(profile)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Failed to save config: {e}")
    
    def _ensure_default_profiles(self):
        """确保默认Profile存在"""
        # 创建默认的10个Profile
        for i in range(10):
            if i not in self.config.profiles:
                name = "默认桌面" if i == 0 else f"标签{i}"
                self.config.profiles[i] = ProfileConfig(
                    name=name,
                    scan_directories=self._get_default_scan_directories() if i == 0 else [],
                    enabled_file_types=[],
                    search_delay=200,
                    max_results=50
                )
        
        # 保存配置
        self.save_config()
    
    def _get_default_scan_directories(self) -> List[str]:
        """获取默认扫描目录 - 优化为只包含桌面相关目录"""
        default_dirs = []

        # 添加用户桌面
        desktop = Path.home() / "Desktop"
        if desktop.exists():
            default_dirs.append(str(desktop))

        # 添加公共桌面
        public_desktop = Path("C:/Users/<USER>/Desktop")
        if public_desktop.exists():
            default_dirs.append(str(public_desktop))

        # 添加开始菜单程序目录
        start_menu = Path("C:/ProgramData/Microsoft/Windows/Start Menu/Programs")
        if start_menu.exists():
            default_dirs.append(str(start_menu))

        # 移除Documents和Downloads目录，聚焦于快速启动相关的目录
        # 这样可以减少搜索结果中不相关的文档和下载文件

        return default_dirs
    
    # Profile相关方法
    def get_current_profile_id(self) -> int:
        """获取当前Profile ID"""
        return self.config.current_profile_id
    
    def set_current_profile_id(self, profile_id: int):
        """设置当前Profile ID"""
        if 0 <= profile_id <= 9:
            self.config.current_profile_id = profile_id
            self.save_config()
    
    def get_profile(self, profile_id: int) -> Optional[ProfileConfig]:
        """获取指定Profile配置"""
        return self.config.profiles.get(profile_id)
    
    def get_current_profile(self) -> Optional[ProfileConfig]:
        """获取当前Profile配置"""
        return self.get_profile(self.config.current_profile_id)
    
    def update_profile(self, profile_id: int, **kwargs):
        """更新Profile配置"""
        if profile_id in self.config.profiles:
            profile = self.config.profiles[profile_id]
            for key, value in kwargs.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            self.save_config()
    
    # 扫描目录相关方法
    def get_scan_directories(self, profile_id: Optional[int] = None) -> List[str]:
        """获取扫描目录列表"""
        if profile_id is None:
            profile_id = self.config.current_profile_id
        
        profile = self.get_profile(profile_id)
        return profile.scan_directories if profile else []
    
    def add_scan_directory(self, directory: str, profile_id: Optional[int] = None) -> bool:
        """添加扫描目录"""
        if profile_id is None:
            profile_id = self.config.current_profile_id
        
        if profile_id in self.config.profiles:
            profile = self.config.profiles[profile_id]
            if directory not in profile.scan_directories:
                profile.scan_directories.append(directory)
                self.save_config()
                return True
        return False
    
    def remove_scan_directory(self, directory: str, profile_id: Optional[int] = None) -> bool:
        """移除扫描目录"""
        if profile_id is None:
            profile_id = self.config.current_profile_id
        
        if profile_id in self.config.profiles:
            profile = self.config.profiles[profile_id]
            if directory in profile.scan_directories:
                profile.scan_directories.remove(directory)
                self.save_config()
                return True
        return False
    
    # 文件类型相关方法
    def get_enabled_file_types(self, profile_id: Optional[int] = None) -> List[str]:
        """获取启用的文件类型"""
        if profile_id is None:
            profile_id = self.config.current_profile_id
        
        profile = self.get_profile(profile_id)
        return profile.enabled_file_types if profile else []
    
    def set_enabled_file_types(self, file_types: List[str], profile_id: Optional[int] = None):
        """设置启用的文件类型"""
        if profile_id is None:
            profile_id = self.config.current_profile_id
        
        if profile_id in self.config.profiles:
            self.config.profiles[profile_id].enabled_file_types = file_types
            self.save_config()
    
    # 应用设置相关方法
    def get_default_action(self) -> str:
        """获取默认行为"""
        return self.config.default_action
    
    def set_default_action(self, action: str):
        """设置默认行为"""
        if action in ["open_file", "open_directory"]:
            self.config.default_action = action
            self.save_config()
    
    def get_search_delay(self, profile_id: Optional[int] = None) -> int:
        """获取搜索延迟"""
        if profile_id is None:
            profile_id = self.config.current_profile_id
        
        profile = self.get_profile(profile_id)
        return profile.search_delay if profile else 200
    
    def get_window_position(self) -> Optional[Dict[str, int]]:
        """获取窗口位置"""
        return self.config.window_position
    
    def set_window_position(self, x: int, y: int):
        """设置窗口位置"""
        self.config.window_position = {"x": x, "y": y}
        self.save_config()
    
    def is_hotkey_enabled(self) -> bool:
        """是否启用快捷键"""
        return self.config.hotkey_enabled
    
    def set_hotkey_enabled(self, enabled: bool):
        """设置快捷键启用状态"""
        self.config.hotkey_enabled = enabled
        self.save_config()
    
    def is_tray_enabled(self) -> bool:
        """是否启用系统托盘"""
        return self.config.tray_enabled
    
    def set_tray_enabled(self, enabled: bool):
        """设置系统托盘启用状态"""
        self.config.tray_enabled = enabled
        self.save_config()


# 全局配置管理器实例
config_manager = ConfigManager()
