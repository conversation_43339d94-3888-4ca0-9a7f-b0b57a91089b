"""
搜索结果数据模型
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime

from ..core.everything_sdk import SearchResult as EverythingResult


@dataclass
class SearchResult:
    """统一的搜索结果数据类"""
    filename: str
    filepath: str
    item_type: str  # "file" or "folder"
    size: int = 0
    suffix: str = ""
    date_modified: Optional[str] = None
    date_modified_dt: Optional[datetime] = None  # datetime对象用于过滤
    size_bytes: int = 0  # 精确的字节大小
    
    @classmethod
    def from_everything_result(cls, result: EverythingResult) -> 'SearchResult':
        """从Everything结果创建SearchResult"""
        # 修复：确保suffix包含点号
        suffix = result.extension
        if suffix and not suffix.startswith("."):
            suffix = f".{suffix}"

        # 解析日期
        date_modified_dt = None
        if result.date_modified:
            try:
                # 尝试解析Everything返回的日期字符串
                # Everything通常返回格式如 "2023-12-01 10:30:45"
                date_modified_dt = datetime.strptime(result.date_modified, "%Y-%m-%d %H:%M:%S")
            except (ValueError, TypeError):
                # 如果解析失败，保持为None
                pass

        return cls(
            filename=result.filename,
            filepath=result.full_path,
            item_type="folder" if result.is_folder else "file",
            size=result.size,
            suffix=suffix,
            date_modified=result.date_modified,
            date_modified_dt=date_modified_dt,
            size_bytes=result.size  # Everything返回的size已经是字节
        )
