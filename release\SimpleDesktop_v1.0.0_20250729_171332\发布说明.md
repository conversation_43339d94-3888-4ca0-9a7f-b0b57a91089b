# Simple Desktop 发布说明

## 版本信息
- **版本**: 1.0.0
- **构建日期**: 2025-07-29
- **平台**: Windows 10/11 (64位)

## 功能特性

### 核心功能
- 🔍 **快速搜索**: 集成 Everything 搜索引擎，毫秒级文件搜索
- ⌨️ **全局热键**: 支持自定义热键快速调出搜索窗口
- 🎯 **智能过滤**: 支持文件类型、日期、大小等多维度过滤
- 📁 **目录搜索**: 支持指定目录范围搜索
- 🔄 **实时搜索**: 输入即搜索，实时显示结果

### 界面特性
- 🎨 **现代界面**: 基于 PySide6 的现代化用户界面
- 🪟 **浮动窗口**: 轻量级浮动搜索窗口
- 🎭 **系统托盘**: 最小化到系统托盘，不占用任务栏
- 🖱️ **拖拽支持**: 支持窗口拖拽移动

### 高级功能
- 📋 **配置管理**: 支持多配置文件管理
- 🔧 **自定义设置**: 丰富的自定义选项
- 🚀 **快速启动**: 开机自启动支持
- 📝 **右键菜单**: 集成系统右键菜单

## 系统要求

### 最低要求
- **操作系统**: Windows 10 (1903) 或更高版本
- **内存**: 512 MB RAM
- **存储空间**: 100 MB 可用空间
- **其他**: Everything 搜索引擎（程序会自动检测）

### 推荐配置
- **操作系统**: Windows 11
- **内存**: 2 GB RAM 或更多
- **存储空间**: 500 MB 可用空间
- **处理器**: 双核 2.0 GHz 或更高

## 安装说明

### 方式一：直接运行（推荐）
1. 下载 `SimpleDesktop.exe`
2. 双击运行即可使用
3. 首次运行会自动配置必要设置

### 方式二：系统安装
1. 下载完整安装包
2. 运行 `install.bat` 安装脚本
3. 程序将安装到 `C:\Program Files\SimpleDesktop\`
4. 自动创建桌面和开始菜单快捷方式

## 使用指南

### 基本使用
1. **启动程序**: 双击桌面图标或使用热键
2. **搜索文件**: 在搜索框中输入关键词
3. **打开文件**: 双击搜索结果或按回车键
4. **隐藏窗口**: 按 ESC 键或点击其他区域

### 热键设置
- **默认热键**: `Ctrl + Space`
- **自定义热键**: 在设置中可修改
- **配置热键**: 支持多种热键组合

### 搜索技巧
- **文件名搜索**: 直接输入文件名
- **扩展名过滤**: 使用 `*.txt` 格式
- **路径搜索**: 输入完整或部分路径
- **大小写**: 搜索不区分大小写

## 故障排除

### 常见问题

**Q: 程序无法启动**
A: 检查是否安装了 Visual C++ Redistributable，或尝试以管理员身份运行

**Q: 搜索无结果**
A: 确保 Everything 搜索引擎正在运行，或重新安装 Everything

**Q: 热键不工作**
A: 检查是否与其他程序热键冲突，尝试更改热键设置

**Q: 程序运行缓慢**
A: 检查系统资源使用情况，关闭不必要的后台程序

### 技术支持
如遇到其他问题，请：
1. 查看程序日志文件
2. 检查系统事件查看器
3. 联系技术支持

## 更新日志

### v1.0.0 (2025-07-29)
- 🎉 首次发布
- ✨ 实现核心搜索功能
- 🔧 集成 Everything 搜索引擎
- 🎨 现代化用户界面
- ⌨️ 全局热键支持
- 📁 多配置文件管理
- 🚀 系统托盘集成

## 许可证
本软件遵循 MIT 许可证。

## 致谢
- Everything 搜索引擎 - 提供强大的文件搜索能力
- PySide6 - 现代化的 Python GUI 框架
- PyInstaller - 可执行文件打包工具

---

**Simple Desktop Team**  
版权所有 © 2025
