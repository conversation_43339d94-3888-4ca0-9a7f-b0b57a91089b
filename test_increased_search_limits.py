#!/usr/bin/env python3
"""
测试增加的搜索结果数量限制
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_search_limits():
    """测试搜索结果数量限制"""
    print("🔍 测试增加的搜索结果数量限制")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试不同的搜索场景
        test_cases = [
            {
                "name": "简单关键词搜索",
                "query": "KAN",
                "criteria": FilterCriteria(
                    scan_directories=["D:/LEMON-PAPER"],
                    include_folders=True
                ),
                "expected_behavior": "应该获取大量原始结果并正确过滤"
            },
            {
                "name": "通配符搜索",
                "query": "test",
                "criteria": FilterCriteria(
                    include_folders=True
                ),
                "expected_behavior": "应该获取大量原始结果"
            },
            {
                "name": "复杂过滤条件",
                "query": "pdf",
                "criteria": FilterCriteria(
                    scan_directories=["D:/LEMON-PAPER"],
                    file_types=["documents"],
                    include_folders=False
                ),
                "expected_behavior": "应该获取大量原始结果并应用多重过滤"
            }
        ]
        
        for test_case in test_cases:
            print(f"\n🎯 测试: {test_case['name']}")
            print(f"   查询: '{test_case['query']}'")
            print(f"   预期: {test_case['expected_behavior']}")
            
            # 计算搜索倍数
            search_multiplier = engine._calculate_search_multiplier(test_case['criteria'])
            print(f"   搜索倍数: {search_multiplier}")
            
            # 计算搜索限制（假设limit=50）
            limit = 50
            search_limit = min(limit * search_multiplier, 5000)
            print(f"   搜索限制: {search_limit}")
            
            # 构建查询
            everything_query = engine._build_optimized_query(test_case['query'], test_case['criteria'])
            print(f"   优化后查询: '{everything_query}'")
            
            # 执行Everything搜索
            try:
                everything_results = engine.everything_sdk.search(
                    query=everything_query,
                    max_results=search_limit,
                    match_case=False,
                    match_whole_word=False,
                    use_regex=False
                )
                
                print(f"   原始结果数量: {len(everything_results)}")
                
                # 显示前几个结果
                if everything_results:
                    print(f"   前3个结果:")
                    for i, result in enumerate(everything_results[:3]):
                        print(f"     {i+1}. {result.filename}")
                        print(f"        路径: {result.full_path}")
                
                # 执行完整的两阶段搜索
                final_results = engine._search_with_filters(test_case['query'], test_case['criteria'], limit)
                print(f"   最终过滤结果: {len(final_results)}")
                
                if final_results:
                    print(f"   最终结果:")
                    for i, result in enumerate(final_results):
                        print(f"     {i+1}. {result.filename}")
                        print(f"        路径: {result.filepath}")
                
                # 评估结果
                if len(everything_results) >= 100:
                    print(f"   ✅ 成功获取大量原始结果 ({len(everything_results)})")
                elif len(everything_results) >= 50:
                    print(f"   ⚠️ 获取中等数量原始结果 ({len(everything_results)})")
                else:
                    print(f"   ❌ 原始结果数量较少 ({len(everything_results)})")
                
            except Exception as e:
                print(f"   ❌ 搜索失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_kan_search_with_increased_limits():
    """专门测试KAN搜索的改进效果"""
    print(f"\n🎯 专门测试KAN搜索的改进效果")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试KAN搜索
        queries = ["KAN", "EEGKAN"]
        
        for query in queries:
            print(f"\n🔍 测试查询: '{query}'")
            
            # 使用标准搜索方法
            results = engine.search(
                query=query,
                limit=50,
                file_types=None,
                include_folders=True,
                global_search=False
            )
            
            print(f"   搜索结果数量: {len(results)}")
            
            if results:
                print(f"   找到的文件:")
                for result in results:
                    print(f"     - {result.filename}")
                    print(f"       路径: {result.filepath}")
                    print(f"       类型: {result.item_type}")
                
                # 检查是否找到了预期的文件
                if query == "KAN":
                    expected_files = ["KAN.pdf", "EEGKAN.pdf"]
                    found_files = [r.filename for r in results]
                    
                    for expected in expected_files:
                        if expected in found_files:
                            print(f"     ✅ 找到预期文件: {expected}")
                        else:
                            print(f"     ❌ 未找到预期文件: {expected}")
                
                elif query == "EEGKAN":
                    if any("EEGKAN.pdf" in r.filename for r in results):
                        print(f"     ✅ 找到EEGKAN.pdf")
                    else:
                        print(f"     ❌ 未找到EEGKAN.pdf")
            else:
                print(f"   ❌ 没有找到任何结果")
        
        return True
        
    except Exception as e:
        print(f"❌ KAN搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 测试增加的搜索结果数量限制")
    
    # 运行测试
    limits_test = test_search_limits()
    kan_test = test_kan_search_with_increased_limits()
    
    print(f"\n" + "=" * 80)
    print("📊 测试结果:")
    print(f"   搜索限制测试: {'✅ 通过' if limits_test else '❌ 失败'}")
    print(f"   KAN搜索测试: {'✅ 通过' if kan_test else '❌ 失败'}")
    
    if limits_test and kan_test:
        print("\n🎉 搜索结果数量限制增加成功！")
        print("\n📈 改进效果:")
        print("   - 基础搜索倍数从3增加到10")
        print("   - 最大搜索倍数从15增加到50")
        print("   - 最大搜索数量从2000增加到5000")
        print("   - 目录过滤时搜索数量增加到3000")
        print("   - 扩展名搜索数量增加到2000")
    else:
        print("\n⚠️ 部分测试失败，可能需要进一步调整")

if __name__ == "__main__":
    main()
