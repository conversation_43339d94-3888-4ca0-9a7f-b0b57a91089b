# -*- coding: utf-8 -*-
"""
测试桌面搜索功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_desktop_search():
    """测试桌面搜索"""
    print("测试桌面搜索功能")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.profile_manager import profile_manager
        
        engine = FileSearchEngine(profile_id=0)
        
        # 获取扫描目录
        scan_dirs = profile_manager.get_profile_scan_directories(0)
        print(f"扫描目录: {scan_dirs}")
        
        # 测试一些应该在桌面找到的查询
        test_queries = [
            "光与影",  # 应该在桌面找到
            "微信",    # 应该在开始菜单找到
            "MUN",     # 应该在桌面找到
        ]
        
        for query in test_queries:
            print(f"\n测试查询: '{query}'")
            
            # 默认搜索（限制目录）
            default_results = engine.search(query, limit=10)
            print(f"  默认搜索结果数: {len(default_results)}")
            
            for result in default_results:
                print(f"    {result.filename} - {result.filepath}")
            
            # 全局搜索
            global_results = engine.search(query, limit=10, global_search=True)
            print(f"  全局搜索结果数: {len(global_results)}")
            
            # 智能搜索
            smart_results = engine.search_with_fallback(query, limit=10)
            print(f"  智能搜索结果数: {len(smart_results)}")
            
            # 分析结果分布
            desktop_count = 0
            other_count = 0
            
            for result in smart_results:
                if any(result.filepath.startswith(scan_dir) for scan_dir in scan_dirs):
                    desktop_count += 1
                else:
                    other_count += 1
            
            print(f"  智能搜索 - 桌面文件: {desktop_count}, 其他文件: {other_count}")
        
        # 测试Everything SDK直接搜索桌面目录
        print(f"\n直接测试Everything SDK搜索桌面:")
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 构建桌面目录查询
        desktop_path = scan_dirs[0]  # C:\Users\<USER>\Desktop
        desktop_query = f'"{desktop_path}" 光与影'
        
        print(f"桌面查询: {desktop_query}")
        
        desktop_sdk_results = sdk.search(
            query=desktop_query,
            max_results=10,
            match_case=False,
            match_whole_word=False,
            use_regex=False
        )
        
        print(f"SDK桌面搜索结果数: {len(desktop_sdk_results)}")
        for result in desktop_sdk_results:
            print(f"  {result.filename} - {result.full_path}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_desktop_search()
