# -*- coding: utf-8 -*-
"""
调试英文搜索问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_english_search():
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        from simple_desktop.core.profile_manager import profile_manager
        
        engine = FileSearchEngine(profile_id=0)
        sdk = get_everything_sdk()
        
        query = "cur"
        print(f"调试查询: '{query}'")
        
        # 1. 检查扫描目录
        scan_dirs = profile_manager.get_profile_scan_directories(0)
        print(f"扫描目录: {scan_dirs}")
        
        # 2. 检查增强查询
        enhanced = engine._build_enhanced_query(query)
        print(f"增强查询: '{enhanced}'")
        
        # 3. 直接测试Everything SDK
        print(f"\nEverything SDK测试:")
        sdk_results = sdk.search(
            query=enhanced,
            max_results=20,
            match_case=False,
            match_whole_word=False,
            use_regex=False
        )
        print(f"SDK结果数: {len(sdk_results)}")
        
        for i, result in enumerate(sdk_results[:5]):
            print(f"  {i+1}. {result.filename} - {result.full_path}")
        
        # 4. 检查过滤器
        if len(sdk_results) > 0:
            print(f"\n检查过滤器:")
            
            # 转换为SearchResult
            from simple_desktop.search.models import SearchResult
            search_results = []
            for result in sdk_results:
                search_result = SearchResult.from_everything_result(result)
                search_results.append(search_result)
            
            print(f"转换后结果数: {len(search_results)}")
            
            # 应用过滤器
            from simple_desktop.search.filters import FilterPipeline, FilterCriteria
            criteria = FilterCriteria(
                file_types=None,
                include_folders=True,
                scan_directories=scan_dirs
            )
            
            filter_pipeline = FilterPipeline.from_criteria(criteria, engine.file_type_extensions)
            filtered_results = filter_pipeline.apply_filters(search_results)
            
            print(f"过滤后结果数: {len(filtered_results)}")
            
            if len(filtered_results) == 0 and len(search_results) > 0:
                print("所有结果都被过滤器排除了！")
                
                # 检查第一个结果为什么被排除
                first_result = search_results[0]
                print(f"检查第一个结果: {first_result.filename} - {first_result.filepath}")
                
                # 检查目录过滤
                from simple_desktop.search.filters import DirectoryFilter
                dir_filter = DirectoryFilter(scan_dirs)
                should_include = dir_filter.should_include(first_result)
                print(f"目录过滤器结果: {'通过' if should_include else '被排除'}")
                
                if not should_include:
                    result_path = os.path.normpath(first_result.filepath)
                    print(f"文件路径: {result_path}")
                    for scan_dir in scan_dirs:
                        scan_normalized = os.path.normpath(scan_dir)
                        matches = result_path.startswith(scan_normalized)
                        print(f"  与 {scan_normalized} 匹配: {'是' if matches else '否'}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_english_search()
