#!/usr/bin/env python3
"""
测试目录限制GUI功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_directory_gui():
    """测试目录限制GUI功能"""
    print("=== 测试目录限制GUI功能 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.core.profile_manager import profile_manager
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        print("✓ 搜索窗口创建成功")
        
        # 切换到Profile 1（应该没有配置目录）
        profile_manager.switch_profile(1)
        
        print("✓ 已切换到Profile 1")
        print("   这个Profile没有配置扫描目录")
        
        # 显示窗口
        search_window.show_window()
        
        print("✓ 搜索窗口已显示")
        print("\n📝 测试说明:")
        print("   1. 窗口应该显示Profile 1（标签1）")
        print("   2. 在搜索框输入任何内容应该显示'未配置扫描目录'提示")
        print("   3. 点击'目录'按钮可以添加扫描目录")
        print("   4. 添加目录后搜索功能应该正常工作")
        print("   5. 切换到Profile 0应该显示已配置的目录搜索结果")
        
        # 设置定时器，5秒后切换到Profile 0
        def switch_to_profile_0():
            print("\n🔄 自动切换到Profile 0...")
            profile_manager.switch_profile(0)
            print("   Profile 0有配置的扫描目录，搜索应该正常工作")
        
        QTimer.singleShot(5000, switch_to_profile_0)
        
        # 10秒后退出
        QTimer.singleShot(10000, app.quit)
        
        print("\n🚀 GUI测试窗口已启动！")
        print("   - 5秒后自动切换到Profile 0")
        print("   - 10秒后自动退出")
        print("   - 或按Ctrl+C手动退出")
        
        # 运行应用
        sys.exit(app.exec())
        
    except KeyboardInterrupt:
        print("\n   测试已退出")
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("Simple Desktop 目录限制GUI测试")
    print("=" * 40)
    
    test_directory_gui()

if __name__ == "__main__":
    main()
