"""
基于Everything SDK的搜索引擎
"""

import os
import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime

from ..core.everything_sdk import get_everything_sdk, SearchResult as EverythingResult
from .models import SearchResult
from .filters import FilterPipeline, FilterCriteria


class FileSearchEngine:
    """文件搜索引擎"""
    
    def __init__(self, profile_id: int = 0):
        """
        初始化搜索引擎
        
        Args:
            profile_id: Profile ID (0-9)
        """
        self.profile_id = profile_id
        self.everything_sdk = get_everything_sdk()
        
        # 文件类型映射
        self.file_type_extensions = {
            "documents": [".txt", ".doc", ".docx", ".pdf", ".rtf", ".odt", ".xls", ".xlsx", ".ppt", ".pptx"],
            "images": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg", ".ico", ".webp"],
            "videos": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp"],
            "audio": [".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a"],
            "archives": [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"],
            "executables": [".exe", ".msi", ".bat", ".cmd", ".com", ".scr", ".lnk", ".url"],
            "code": [".py", ".js", ".html", ".css", ".cpp", ".c", ".java", ".cs", ".php", ".rb", ".go"]
        }
    
    def search(self, query: str, limit: int = 50, file_types: Optional[List[str]] = None,
               include_folders: bool = True, global_search: bool = False) -> List[SearchResult]:
        """
        执行搜索 - 使用新的简化搜索方法

        Args:
            query: 搜索查询字符串
            limit: 最大结果数量
            file_types: 文件类型过滤列表
            include_folders: 是否包含文件夹
            global_search: 是否进行全局搜索（不限制扫描目录）

        Returns:
            搜索结果列表
        """
        if not query.strip():
            return []

        # 获取当前Profile的扫描目录
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)

        # 创建过滤条件
        # 默认限制在扫描目录内，除非明确要求全局搜索
        use_scan_dirs = None if global_search else scan_directories

        criteria = FilterCriteria(
            file_types=file_types,
            include_folders=include_folders,
            scan_directories=use_scan_dirs
        )

        return self._search_with_filters(query, criteria, limit)

    def search_with_fallback(self, query: str, limit: int = 50, file_types: Optional[List[str]] = None,
                           include_folders: bool = True) -> List[SearchResult]:
        """
        带回退机制的智能搜索：先在扫描目录内搜索，如果结果不足则扩展到全局搜索

        Args:
            query: 搜索查询字符串
            limit: 最大结果数量
            file_types: 文件类型过滤列表
            include_folders: 是否包含文件夹

        Returns:
            搜索结果列表
        """
        # 首先在扫描目录内搜索
        local_results = self.search(query, limit, file_types, include_folders, global_search=False)

        # 如果结果足够，直接返回
        if len(local_results) >= min(limit, 5):  # 至少5个结果或达到limit
            return local_results

        # 如果结果不足，进行全局搜索
        global_results = self.search(query, limit, file_types, include_folders, global_search=True)

        # 合并结果，优先显示本地结果
        seen_paths = set()
        combined_results = []

        # 先添加本地结果
        for result in local_results:
            if result.filepath not in seen_paths:
                combined_results.append(result)
                seen_paths.add(result.filepath)

        # 再添加全局结果（去重）
        for result in global_results:
            if result.filepath not in seen_paths and len(combined_results) < limit:
                combined_results.append(result)
                seen_paths.add(result.filepath)

        return combined_results[:limit]

    def _search_with_filters(self, query: str, criteria: FilterCriteria, limit: int) -> List[SearchResult]:
        """
        两阶段过滤搜索方法

        阶段1：向Everything SDK发送简化的原始关键词查询，获取所有匹配结果
        阶段2：在应用程序中对结果进行程序化过滤（目录、文件类型、扩展名等）

        Args:
            query: 用户原始搜索查询
            criteria: 过滤条件
            limit: 最大结果数量

        Returns:
            过滤后的搜索结果列表
        """
        import time
        start_time = time.time()

        try:
            # === 阶段1：初始搜索 ===
            # 构建简化查询，只包含原始关键词
            everything_query = self._build_optimized_query(query, criteria)

            if not everything_query.strip():
                return []

            # 使用动态搜索获取所有可能的结果
            stage1_start = time.time()
            try:
                # 根据查询复杂度决定安全限制
                max_safe_limit = self._calculate_safe_limit(query, criteria)

                print(f"Dynamic search: query='{everything_query}', max_safe_limit={max_safe_limit}")

                everything_results = self.everything_sdk.search_all_results(
                    query=everything_query,
                    match_case=False,
                    match_whole_word=False,
                    use_regex=False,
                    max_safe_limit=max_safe_limit
                )
            except Exception as e:
                print(f"Everything SDK dynamic search error: {e}")
                return []

            stage1_time = time.time() - stage1_start

            if not everything_results:
                return []

            # === 阶段2：后处理过滤 ===
            stage2_start = time.time()

            # 转换为结构化数据格式
            raw_results = []
            conversion_errors = 0

            for result in everything_results:
                try:
                    search_result = SearchResult.from_everything_result(result)
                    raw_results.append(search_result)
                except Exception as e:
                    conversion_errors += 1
                    if conversion_errors <= 5:  # 只记录前5个错误，避免日志过多
                        print(f"Result conversion error: {e}")

            if conversion_errors > 5:
                print(f"Total conversion errors: {conversion_errors}")

            # 应用所有程序化过滤器
            try:
                filter_pipeline = FilterPipeline.from_criteria(criteria, self.file_type_extensions)
                filtered_results = filter_pipeline.apply_filters(raw_results, max_results=limit)
            except Exception as e:
                print(f"Filter pipeline error: {e}")
                # 如果过滤失败，返回原始结果的截断版本
                return raw_results[:limit]

            stage2_time = time.time() - stage2_start
            total_time = time.time() - start_time

            # 性能监控（仅在调试模式下输出）
            if total_time > 1.0:  # 只记录耗时超过1秒的搜索
                print(f"Search performance: total={total_time:.2f}s, "
                      f"stage1={stage1_time:.2f}s, stage2={stage2_time:.2f}s, "
                      f"raw_results={len(raw_results)}, filtered_results={len(filtered_results)}")

            return filtered_results

        except Exception as e:
            print(f"Two-stage search error: {e}")
            return []

    def _build_optimized_query(self, query: str, criteria: FilterCriteria) -> str:
        """
        构建简化的Everything查询 - 两阶段过滤方法的第一阶段
        只发送原始关键词到Everything SDK，所有复杂过滤在后处理阶段完成
        """
        # 提取基础查询词，移除所有复杂语法
        simple_query = self._extract_simple_query(query)

        # 如果查询为空，返回空字符串
        if not simple_query.strip():
            return ""

        # 只返回简单的关键词，不添加任何复杂语法
        # 这样可以避免查询长度限制，并获得最大的结果集用于后续过滤
        return simple_query

    def _calculate_search_multiplier(self, criteria: FilterCriteria) -> int:
        """
        根据过滤条件计算搜索倍数
        过滤条件越多，需要获取的原始结果越多，以确保过滤后有足够的结果
        大幅增加倍数以获取更多结果
        """
        multiplier = 10  # 大幅增加基础倍数

        # 目录过滤：需要更多结果，因为可能大部分结果不在指定目录中
        if criteria.scan_directories:
            multiplier += 10

        # 文件类型过滤：根据类型数量调整
        if criteria.file_types:
            multiplier += 8

        # 扩展名过滤：需要更多结果
        if criteria.extensions:
            multiplier += 8

        # 日期过滤：可能过滤掉很多结果
        if criteria.date_range_days:
            multiplier += 6

        # 大小过滤：通常过滤掉的结果较少
        if criteria.min_size_mb or criteria.max_size_mb:
            multiplier += 4

        return min(multiplier, 50)  # 大幅增加最大倍数限制

    def _build_enhanced_query(self, query: str) -> str:
        """
        构建增强的查询，支持更好的文件名和路径匹配，特别优化中文支持
        """
        # 先提取简单查询
        simple_query = self._extract_simple_query(query)

        if not simple_query.strip():
            return simple_query

        # 对于单个关键词，使用多种匹配模式
        keywords = simple_query.split()

        if len(keywords) == 1:
            keyword = keywords[0]

            # 检查是否包含中文字符
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in keyword)
            # 检查是否是纯数字
            is_numeric = keyword.isdigit()

            if has_chinese:
                # 中文查询：使用通配符包围以支持部分匹配
                return f"{keyword} | *{keyword}*"
            elif is_numeric:
                # 纯数字查询：使用通配符包围，因为Everything对数字搜索有特殊处理
                return f"*{keyword}*"
            else:
                # 英文查询：根据长度决定匹配策略
                if len(keyword) <= 3:
                    # 短词（3个字符及以下）：使用包含匹配，支持模糊搜索
                    return f"*{keyword}*"
                else:
                    # 长词：使用前缀匹配和包含匹配的组合
                    return f"{keyword}* | *{keyword}*"
        else:
            # 多个关键词时，保持原样
            return simple_query

    def _extract_simple_query(self, query: str) -> str:
        """
        从用户查询中提取简单的Everything查询
        移除复杂的过滤语法，只保留基本的搜索关键词
        """
        # 移除Everything特殊语法，只保留基本搜索词
        simple_query = query

        # 移除扩展名过滤语法
        simple_query = re.sub(r'\s+ext:[^\s]+', '', simple_query)
        simple_query = re.sub(r'\s+suffix:[^\s]+', '', simple_query)

        # 移除大小过滤语法
        simple_query = re.sub(r'\s+size:[^\s]+', '', simple_query)

        # 移除日期过滤语法
        simple_query = re.sub(r'\s+dm:[^\s]+', '', simple_query)
        simple_query = re.sub(r'\s+dc:[^\s]+', '', simple_query)

        # 移除文件夹过滤语法
        simple_query = re.sub(r'\s*folder:\s*', ' ', simple_query)
        simple_query = re.sub(r'^folder:\s*', '', simple_query)

        # 移除路径过滤语法（引号包围的路径）
        simple_query = re.sub(r'"[^"]*"', '', simple_query)

        # 清理多余的空格
        simple_query = ' '.join(simple_query.split())

        return simple_query.strip()

    def search_legacy(self, query: str, limit: int = 50, file_types: Optional[List[str]] = None,
               include_folders: bool = True) -> List[SearchResult]:
        """
        原有的搜索方法（保留用于兼容性）

        Args:
            query: 搜索查询字符串
            limit: 最大结果数量
            file_types: 文件类型过滤列表
            include_folders: 是否包含文件夹

        Returns:
            搜索结果列表
        """
        if not query.strip():
            return []

        # 获取当前Profile的扫描目录
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)

        try:
            # 根据是否配置了扫描目录选择搜索策略
            if scan_directories:
                use_directory_filter = True
            else:
                use_directory_filter = False
                scan_directories = []

            # 处理文件类型过滤的特殊情况
            if file_types and any(ft != "folders" for ft in file_types):
                # 有文件类型过滤，需要为每个扩展名单独搜索
                results = self._search_with_file_types(
                    query, scan_directories, file_types, include_folders,
                    limit, use_directory_filter
                )
            else:
                # 没有文件类型过滤或只有文件夹过滤，使用标准搜索
                if scan_directories:
                    everything_query = self._build_everything_query_with_directories(
                        query, scan_directories, file_types, include_folders
                    )
                else:
                    everything_query = self._build_everything_query(query, file_types, include_folders)

                # 执行Everything搜索
                search_limit = min(limit * 20, 3000) if use_directory_filter else min(limit * 10, 2000)
                everything_results = self.everything_sdk.search(
                    query=everything_query,
                    max_results=search_limit,
                    match_case=False,
                    match_whole_word=False,
                    use_regex=False
                )

                # 转换结果格式
                results = self._process_search_results(
                    everything_results, scan_directories, file_types,
                    include_folders, use_directory_filter, limit
                )

            return results

        except Exception as e:
            print(f"Search error: {e}")
            return []

    def _search_with_file_types(self, query: str, scan_directories: List[str],
                               file_types: List[str], include_folders: bool,
                               limit: int, use_directory_filter: bool) -> List[SearchResult]:
        """为每个文件类型单独搜索并合并结果"""
        all_results = []
        seen_paths = set()

        # 获取所有需要搜索的扩展名
        file_extensions = []
        for file_type in file_types:
            if file_type != "folders" and file_type in self.file_type_extensions:
                file_extensions.extend(self.file_type_extensions[file_type])

        # 为每个扩展名单独搜索
        for ext in file_extensions:
            ext_name = ext[1:]  # 去掉点号

            # 构建查询
            if scan_directories:
                # 使用简单查询 + 后续路径过滤
                ext_query = f"{query} ext:{ext_name}"
            else:
                ext_query = f"{query} ext:{ext_name}"

            try:
                # 执行搜索
                search_limit = min(limit * 10, 2000)
                everything_results = self.everything_sdk.search(
                    query=ext_query,
                    max_results=search_limit,
                    match_case=False,
                    match_whole_word=False,
                    use_regex=False
                )

                # 处理结果
                for result in everything_results:
                    # 避免重复结果
                    if result.full_path in seen_paths:
                        continue
                    seen_paths.add(result.full_path)

                    search_result = SearchResult.from_everything_result(result)

                    # 应用目录过滤
                    should_include_by_directory = True
                    if use_directory_filter:
                        should_include_by_directory = self._is_in_scan_directories(
                            search_result.filepath, scan_directories
                        )

                    if should_include_by_directory:
                        # 应用文件类型过滤
                        should_include_by_type = self._should_include_result(
                            search_result, file_types, include_folders
                        )

                        if should_include_by_type:
                            all_results.append(search_result)

                        # 限制结果数量
                        if len(all_results) >= limit:
                            break

                # 如果已经达到限制，停止搜索其他扩展名
                if len(all_results) >= limit:
                    break

            except Exception as e:
                print(f"Error searching for extension {ext}: {e}")
                continue

        return all_results

    def _process_search_results(self, everything_results, scan_directories: List[str],
                               file_types: Optional[List[str]], include_folders: bool,
                               use_directory_filter: bool, limit: int) -> List[SearchResult]:
        """处理搜索结果"""
        results = []
        for result in everything_results:
            search_result = SearchResult.from_everything_result(result)

            # 根据搜索策略决定是否需要目录过滤
            should_include_by_directory = True
            if use_directory_filter:
                should_include_by_directory = self._is_in_scan_directories(
                    search_result.filepath, scan_directories
                )

            if should_include_by_directory:
                # 应用文件类型过滤
                should_include_by_type = self._should_include_result(
                    search_result, file_types, include_folders
                )

                if should_include_by_type:
                    results.append(search_result)

                # 限制结果数量
                if len(results) >= limit:
                    break

        return results
    
    def _build_everything_query_with_directories(self, query: str, scan_directories: List[str],
                                                file_types: Optional[List[str]] = None,
                                                include_folders: bool = True) -> str:
        """构建包含目录限制的Everything查询字符串"""
        # 基础查询
        base_query = query

        # 处理后缀过滤
        if "suffix:" in query or "ext:" in query:
            # 查询中已包含后缀过滤，直接使用目录限制
            directory_filters = []
            for directory in scan_directories:
                normalized_path = os.path.normpath(directory)
                if not normalized_path.endswith("\\"):
                    normalized_path += "\\"
                directory_filters.append(f'"{normalized_path}"')

            if len(directory_filters) == 1:
                directory_query = directory_filters[0]
            else:
                directory_query = "(" + " | ".join(directory_filters) + ")"

            return f"{directory_query} {base_query}"

        # 构建查询组件
        query_parts = []

        # 处理文件类型过滤
        if file_types:
            # 有指定文件类型过滤

            # 处理文件夹
            if "folders" in file_types and include_folders:
                # 为每个目录构建folder查询
                folder_queries = []
                for directory in scan_directories:
                    normalized_path = os.path.normpath(directory)
                    if not normalized_path.endswith("\\"):
                        normalized_path += "\\"
                    folder_queries.append(f'"{normalized_path}" folder:{base_query}')

                if folder_queries:
                    # 修复：不要在OR查询外面加括号
                    query_parts.append(" | ".join(folder_queries))

            # 处理文件类型
            file_extensions = []
            for file_type in file_types:
                if file_type != "folders" and file_type in self.file_type_extensions:
                    file_extensions.extend(self.file_type_extensions[file_type])

            if file_extensions:
                # 修复：Everything不支持复杂的扩展名OR语法
                # 为每个扩展名单独构建查询，然后在应用层合并结果
                # 这里只使用第一个扩展名，其他扩展名的结果将通过多次搜索获得
                first_ext = file_extensions[0][1:]  # 去掉点号
                file_query = f"{base_query} ext:{first_ext}"
                query_parts.append(file_query)

            # 组合所有查询部分
            if query_parts:
                if len(query_parts) == 1:
                    return query_parts[0]
                else:
                    # 修复：不要在最外层加括号
                    return " | ".join(query_parts)
            else:
                # 没有匹配的类型，返回空查询
                return f'"{scan_directories[0]}\\" nonexistent_placeholder_query'
        else:
            # 没有指定文件类型过滤，搜索所有内容（包括文件夹）
            # 优化：使用简单的查询语法，路径过滤在后续处理
            return base_query

    def _build_everything_query(self, query: str, file_types: Optional[List[str]] = None,
                               include_folders: bool = True) -> str:
        """构建Everything查询字符串（保留原方法以兼容性）"""
        # 基础查询
        everything_query = query

        # 处理后缀过滤
        if "suffix:" in query:
            # 查询中已包含后缀过滤，直接使用
            return everything_query

        # 添加文件类型过滤
        if file_types:
            type_filters = []

            # 处理文件夹
            if "folders" in file_types and include_folders:
                type_filters.append("folder:")

            # 处理文件类型
            file_extensions = []
            for file_type in file_types:
                if file_type != "folders" and file_type in self.file_type_extensions:
                    file_extensions.extend(self.file_type_extensions[file_type])

            if file_extensions:
                # 构建扩展名过滤
                ext_filter = " | ".join([f"ext:{ext[1:]}" for ext in file_extensions])
                if ext_filter:
                    type_filters.append(f"({ext_filter})")

            if type_filters:
                everything_query += " " + " | ".join(type_filters)

        return everything_query

    def _is_in_scan_directories(self, filepath: str, scan_directories: List[str]) -> bool:
        """检查文件路径是否在配置的扫描目录中"""
        if not filepath or not scan_directories:
            return False

        # 规范化文件路径
        normalized_filepath = os.path.normpath(filepath).lower()

        # 检查是否在任何一个扫描目录中
        for directory in scan_directories:
            normalized_directory = os.path.normpath(directory).lower()

            # 确保目录路径以分隔符结尾
            if not normalized_directory.endswith(os.sep):
                normalized_directory += os.sep

            # 检查文件路径是否以目录路径开头
            if normalized_filepath.startswith(normalized_directory):
                return True

            # 也检查完全匹配的情况（文件就在目录根目录下）
            if os.path.dirname(normalized_filepath + os.sep) == normalized_directory.rstrip(os.sep):
                return True

        return False
    
    def _should_include_result(self, result: SearchResult, file_types: Optional[List[str]] = None,
                              include_folders: bool = True) -> bool:
        """判断是否应该包含此结果"""
        # 检查文件夹
        if result.item_type == "folder":
            if not include_folders:
                return False

            # 如果没有文件类型过滤，包含所有文件夹
            if not file_types:
                return True

            # 如果有文件类型过滤，只有明确包含"folders"时才包含文件夹
            return "folders" in file_types

        # 检查文件类型
        if file_types:
            # 有文件类型过滤
            result_ext = result.suffix.lower()

            # 检查是否匹配任何选中的文件类型
            for file_type in file_types:
                if file_type != "folders" and file_type in self.file_type_extensions:
                    if result_ext in self.file_type_extensions[file_type]:
                        return True

            # 如果没有匹配任何文件类型，返回False
            return False

        # 没有文件类型过滤，包含所有文件
        return True
    
    def search_with_suffix(self, query: str, suffix: str, limit: int = 50) -> List[SearchResult]:
        """
        带后缀过滤的搜索 - 使用新的过滤方法

        Args:
            query: 搜索查询字符串
            suffix: 文件后缀（如 .txt）
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        if not suffix.startswith("."):
            suffix = f".{suffix}"

        # 获取当前Profile的扫描目录
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)

        # 创建过滤条件，包含扩展名过滤
        criteria = FilterCriteria(
            include_folders=False,
            scan_directories=scan_directories,
            extensions={suffix}
        )

        return self._search_with_filters(query, criteria, limit)

    def search_in_directory(self, query: str, directory: str, limit: int = 50) -> List[SearchResult]:
        """
        在指定目录中搜索（已更新为使用配置的扫描目录）

        Args:
            query: 搜索查询字符串
            directory: 目录路径（此参数现在被忽略，使用Profile配置的目录）
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        # 注意：此方法现在使用Profile配置的扫描目录，而不是传入的directory参数
        print("注意：search_in_directory现在使用Profile配置的扫描目录")
        return self.search(query, limit=limit)
    
    def get_file_type_extensions(self, file_type: str) -> List[str]:
        """获取文件类型对应的扩展名列表"""
        return self.file_type_extensions.get(file_type, [])
    
    def get_all_file_types(self) -> List[str]:
        """获取所有支持的文件类型"""
        return list(self.file_type_extensions.keys()) + ["folders"]

    def has_scan_directories(self) -> bool:
        """检查当前Profile是否配置了扫描目录"""
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)
        return len(scan_directories) > 0

    def get_scan_directories(self) -> List[str]:
        """获取当前Profile的扫描目录列表"""
        from ..core.profile_manager import profile_manager
        return profile_manager.get_profile_scan_directories(self.profile_id)

    def get_scan_directories_info(self) -> Dict[str, Any]:
        """获取扫描目录的详细信息"""
        scan_directories = self.get_scan_directories()
        info = {
            "count": len(scan_directories),
            "directories": scan_directories,
            "valid_directories": [],
            "invalid_directories": []
        }

        for directory in scan_directories:
            if os.path.exists(directory) and os.path.isdir(directory):
                info["valid_directories"].append(directory)
            else:
                info["invalid_directories"].append(directory)

        return info

    def is_everything_available(self) -> bool:
        """检查Everything是否可用"""
        return self.everything_sdk.is_everything_running()

    def get_everything_version(self) -> Dict[str, Any]:
        """获取Everything版本信息"""
        return self.everything_sdk.get_version_info()

    def search_in_directory(self, query: str, directory: str, limit: int = 50) -> List[SearchResult]:
        """
        在指定目录中搜索

        Args:
            query: 搜索查询字符串
            directory: 目录路径
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        # 构建目录限制的查询
        dir_query = f'"{directory}" {query}'
        return self.search(dir_query, limit=limit)

    def search_by_extension(self, extension: str, limit: int = 50) -> List[SearchResult]:
        """
        按扩展名搜索 - 使用新的过滤方法

        Args:
            extension: 文件扩展名（如 .txt）
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        if not extension.startswith("."):
            extension = f".{extension}"

        # 获取当前Profile的扫描目录
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)

        # 创建过滤条件，只搜索指定扩展名的文件
        criteria = FilterCriteria(
            include_folders=False,
            scan_directories=scan_directories,
            extensions={extension}
        )

        # 使用空查询搜索所有文件，然后通过扩展名过滤
        return self._search_with_filters("", criteria, limit)

    def search_recent_files(self, days: int = 7, limit: int = 50) -> List[SearchResult]:
        """
        搜索最近修改的文件 - 使用新的过滤方法

        Args:
            days: 天数
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        # 获取当前Profile的扫描目录
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)

        # 创建过滤条件，包含日期范围过滤
        criteria = FilterCriteria(
            include_folders=False,
            scan_directories=scan_directories,
            date_range_days=days
        )

        # 使用空查询搜索所有文件，然后通过日期过滤
        return self._search_with_filters("", criteria, limit)

    def search_large_files(self, min_size_mb: int = 100, limit: int = 50) -> List[SearchResult]:
        """
        搜索大文件 - 使用新的过滤方法

        Args:
            min_size_mb: 最小文件大小（MB）
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        # 获取当前Profile的扫描目录
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)

        # 创建过滤条件，包含大小过滤
        criteria = FilterCriteria(
            include_folders=False,
            scan_directories=scan_directories,
            min_size_mb=min_size_mb
        )

        # 使用空查询搜索所有文件，然后通过大小过滤
        return self._search_with_filters("", criteria, limit)

    def search_advanced(self, query: str = "", file_types: Optional[List[str]] = None,
                       include_folders: bool = True, date_range_days: Optional[int] = None,
                       min_size_mb: Optional[int] = None, max_size_mb: Optional[int] = None,
                       extensions: Optional[List[str]] = None, limit: int = 50) -> List[SearchResult]:
        """
        高级搜索方法，支持多种过滤条件组合

        Args:
            query: 搜索查询字符串
            file_types: 文件类型过滤列表
            include_folders: 是否包含文件夹
            date_range_days: 最近N天的文件
            min_size_mb: 最小文件大小（MB）
            max_size_mb: 最大文件大小（MB）
            extensions: 具体的文件扩展名列表
            limit: 最大结果数量

        Returns:
            搜索结果列表
        """
        # 获取当前Profile的扫描目录
        from ..core.profile_manager import profile_manager
        scan_directories = profile_manager.get_profile_scan_directories(self.profile_id)

        # 处理扩展名列表
        extension_set = None
        if extensions:
            extension_set = set()
            for ext in extensions:
                if not ext.startswith("."):
                    ext = f".{ext}"
                extension_set.add(ext)

        # 创建综合过滤条件
        criteria = FilterCriteria(
            file_types=file_types,
            include_folders=include_folders,
            scan_directories=scan_directories,
            date_range_days=date_range_days,
            min_size_mb=min_size_mb,
            max_size_mb=max_size_mb,
            extensions=extension_set
        )

        return self._search_with_filters(query, criteria, limit)

    def _calculate_safe_limit(self, query: str, criteria: FilterCriteria) -> int:
        """
        根据查询复杂度和过滤条件计算安全的结果数量限制

        这个方法用于防止系统过载，同时确保获取足够多的结果
        """
        # 基础安全限制
        base_limit = 10000

        # 根据查询长度调整
        query_length = len(query.strip())
        if query_length <= 2:
            # 非常短的查询可能返回大量结果，需要更保守的限制
            base_limit = 5000
        elif query_length <= 5:
            # 短查询
            base_limit = 15000
        else:
            # 长查询通常返回较少结果
            base_limit = 25000

        # 根据过滤条件调整
        if criteria.scan_directories:
            # 有目录限制时，可以允许更多结果，因为会被目录过滤器过滤
            base_limit = min(base_limit * 2, 30000)

        if criteria.extensions or criteria.file_types:
            # 有文件类型或扩展名过滤时，可以允许更多结果
            base_limit = min(base_limit * 1.5, 25000)

        # 检查是否是通配符查询
        if '*' in query or '?' in query:
            # 通配符查询可能返回大量结果，需要更保守
            base_limit = min(base_limit, 8000)

        return int(base_limit)
