"""对话框和弹出窗口模块"""

from PySide6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QCheckBox,
    QLabel,
    QPushButton,
    QGroupBox,
    QScrollArea,
    QWidget,
    QFileDialog,
    QComboBox,
    QButtonGroup,
    QRadioButton,
    QSpacerItem,
    QSizePolicy,
    QProgressDialog,
    QMessageBox,
)
from PySide6.QtCore import Qt, Signal, QThread, QSize
from PySide6.QtGui import QFont

from src.core.config import config_manager

# 后续新增的后台扫描线程


class IndexWorker(QThread):
    """后台索引构建线程"""

    progress = Signal(int)  # 已扫描文件数
    finished = Signal(dict)  # 构建结果

    def __init__(self, directory: str):
        super().__init__()
        self.directory = directory

    def run(self):
        # 在后台执行文件扫描
        from src.indexer.scanner import FileScanner  # 延迟导入避免循环

        scanner = FileScanner()

        def callback(count: int):
            self.progress.emit(count)

        result = scanner.build_index(self.directory, progress_callback=callback)
        self.finished.emit(result)


class FileTypeFilterDialog(QDialog):
    """文件类型筛选对话框"""

    # 自定义信号，传递选中的文件类型
    filter_changed = Signal(list)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("文件类型筛选")
        self.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 设置固定大小
        self.setFixedSize(280, 320)

        # 获取当前文件类型配置
        self.file_types = config_manager.get_file_types()
        self.selected_categories = []  # 默认全不选

        self.init_ui()
        self.setup_style()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 标题
        title_label = QLabel("选择文件类型")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(8)

        # 文件类型复选框
        self.checkboxes = {}

        # 定义类型显示名称
        type_names = {
            "folders": "📁 文件夹",
            "documents": "📄 文档",
            "images": "🖼️ 图片",
            "videos": "🎬 视频",
            "audios": "🎵 音频",
            "executables": "⚙️ 可执行文件",
            "archives": "📦 压缩包",
            "code": "💻 代码文件",
            "shortcuts": "🔗 快捷方式",
        }

        for category, extensions in self.file_types.items():
            # 创建复选框
            checkbox = QCheckBox(type_names.get(category, category.capitalize()))
            checkbox.setChecked(False)  # 默认全不选
            checkbox.setFont(QFont("Microsoft YaHei", 10))

            # 添加扩展名提示
            if category == "folders":
                tooltip = f"{type_names.get(category, category)}\n显示文件夹类型的搜索结果"
            else:
                ext_text = ", ".join(extensions[:5])  # 显示前5个扩展名
                if len(extensions) > 5:
                    ext_text += f" 等 {len(extensions)} 种"
                tooltip = f"{type_names.get(category, category)}\n包含: {ext_text}"
            
            checkbox.setToolTip(tooltip)

            checkbox.stateChanged.connect(self.on_checkbox_changed)
            self.checkboxes[category] = checkbox
            scroll_layout.addWidget(checkbox)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(180)
        layout.addWidget(scroll_area)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 全选/全不选按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all)

        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(self.select_none)

        # 确定/取消按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(select_none_btn)
        button_layout.addStretch()
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(
            """
            QDialog {
                background-color: rgba(255, 255, 255, 240);
                border-radius: 8px;
                border: 1px solid rgba(200, 200, 200, 150);
            }

            QLabel {
                color: #333333;
                background: transparent;
            }

            QCheckBox {
                color: #333333;
                background: transparent;
                spacing: 8px;
                padding: 4px;
            }

            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid rgba(150, 150, 150, 150);
                background-color: rgba(255, 255, 255, 200);
            }

            QCheckBox::indicator:checked {
                background-color: rgba(0, 120, 215, 180);
                border-color: rgba(0, 120, 215, 200);
            }

            QCheckBox::indicator:hover {
                border-color: rgba(0, 120, 215, 150);
            }

            QPushButton {
                background-color: rgba(240, 240, 240, 200);
                border: 1px solid rgba(200, 200, 200, 150);
                border-radius: 4px;
                color: #333333;
                padding: 6px 12px;
                font-size: 10px;
            }

            QPushButton:hover {
                background-color: rgba(230, 230, 230, 200);
            }

            QPushButton:pressed {
                background-color: rgba(210, 210, 210, 200);
            }

            QScrollArea {
                background: transparent;
                border: 1px solid rgba(200, 200, 200, 150);
                border-radius: 4px;
            }

            QScrollBar:vertical {
                background: rgba(230, 230, 230, 150);
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background: rgba(180, 180, 180, 150);
                border-radius: 6px;
                min-height: 20px;
            }
        """
        )

    def on_checkbox_changed(self):
        """复选框状态改变"""
        self.selected_categories = [
            category
            for category, checkbox in self.checkboxes.items()
            if checkbox.isChecked()
        ]
        # 实时发送信号
        self.filter_changed.emit(self.selected_categories)

    def select_all(self):
        """全选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(True)

    def select_none(self):
        """全不选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)

    def get_selected_categories(self):
        """获取选中的类别"""
        return self.selected_categories


class PathSelectionDialog(QDialog):
    """路径选择对话框"""

    # 信号：路径被添加
    path_added = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("管理扫描目录")
        self.setFixedSize(500, 400)
        self.setWindowFlags(Qt.WindowType.Dialog)

        self.init_ui()
        self.load_current_paths()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("扫描目录管理")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 当前目录列表
        current_group = QGroupBox("当前扫描目录")
        current_layout = QVBoxLayout()

        self.path_list_widget = QWidget()
        self.path_list_layout = QVBoxLayout(self.path_list_widget)

        scroll_area = QScrollArea()
        scroll_area.setWidget(self.path_list_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(200)

        current_layout.addWidget(scroll_area)
        current_group.setLayout(current_layout)
        layout.addWidget(current_group)

        # 操作按钮
        button_layout = QHBoxLayout()

        add_btn = QPushButton("添加目录")
        add_btn.clicked.connect(self.add_directory)

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)

        button_layout.addWidget(add_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_current_paths(self):
        """加载当前扫描路径"""
        # 清空现有项目
        while self.path_list_layout.count():
            child = self.path_list_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 添加当前路径
        current_paths = config_manager.get_scan_directories()

        if not current_paths:
            no_path_label = QLabel("暂无扫描目录，请添加目录以启用文件监听")
            no_path_label.setStyleSheet("color: gray; font-style: italic;")
            self.path_list_layout.addWidget(no_path_label)
        else:
            for path in current_paths:
                self.add_path_item(path)

        # 添加弹性空间
        self.path_list_layout.addStretch()

    def add_path_item(self, path: str):
        """添加路径项目"""
        item_widget = QWidget()
        item_layout = QHBoxLayout(item_widget)
        item_layout.setContentsMargins(5, 5, 5, 5)

        # 路径标签
        path_label = QLabel(path)
        path_label.setWordWrap(True)

        # 删除按钮
        remove_btn = QPushButton("删除")
        remove_btn.setMaximumWidth(60)
        remove_btn.clicked.connect(lambda: self.remove_directory(path))

        item_layout.addWidget(path_label, 1)
        item_layout.addWidget(remove_btn)

        # 设置样式
        item_widget.setStyleSheet(
            """
            QWidget {
                background-color: rgba(240, 240, 240, 20);
                border: 1px solid rgba(200, 200, 200, 100);
                border-radius: 4px;
                margin: 2px;
            }
        """
        )

        self.path_list_layout.addWidget(item_widget)

    def add_directory(self):
        """添加目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择要扫描的目录", "", QFileDialog.Option.ShowDirsOnly
        )

        if directory:
            success = config_manager.add_scan_directory(directory)
            if success:
                # 显示进度对话框
                progress_dialog = QProgressDialog("正在扫描目录，请稍候...", None, 0, 0, self)
                progress_dialog.setWindowTitle("构建索引")
                progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                progress_dialog.setCancelButton(None)
                progress_dialog.show()

                # 启动后台线程
                worker = IndexWorker(directory)

                # 把进度条对象和线程保存到实例，防止被垃圾回收
                self._current_worker = worker
                self._progress_dialog = progress_dialog

                # 连接信号
                worker.progress.connect(
                    lambda c: progress_dialog.setLabelText(f"已扫描 {c} 个文件...")
                )

                def on_finished(result: dict):
                    progress_dialog.close()
                    self._current_worker = None
                    self._progress_dialog = None
                    # 通知外部路径已添加并完成索引
                    self.path_added.emit(directory)
                    self.load_current_paths()

                worker.finished.connect(on_finished)

                worker.start()
            else:
                # 可以添加错误提示
                pass

    def remove_directory(self, path: str):
        """删除目录"""
        # 确认删除操作
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除扫描目录吗？\n\n目录: {path}\n\n此操作将从索引中删除该目录下的所有文件记录，且无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            success = config_manager.remove_scan_directory(path)
            if success:
                self.load_current_paths()
                # 显示成功消息
                QMessageBox.information(
                    self, "删除成功", f"目录已成功移除：\n{path}\n\n相关的文件记录也已从索引中清理。"
                )


class DefaultActionDialog(QDialog):
    """默认行为设置对话框"""

    # 信号：设置改变
    action_changed = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("默认行为设置")
        self.setFixedSize(350, 200)

        self.init_ui()
        self.load_current_setting()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setSpacing(20)

        # 标题
        title_label = QLabel("双击搜索结果时的默认行为")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 选项组
        options_group = QGroupBox("选择默认操作")
        options_layout = QVBoxLayout()

        self.button_group = QButtonGroup()

        # 打开文件选项
        self.open_file_radio = QRadioButton("🗎 打开文件")
        self.open_file_radio.setToolTip("直接打开文件（如果有关联程序）")

        # 打开目录选项
        self.open_dir_radio = QRadioButton("📁 打开文件所在目录")
        self.open_dir_radio.setToolTip("打开文件所在的文件夹")

        self.button_group.addButton(self.open_file_radio, 0)
        self.button_group.addButton(self.open_dir_radio, 1)

        options_layout.addWidget(self.open_file_radio)
        options_layout.addWidget(self.open_dir_radio)
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # 按钮
        button_layout = QHBoxLayout()

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.apply_setting)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_current_setting(self):
        """加载当前设置"""
        current_action = config_manager.get_default_action()

        if current_action == "open_file":
            self.open_file_radio.setChecked(True)
        else:
            self.open_dir_radio.setChecked(True)

    def apply_setting(self):
        """应用设置"""
        if self.open_file_radio.isChecked():
            action = "open_file"
        else:
            action = "open_directory"

        success = config_manager.set_default_action(action)
        if success:
            self.action_changed.emit(action)
            self.accept()
        else:
            self.reject()


class HelpDialog(QDialog):
    """操作方式和快捷键帮助对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("操作方式和快捷键")
        self.setFixedSize(480, 520)
        self.setWindowFlags(Qt.WindowType.Dialog)
        
        self.init_ui()
        self.setup_style()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("CleanTable 操作指南")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(12)

        # 快捷键部分
        self.add_section(scroll_layout, "🔧 全局快捷键", [
            ("双击 Ctrl", "显示/隐藏搜索窗口"),
            ("Ctrl + 0", "切换到默认桌面标签"),
            ("Ctrl + 1-9", "切换到对应标签"),
            ("Esc", "退出程序")
        ])

        # 搜索操作
        self.add_section(scroll_layout, "🔍 搜索操作", [
            ("输入文字", "实时搜索文件"),
            ("后缀框", "按文件扩展名筛选（如.txt）"),
            ("Enter", "打开选中的文件/文件夹"),
            ("双击项目", "打开选中的文件/文件夹")
        ])

        # 界面操作
        self.add_section(scroll_layout, "⚙️ 界面操作", [
            ("操作方式按钮", "显示本帮助界面"),
            ("类型按钮", "筛选文件类型（文档、图片等）"),
            ("目录按钮", "管理当前标签的扫描目录"),
            ("打开文件/文件夹", "切换默认打开行为")
        ])

        # 标签管理
        self.add_section(scroll_layout, "📁 标签管理", [
            ("标签0", "默认桌面 - 自动索引常用目录"),
            ("标签1-9", "自定义标签 - 手动添加目录"),
            ("点击标签", "切换到对应的搜索范围"),
            ("延迟加载", "仅在首次访问时构建索引")
        ])

        # 使用技巧
        self.add_section(scroll_layout, "💡 使用技巧", [
            ("搜索为空", "首次使用需添加扫描目录"),
            ("结果过多", "使用后缀筛选或类型筛选"),
            ("快速切换", "使用Ctrl+数字快速切换标签"),
            ("文件夹筛选", "在类型筛选中选择文件夹")
        ])

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # 关闭按钮
        close_btn = QPushButton("知道了")
        close_btn.clicked.connect(self.accept)
        close_btn.setFixedHeight(35)
        close_btn.setFont(QFont("Microsoft YaHei", 11))
        layout.addWidget(close_btn)

        self.setLayout(layout)

    def add_section(self, layout, title, items):
        """添加一个帮助部分"""
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-top: 8px;")
        layout.addWidget(title_label)

        # 项目列表
        for key, description in items:
            item_layout = QHBoxLayout()
            item_layout.setContentsMargins(20, 2, 0, 2)
            
            # 快捷键/操作
            key_label = QLabel(key)
            key_label.setFont(QFont("Consolas", 10, QFont.Weight.Bold))
            key_label.setStyleSheet("""
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 4px 8px;
                color: #2c3e50;
            """)
            key_label.setFixedWidth(120)
            
            # 描述
            desc_label = QLabel(description)
            desc_label.setFont(QFont("Microsoft YaHei", 10))
            desc_label.setStyleSheet("color: #34495e; margin-left: 10px;")
            
            item_layout.addWidget(key_label)
            item_layout.addWidget(desc_label)
            item_layout.addStretch()
            
            layout.addLayout(item_layout)

    def setup_style(self):
        """设置对话框样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border: 1px solid #ddd;
                border-radius: 8px;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
