; SimpleDesktop.iss

[Setup]
AppName=Simple Desktop
AppVersion=1.0.0
AppPublisher=Simple Desktop
DefaultDirName={pf}\Simple Desktop
DefaultGroupName=Simple Desktop
UninstallDisplayIcon={app}\SimpleDesktop.exe
Compression=lzma
SolidCompression=yes
OutputBaseFilename=SimpleDesktopSetup
SetupIconFile=ic.ico
OutputDir=SimpleDesktop
AllowNoIcons=yes
DisableProgramGroupPage=no
CreateAppDir=yes



[Files]
Source: "SimpleDesktop.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "Everything64.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "install.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "ic.ico"; DestDir: "{app}"; Flags: ignoreversion
Source: "Everything.exe"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Simple Desktop"; Filename: "{app}\SimpleDesktop.exe"; IconFilename: "{app}\ic.ico"
Name: "{commondesktop}\Simple Desktop"; Filename: "{app}\SimpleDesktop.exe"; IconFilename: "{app}\ic.ico"; Tasks: desktopicon
Name: "{group}\卸载 Simple Desktop"; Filename: "{uninstallexe}"

[Tasks]
Name: "desktopicon"; Description: "在桌面创建图标"; GroupDescription: "附加任务"

[Run]
Filename: "{app}\SimpleDesktop.exe"; Description: "运行 Simple Desktop"; Flags: nowait postinstall skipifsilent
; 添加开机自启注册表项（当前用户）
Filename: "reg"; Parameters: "add HKCU\Software\Microsoft\Windows\CurrentVersion\Run /v ""SimpleDesktop"" /t REG_SZ /d ""{app}\SimpleDesktop.exe"" /f"; Flags: runhidden
Filename: "{app}\Everything.exe"; Parameters: "-install-service"; Flags: runhidden
Filename: "{app}\Everything.exe"; Parameters: "-startup"; Flags: runhidden
Filename: "{app}\SimpleDesktop.exe"; Description: "运行 Simple Desktop"; Flags: nowait postinstall skipifsilent


[UninstallRun]
; 卸载时删除注册表启动项
Filename: "reg"; Parameters: "delete HKCU\Software\Microsoft\Windows\CurrentVersion\Run /v ""SimpleDesktop"" /f"; Flags: runhidden
