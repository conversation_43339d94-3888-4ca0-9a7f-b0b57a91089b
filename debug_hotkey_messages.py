#!/usr/bin/env python3
"""
实时调试热键消息传递问题
"""

import sys
import time
import threading
import ctypes
import ctypes.wintypes as wintypes
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_0 = 0x30

class HotkeyMessageDebugger:
    """热键消息调试器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.is_debugging = False
        self.registered_hotkeys = set()
        self.message_count = 0
        self.hotkey_events = []
        
    def register_debug_hotkeys(self):
        """注册调试热键"""
        print("🔧 注册调试热键...")
        
        success_count = 0
        
        for i in range(10):
            hotkey_id = 6000 + i  # 使用新的ID范围
            vk_code = VK_0 + i
            
            result = self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
            
            if result:
                self.registered_hotkeys.add(hotkey_id)
                success_count += 1
                print(f"   ✅ 调试热键 Ctrl+{i} 注册成功 (ID: {hotkey_id})")
            else:
                error_code = ctypes.GetLastError()
                print(f"   ❌ 调试热键 Ctrl+{i} 注册失败 (错误: {error_code})")
        
        print(f"📊 调试热键注册结果: {success_count}/10")
        return success_count > 0
    
    def start_message_debugging(self):
        """开始消息调试"""
        if self.is_debugging:
            return True
        
        if not self.register_debug_hotkeys():
            print("❌ 调试热键注册失败")
            return False
        
        self.is_debugging = True
        
        # 启动消息监控线程
        debug_thread = threading.Thread(target=self._debug_message_loop, daemon=True)
        debug_thread.start()
        
        print("🎯 热键消息调试已启动")
        print("📝 调试说明:")
        print("1. 请按 Ctrl+0 到 Ctrl+9")
        print("2. 观察消息接收情况")
        print("3. 每个消息都会被详细记录")
        
        return True
    
    def stop_debugging(self):
        """停止调试"""
        self.is_debugging = False
        
        # 注销调试热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()
        
        print("⏹️ 热键消息调试已停止")
    
    def _debug_message_loop(self):
        """调试消息循环"""
        print("🔄 调试消息循环已启动")
        
        msg = wintypes.MSG()
        
        while self.is_debugging:
            try:
                # 使用GetMessage进行阻塞等待
                result = self.user32.GetMessageW(ctypes.byref(msg), None, 0, 0)
                
                if result == -1:  # 错误
                    print("❌ GetMessageW 返回错误")
                    break
                elif result == 0:  # WM_QUIT
                    print("🔚 收到WM_QUIT消息")
                    break
                
                self.message_count += 1
                
                # 记录所有消息
                print(f"📨 消息 #{self.message_count}: 0x{msg.message:04X}")
                
                # 特别处理热键消息
                if msg.message == WM_HOTKEY:
                    hotkey_id = msg.wParam
                    self._handle_debug_hotkey(hotkey_id)
                
                # 分发消息
                self.user32.TranslateMessage(ctypes.byref(msg))
                self.user32.DispatchMessageW(ctypes.byref(msg))
                
            except Exception as e:
                print(f"❌ 调试消息循环错误: {e}")
                time.sleep(0.1)
        
        print("🔚 调试消息循环已退出")
    
    def _handle_debug_hotkey(self, hotkey_id):
        """处理调试热键"""
        current_time = time.time()
        
        if 6000 <= hotkey_id <= 6009:
            profile_id = hotkey_id - 6000
            
            event_info = {
                'time': current_time,
                'profile_id': profile_id,
                'hotkey_id': hotkey_id,
                'message_count': self.message_count
            }
            
            self.hotkey_events.append(event_info)
            
            print(f"🎯 热键消息接收成功!")
            print(f"   时间: {time.strftime('%H:%M:%S.%f', time.localtime(current_time))[:-3]}")
            print(f"   热键: Ctrl+{profile_id}")
            print(f"   热键ID: {hotkey_id}")
            print(f"   消息序号: #{self.message_count}")
            print(f"   累计热键事件: {len(self.hotkey_events)}")
    
    def get_debug_statistics(self):
        """获取调试统计"""
        return {
            'total_messages': self.message_count,
            'hotkey_events': len(self.hotkey_events),
            'registered_hotkeys': len(self.registered_hotkeys),
            'is_debugging': self.is_debugging,
            'recent_events': self.hotkey_events[-5:]
        }

def test_message_loop_methods():
    """测试不同的消息循环方法"""
    print("🧪 测试不同的消息循环方法")
    print("=" * 60)
    
    user32 = ctypes.windll.user32
    
    # 测试1: GetMessageW
    print("1. 测试GetMessageW可用性:")
    try:
        msg = wintypes.MSG()
        # 不实际调用，只检查函数是否存在
        func = user32.GetMessageW
        print("   ✅ GetMessageW 函数可用")
    except AttributeError:
        print("   ❌ GetMessageW 函数不可用")
    
    # 测试2: PeekMessageW
    print("2. 测试PeekMessageW可用性:")
    try:
        msg = wintypes.MSG()
        # 测试调用PeekMessageW
        result = user32.PeekMessageW(ctypes.byref(msg), None, 0, 0, 0)  # PM_NOREMOVE
        print(f"   ✅ PeekMessageW 调用成功，返回: {result}")
    except Exception as e:
        print(f"   ❌ PeekMessageW 调用失败: {e}")
    
    # 测试3: 注册测试热键
    print("3. 测试热键注册:")
    test_id = 7000
    result = user32.RegisterHotKey(None, test_id, MOD_CONTROL, VK_0)
    if result:
        print("   ✅ 测试热键注册成功")
        user32.UnregisterHotKey(None, test_id)
    else:
        error_code = ctypes.GetLastError()
        print(f"   ❌ 测试热键注册失败 (错误: {error_code})")

def create_enhanced_hotkey_manager():
    """创建增强的热键管理器"""
    print("\n🔧 创建增强的热键管理器")
    print("=" * 60)
    
    enhanced_code = '''
"""
增强的热键管理器 - 使用GetMessageW进行可靠的消息处理
"""

import time
import threading
import ctypes
import ctypes.wintypes as wintypes
from typing import Optional, Callable

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_CONTROL = 0x11
VK_0 = 0x30
DOUBLE_CLICK_INTERVAL = 0.5

class EnhancedHotkeyManager:
    """增强的热键管理器 - 使用可靠的消息循环"""
    
    def __init__(self):
        """初始化热键管理器"""
        self.is_listening = False
        self.toggle_callback: Optional[Callable[[], None]] = None
        self.profile_switch_callback: Optional[Callable[[int], None]] = None
        
        # 双击Ctrl检测
        self.last_ctrl_press_time = 0
        self.ctrl_press_count = 0
        self.ctrl_check_thread = None
        self.stop_event = threading.Event()
        
        # Windows API函数
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
        # 注册的热键ID
        self.registered_hotkeys = set()
        
        # 消息循环线程
        self.message_thread = None
        self.message_thread_id = None
        
        print("[ENHANCED] 增强的热键管理器已初始化")
    
    def set_callback(self, callback: Callable[[], None]):
        """设置窗口切换回调"""
        self.toggle_callback = callback
        print("[ENHANCED] 窗口切换回调已设置")
    
    def set_profile_switch_callback(self, callback: Callable[[int], None]):
        """设置Profile切换回调"""
        self.profile_switch_callback = callback
        print("[ENHANCED] Profile切换回调已设置")
    
    def start_listening(self) -> bool:
        """开始监听快捷键"""
        if self.is_listening:
            return True
        
        try:
            print("[ENHANCED] 开始注册Profile热键...")
            
            # 注册Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            successful_registrations = 0
            
            for i in range(10):
                hotkey_id = 1000 + i
                vk_code = VK_0 + i
                
                # 尝试注册热键
                result = self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
                if result:
                    self.registered_hotkeys.add(hotkey_id)
                    successful_registrations += 1
                    print(f"[ENHANCED] Ctrl+{i} 注册成功")
                else:
                    error_code = ctypes.GetLastError()
                    print(f"[ENHANCED] Ctrl+{i} 注册失败 (错误: {error_code})")
            
            print(f"[ENHANCED] Profile热键注册完成: {successful_registrations}/10")
            
            if successful_registrations == 0:
                print("[ERROR] 没有成功注册任何Profile热键")
                return False
            
            # 启动双击Ctrl检测线程
            self.stop_event.clear()
            self.ctrl_check_thread = threading.Thread(target=self._ctrl_monitor_thread, daemon=True)
            self.ctrl_check_thread.start()
            
            # 启动消息循环线程
            self.message_thread = threading.Thread(target=self._enhanced_message_loop, daemon=True)
            self.message_thread.start()
            
            self.is_listening = True
            print("[ENHANCED] 热键监听已启动")
            return True
            
        except Exception as e:
            print(f"[ERROR] 热键监听启动失败: {e}")
            self.stop_listening()
            return False
    
    def stop_listening(self):
        """停止监听快捷键"""
        if not self.is_listening:
            return
        
        print("[ENHANCED] 停止热键监听...")
        self.is_listening = False
        self.stop_event.set()
        
        # 注销热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()
        
        # 向消息线程发送退出消息
        if self.message_thread_id:
            self.user32.PostThreadMessageW(self.message_thread_id, 0x0012, 0, 0)  # WM_QUIT
        
        # 等待线程结束
        if self.ctrl_check_thread and self.ctrl_check_thread.is_alive():
            self.ctrl_check_thread.join(timeout=1.0)
        
        if self.message_thread and self.message_thread.is_alive():
            self.message_thread.join(timeout=2.0)
        
        print("[ENHANCED] 热键监听已停止")
    
    def _enhanced_message_loop(self):
        """增强的消息循环 - 使用GetMessageW"""
        print("[ENHANCED] 增强消息循环线程已启动")
        
        # 获取当前线程ID
        self.message_thread_id = self.kernel32.GetCurrentThreadId()
        print(f"[ENHANCED] 消息线程ID: {self.message_thread_id}")
        
        msg = wintypes.MSG()
        message_count = 0
        
        while self.is_listening:
            try:
                # 使用GetMessageW进行阻塞等待
                result = self.user32.GetMessageW(ctypes.byref(msg), None, 0, 0)
                
                if result == -1:  # 错误
                    print("[ERROR] GetMessageW 返回错误")
                    break
                elif result == 0:  # WM_QUIT
                    print("[ENHANCED] 收到退出消息")
                    break
                
                message_count += 1
                
                # 处理热键消息
                if msg.message == WM_HOTKEY:
                    hotkey_id = msg.wParam
                    print(f"[ENHANCED] 收到热键消息: ID={hotkey_id}, 消息#{message_count}")
                    self._handle_enhanced_hotkey(hotkey_id)
                
                # 分发消息
                self.user32.TranslateMessage(ctypes.byref(msg))
                self.user32.DispatchMessageW(ctypes.byref(msg))
                
            except Exception as e:
                print(f"[ERROR] 增强消息循环错误: {e}")
                time.sleep(0.1)
        
        print("[ENHANCED] 增强消息循环线程已退出")
    
    def _handle_enhanced_hotkey(self, hotkey_id: int):
        """处理增强热键消息"""
        if 1000 <= hotkey_id <= 1009:
            # Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            profile_id = hotkey_id - 1000
            print(f"[ENHANCED] 检测到Profile热键: Ctrl+{profile_id}")
            
            if self.profile_switch_callback:
                try:
                    print(f"[ENHANCED] 执行Profile切换回调: Profile {profile_id}")
                    self.profile_switch_callback(profile_id)
                    print(f"[ENHANCED] Profile切换回调执行完成")
                except Exception as e:
                    print(f"[ERROR] Profile切换回调错误: {e}")
            else:
                print("[WARN] Profile切换回调未设置")
    
    def _ctrl_monitor_thread(self):
        """监控Ctrl键双击的线程"""
        last_state = False
        
        while not self.stop_event.is_set():
            try:
                # 检查Ctrl键状态
                ctrl_state = self.user32.GetAsyncKeyState(VK_CONTROL)
                is_pressed = bool(ctrl_state & 0x8000)
                
                # 边沿触发：检测从未按下到按下的转换
                if is_pressed and not last_state:
                    current_time = time.time()
                    
                    # 如果有之前的按键记录，检查双击
                    if self.ctrl_press_count > 0:
                        time_diff = current_time - self.last_ctrl_press_time
                        
                        if time_diff <= DOUBLE_CLICK_INTERVAL:
                            # 双击Ctrl检测到
                            if self.toggle_callback:
                                try:
                                    self.toggle_callback()
                                    print("[ENHANCED] 双击Ctrl检测到")
                                except Exception as e:
                                    print(f"[ERROR] 切换回调错误: {e}")
                            
                            # 重置状态
                            self.ctrl_press_count = 0
                            self.last_ctrl_press_time = 0
                            last_state = is_pressed
                            continue
                    
                    # 记录新的按键
                    self.last_ctrl_press_time = current_time
                    self.ctrl_press_count = 1
                
                # 重置超时的计数
                current_time = time.time()
                if (self.ctrl_press_count > 0 and 
                    current_time - self.last_ctrl_press_time > DOUBLE_CLICK_INTERVAL):
                    self.ctrl_press_count = 0
                
                last_state = is_pressed
                
                # 短暂休眠
                time.sleep(0.05)
                
            except Exception as e:
                print(f"[ERROR] Ctrl监控错误: {e}")
                break

# 创建全局实例
hotkey_manager = EnhancedHotkeyManager()
'''
    
    try:
        # 保存增强的热键管理器
        enhanced_file = project_root / "simple_desktop" / "core" / "enhanced_hotkey.py"
        
        with open(enhanced_file, 'w', encoding='utf-8') as f:
            f.write(enhanced_code)
        
        print(f"✅ 增强的热键管理器已保存到: {enhanced_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建增强版本失败: {e}")
        return False

def test_enhanced_hotkey_manager():
    """测试增强的热键管理器"""
    print("\n🧪 测试增强的热键管理器")
    print("=" * 60)
    
    try:
        from simple_desktop.core.enhanced_hotkey import hotkey_manager
        
        print("✅ 增强的热键管理器导入成功")
        
        # 设置测试回调
        profile_switches = []
        
        def test_profile_callback(profile_id):
            profile_switches.append(profile_id)
            print(f"[TEST] Profile切换回调触发: {profile_id}")
        
        hotkey_manager.set_profile_switch_callback(test_profile_callback)
        
        # 启动热键监听
        success = hotkey_manager.start_listening()
        
        if success:
            print("✅ 增强的热键管理器启动成功")
            
            print("\n📝 增强版测试说明:")
            print("1. 增强的热键管理器已启动")
            print("2. 使用GetMessageW进行可靠的消息处理")
            print("3. 请按 Ctrl+0 到 Ctrl+9 测试Profile切换")
            print("4. 观察详细的调试输出")
            print("5. 测试将在25秒后自动结束")
            
            # 等待测试
            time.sleep(25)
            
            print(f"\n📊 增强版测试结果:")
            print(f"   Profile切换次数: {len(profile_switches)}")
            
            if profile_switches:
                print("   ✅ 增强版Profile热键功能正常")
                print(f"   切换的Profile: {profile_switches}")
            else:
                print("   ❌ 增强版Profile热键功能仍有问题")
            
            # 停止监听
            hotkey_manager.stop_listening()
            
            return len(profile_switches) > 0
        else:
            print("❌ 增强的热键管理器启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试增强的热键管理器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 热键消息传递深度调试")
    print("=" * 80)
    
    # 执行调试和修复步骤
    steps = [
        ("测试消息循环方法", test_message_loop_methods),
        ("创建增强的热键管理器", create_enhanced_hotkey_manager),
        ("测试增强的热键管理器", test_enhanced_hotkey_manager),
    ]
    
    passed_steps = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            if step_func():
                passed_steps += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 热键消息传递深度调试完成！")
    print(f"完成步骤: {passed_steps}/{total_steps}")
    
    if passed_steps >= 2:
        print("\n💡 下一步操作:")
        print("1. 修改app.py使用增强版热键管理器:")
        print("   将 'from .core.fixed_hotkey import hotkey_manager'")
        print("   改为 'from .core.enhanced_hotkey import hotkey_manager'")
        print("2. 重新启动Simple Desktop: python main.py")
        print("3. 测试Ctrl+0~9热键并观察详细日志")
    else:
        print(f"\n⚠️ 调试未完成，请检查相关问题")

if __name__ == "__main__":
    main()
