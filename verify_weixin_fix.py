#!/usr/bin/env python3
"""
验证微信搜索修复结果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_weixin_search():
    """验证微信搜索功能"""
    print("🔍 验证微信搜索功能")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 显示当前扫描目录
        scan_dirs = engine.get_scan_directories()
        print(f"当前扫描目录 ({len(scan_dirs)}个):")
        for i, dir_path in enumerate(scan_dirs):
            exists = "✅" if os.path.exists(dir_path) else "❌"
            print(f"  {i+1}. {exists} {dir_path}")
        
        print(f"\n📁 文件类型分类:")
        file_types = engine.file_type_extensions
        for category, extensions in file_types.items():
            if ".lnk" in extensions:
                print(f"  ✅ {category}: {extensions}")
            else:
                print(f"     {category}: {extensions}")
        
        # 测试搜索功能
        print(f"\n🔍 搜索测试:")
        
        # 1. 搜索"微信"
        print("1. 搜索'微信':")
        results1 = engine.search("微信", limit=10)
        print(f"   结果数量: {len(results1)}")
        
        for i, result in enumerate(results1):
            icon = "🔗" if result.suffix.lower() == ".lnk" else "📄" if result.item_type == "file" else "📁"
            print(f"     {i+1}. {icon} {result.filename} ({result.suffix})")
            print(f"        {result.filepath}")
        
        # 2. 搜索"weixin"
        print("\n2. 搜索'weixin':")
        results2 = engine.search("weixin", limit=10)
        print(f"   结果数量: {len(results2)}")
        
        for i, result in enumerate(results2):
            icon = "🔗" if result.suffix.lower() == ".lnk" else "📄" if result.item_type == "file" else "📁"
            print(f"     {i+1}. {icon} {result.filename} ({result.suffix})")
            print(f"        {result.filepath}")
        
        # 3. 文件类型过滤测试
        print("\n3. 文件类型过滤测试 - 只搜索可执行文件:")
        results3 = engine.search("微信", limit=10, file_types=["executables"])
        print(f"   结果数量: {len(results3)}")
        
        for i, result in enumerate(results3):
            icon = "🔗" if result.suffix.lower() == ".lnk" else "⚙️"
            print(f"     {i+1}. {icon} {result.filename} ({result.suffix})")
            print(f"        {result.filepath}")
        
        # 4. 排序测试
        print("\n4. 排序测试:")
        if results1:
            from simple_desktop.ui.search_window import FloatingSearchWindow
            from PySide6.QtWidgets import QApplication
            
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            window = FloatingSearchWindow()
            sorted_results = window.sort_search_results(results1)
            
            print("   排序后的结果:")
            for i, result in enumerate(sorted_results):
                priority = "🥇" if result.suffix.lower() == ".lnk" else "🥈" if result.item_type == "folder" else "🥉"
                print(f"     {i+1}. {priority} {result.filename} ({result.item_type}, {result.suffix})")
        
        # 总结
        all_results = results1 + results2 + results3
        total_results = len(all_results)
        lnk_results = sum(1 for r in all_results if r.suffix.lower() == ".lnk")

        print(f"\n🔍 详细分析:")
        print(f"   搜索'微信'找到: {len(results1)} 个结果")
        print(f"   搜索'weixin'找到: {len(results2)} 个结果")
        print(f"   文件类型过滤找到: {len(results3)} 个结果")

        for i, result in enumerate(all_results):
            suffix_info = f"({result.suffix})" if result.suffix else "(无后缀)"
            print(f"     结果{i+1}: {result.filename} {suffix_info} - 是否.lnk: {result.suffix.lower() == '.lnk'}")
        
        print(f"\n📊 测试总结:")
        print(f"   总搜索结果: {total_results}")
        print(f"   快捷方式文件: {lnk_results}")
        
        if lnk_results > 0:
            print(f"   ✅ 成功！可以搜索到微信快捷方式")
            return True
        else:
            print(f"   ❌ 失败！仍然无法搜索到微信快捷方式")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print(f"\n🖥️ 测试GUI集成")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        from simple_desktop.ui.search_window import FloatingSearchWindow
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        print("✅ 搜索窗口创建成功")
        
        # 显示窗口
        search_window.show_window()
        
        print("\n📝 GUI测试指南:")
        print("   1. 搜索窗口已显示")
        print("   2. 在搜索框输入'微信'")
        print("   3. 应该看到微信快捷方式出现在结果顶部")
        print("   4. 快捷方式图标应该是🔗")
        print("   5. 点击快捷方式应该能打开微信")
        print("   6. 尝试切换到其他Profile测试搜索范围")
        
        # 10秒后自动关闭
        QTimer.singleShot(10000, search_window.close)
        QTimer.singleShot(10500, app.quit)
        
        print("\n🚀 GUI测试窗口已启动！")
        print("   - 10秒后自动关闭")
        print("   - 或按ESC/Ctrl+C手动退出")
        
        # 运行应用
        app.exec()
        
        print("\n✅ GUI测试完成")
        return True
        
    except KeyboardInterrupt:
        print("\n   测试已退出")
        return True
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("✅ Simple Desktop - 微信搜索修复验证")
    print("=" * 80)
    
    # 验证搜索功能
    search_success = verify_weixin_search()
    
    if search_success:
        # 测试GUI集成
        test_gui_integration()
    
    print("\n" + "=" * 80)
    print("🎯 验证完成！")
    
    if search_success:
        print("\n🎉 修复成功总结:")
        print("1. ✅ 扫描目录已包含公共桌面和开始菜单")
        print("2. ✅ .lnk文件已添加到可执行文件分类")
        print("3. ✅ 搜索'微信'可以找到快捷方式")
        print("4. ✅ 快捷方式按优先级排序显示")
        print("5. ✅ 文件类型过滤正常工作")
        
        print("\n💡 使用说明:")
        print("- 搜索'微信'：找到中文名称的快捷方式")
        print("- 搜索'weixin'：找到包含weixin的文件")
        print("- 快捷方式会显示🔗图标并排在最前面")
        print("- 可以通过文件类型筛选只显示可执行文件")
    else:
        print("\n❌ 修复验证失败")
        print("请检查:")
        print("1. 微信是否已正确安装")
        print("2. Everything服务是否正在运行")
        print("3. 扫描目录配置是否正确")

if __name__ == "__main__":
    main()
