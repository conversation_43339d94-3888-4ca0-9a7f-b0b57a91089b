#!/usr/bin/env python3
"""
测试Simple Desktop搜索优化效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_sorting_logic():
    """测试排序逻辑优化"""
    print("🔍 测试排序逻辑优化")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import SearchResult
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口实例来测试排序
        window = FloatingSearchWindow()
        
        # 创建测试结果，包含各种文件类型
        test_results = [
            SearchResult("document.txt", "C:\\test\\document.txt", "file", suffix=".txt"),
            SearchResult("folder", "C:\\test\\folder", "folder"),
            SearchResult("app.exe", "C:\\test\\app.exe", "file", suffix=".exe"),
            SearchResult("shortcut.lnk", "C:\\test\\shortcut.lnk", "file", suffix=".lnk"),
            SearchResult("website.url", "C:\\test\\website.url", "file", suffix=".url"),
            SearchResult("script.bat", "C:\\test\\script.bat", "file", suffix=".bat"),
            SearchResult("command.cmd", "C:\\test\\command.cmd", "file", suffix=".cmd"),
            SearchResult("installer.msi", "C:\\test\\installer.msi", "file", suffix=".msi"),
            SearchResult("image.jpg", "C:\\test\\image.jpg", "file", suffix=".jpg"),
            SearchResult("another_folder", "C:\\test\\another_folder", "folder"),
        ]
        
        print("原始结果顺序:")
        for i, result in enumerate(test_results):
            print(f"  {i+1}. {result.filename} ({result.item_type}, {result.suffix})")
        
        # 测试排序
        sorted_results = window.sort_search_results(test_results)
        
        print("\n排序后的结果:")
        for i, result in enumerate(sorted_results):
            # 确定优先级标识
            quick_launch_extensions = {".lnk", ".exe", ".url", ".bat", ".cmd", ".com", ".scr", ".msi"}
            
            if result.suffix.lower() in quick_launch_extensions:
                priority = "🥇 快速启动"
            elif result.item_type == "folder":
                priority = "🥈 文件夹"
            else:
                priority = "🥉 普通文件"
            
            print(f"  {i+1}. {priority}: {result.filename} ({result.suffix})")
        
        # 验证排序是否正确
        quick_launch_count = 0
        folder_count = 0
        file_count = 0
        
        for result in sorted_results:
            quick_launch_extensions = {".lnk", ".exe", ".url", ".bat", ".cmd", ".com", ".scr", ".msi"}
            
            if result.suffix.lower() in quick_launch_extensions:
                if folder_count > 0 or file_count > 0:
                    print("\n❌ 排序错误：快速启动文件应该在最前面")
                    return False
                quick_launch_count += 1
            elif result.item_type == "folder":
                if file_count > 0:
                    print("\n❌ 排序错误：文件夹应该在普通文件前面")
                    return False
                folder_count += 1
            else:
                file_count += 1
        
        print(f"\n✅ 排序验证成功:")
        print(f"   快速启动文件: {quick_launch_count} 个")
        print(f"   文件夹: {folder_count} 个")
        print(f"   普通文件: {file_count} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 排序逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scan_directories():
    """测试扫描目录配置优化"""
    print("\n🔍 测试扫描目录配置优化")
    print("=" * 50)
    
    try:
        from simple_desktop.core.config import config_manager
        
        # 获取Profile 0的扫描目录
        scan_dirs = config_manager.get_scan_directories(0)
        
        print("当前扫描目录:")
        for i, dir_path in enumerate(scan_dirs):
            exists = "✅" if os.path.exists(dir_path) else "❌"
            print(f"  {i+1}. {exists} {dir_path}")
        
        # 检查是否包含期望的目录
        expected_dirs = [
            str(Path.home() / "Desktop"),
            "C:\\Users\\<USER>\\Desktop",
            "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs"
        ]
        
        # 检查是否移除了不期望的目录
        unwanted_dirs = [
            str(Path.home() / "Documents"),
            str(Path.home() / "Downloads")
        ]
        
        print("\n目录配置验证:")
        
        for expected_dir in expected_dirs:
            if any(expected_dir.lower() in scan_dir.lower() for scan_dir in scan_dirs):
                print(f"  ✅ 包含期望目录: {expected_dir}")
            else:
                print(f"  ⚠️ 缺少期望目录: {expected_dir}")
        
        for unwanted_dir in unwanted_dirs:
            if any(unwanted_dir.lower() in scan_dir.lower() for scan_dir in scan_dirs):
                print(f"  ❌ 仍包含不期望目录: {unwanted_dir}")
            else:
                print(f"  ✅ 已移除不期望目录: {unwanted_dir}")
        
        print(f"\n✅ 扫描目录配置验证完成")
        print(f"   总目录数: {len(scan_dirs)}")
        print(f"   聚焦于桌面和程序菜单，减少文档干扰")
        
        return True
        
    except Exception as e:
        print(f"❌ 扫描目录测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_type_classification():
    """测试文件类型分类优化"""
    print("\n🔍 测试文件类型分类优化")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        file_types = engine.file_type_extensions
        
        print("executables分类包含的文件类型:")
        executables = file_types.get("executables", [])
        for ext in executables:
            print(f"  {ext}")
        
        # 检查是否包含所有期望的快速启动文件类型
        expected_extensions = [".exe", ".lnk", ".url", ".bat", ".cmd", ".msi", ".com", ".scr"]
        
        print("\n快速启动文件类型验证:")
        for ext in expected_extensions:
            if ext in executables:
                print(f"  ✅ 包含: {ext}")
            else:
                print(f"  ❌ 缺少: {ext}")
        
        # 测试_should_include_result方法
        print("\n文件类型过滤测试:")
        
        class TestResult:
            def __init__(self, filename, suffix, item_type):
                self.filename = filename
                self.suffix = suffix
                self.item_type = item_type
        
        test_cases = [
            TestResult("app.exe", ".exe", "file"),
            TestResult("shortcut.lnk", ".lnk", "file"),
            TestResult("website.url", ".url", "file"),
            TestResult("script.bat", ".bat", "file"),
            TestResult("document.txt", ".txt", "file"),
        ]
        
        for test_result in test_cases:
            should_include = engine._should_include_result(
                test_result, ["executables"], True
            )
            
            is_executable = test_result.suffix in executables
            status = "✅" if should_include == is_executable else "❌"
            
            print(f"  {status} {test_result.filename} ({test_result.suffix}): "
                  f"应包含={is_executable}, 实际={should_include}")
        
        print(f"\n✅ 文件类型分类验证完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件类型分类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_search():
    """测试真实搜索效果"""
    print("\n🔍 测试真实搜索效果")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试搜索常见应用程序
        test_queries = ["微信", "chrome", "notepad"]
        
        for query in test_queries:
            print(f"\n搜索'{query}':")
            
            try:
                results = engine.search(query, limit=5)
                print(f"  结果数量: {len(results)}")
                
                quick_launch_count = 0
                for i, result in enumerate(results):
                    quick_launch_extensions = {".lnk", ".exe", ".url", ".bat", ".cmd", ".com", ".scr", ".msi"}
                    
                    if result.suffix.lower() in quick_launch_extensions:
                        icon = "🚀"
                        quick_launch_count += 1
                    elif result.item_type == "folder":
                        icon = "📁"
                    else:
                        icon = "📄"
                    
                    print(f"    {i+1}. {icon} {result.filename} ({result.suffix})")
                    print(f"       {result.filepath}")
                
                print(f"  快速启动文件数量: {quick_launch_count}")
                
            except Exception as e:
                print(f"  ❌ 搜索'{query}'失败: {e}")
        
        print(f"\n✅ 真实搜索测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 搜索优化效果验证")
    print("=" * 80)
    
    # 测试各项优化
    tests = [
        ("排序逻辑优化", test_sorting_logic),
        ("扫描目录配置优化", test_scan_directories),
        ("文件类型分类优化", test_file_type_classification),
        ("真实搜索效果", test_real_search),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed_tests += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 80)
    print("🎯 优化验证完成！")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有优化都已成功实现:")
        print("1. ✅ 快速启动文件(.exe、.lnk、.url等)获得最高排序优先级")
        print("2. ✅ 扫描目录聚焦于桌面和程序菜单，减少文档干扰")
        print("3. ✅ 文件类型分类包含所有快速启动文件类型")
        print("4. ✅ 真实搜索体验得到优化")
        
        print("\n💡 优化效果:")
        print("- 搜索应用程序时，可执行文件和快捷方式排在最前面")
        print("- 减少了文档和下载文件的干扰")
        print("- 提升了快速启动应用程序的体验")
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 项测试失败，请检查相关配置")

if __name__ == "__main__":
    main()
