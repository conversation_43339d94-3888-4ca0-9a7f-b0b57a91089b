
"""
改进的热键管理器，解决线程安全问题
"""

from simple_desktop.core.hotkey import <PERSON><PERSON><PERSON><PERSON><PERSON> as OriginalHotkeyManager
from simple_desktop.core.hotkey_bridge import ThreadSafeHotkeyBridge

class ImprovedHotkeyManager:
    """改进的热键管理器，使用线程安全的桥接器"""
    
    def __init__(self, search_window):
        self.original_manager = OriginalHotkeyManager()
        self.bridge = ThreadSafeHotkeyBridge(search_window)
        
        # 设置桥接器的回调到原始管理器
        self.original_manager.set_callback(self.bridge.hotkey_callback)
    
    def start_listening(self):
        """启动热键监听"""
        return self.original_manager.start_listening()
    
    def stop_listening(self):
        """停止热键监听"""
        return self.original_manager.stop_listening()
    
    @property
    def is_listening(self):
        """获取监听状态"""
        return self.original_manager.is_listening
