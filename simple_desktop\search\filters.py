"""
搜索过滤器模块
提供各种类型的搜索结果过滤器，用于在Everything搜索结果基础上进行程序化过滤
"""

import os
from abc import ABC, abstractmethod
from typing import List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass

from .models import SearchResult


@dataclass
class FilterCriteria:
    """过滤条件数据类"""
    file_types: Optional[List[str]] = None  # 文件类型列表，如 ["documents", "images"]
    include_folders: bool = True
    scan_directories: Optional[List[str]] = None  # 扫描目录列表
    date_range_days: Optional[int] = None  # 最近N天的文件
    min_size_mb: Optional[int] = None  # 最小文件大小（MB）
    max_size_mb: Optional[int] = None  # 最大文件大小（MB）
    extensions: Optional[Set[str]] = None  # 具体的文件扩展名集合，如 {".txt", ".doc"}


class SearchFilter(ABC):
    """搜索过滤器基类"""
    
    @abstractmethod
    def should_include(self, result: SearchResult) -> bool:
        """判断是否应该包含此搜索结果"""
        pass


class FileTypeFilter(SearchFilter):
    """文件类型过滤器"""

    def __init__(self, file_types: List[str], include_folders: bool = True, file_type_extensions: Optional[dict] = None):
        self.file_types = file_types
        self.include_folders = include_folders

        # 文件类型扩展名映射 - 可以从外部传入，否则使用默认值
        self.file_type_extensions = file_type_extensions or {
            "documents": [".txt", ".doc", ".docx", ".pdf", ".rtf", ".odt", ".xls", ".xlsx", ".ppt", ".pptx"],
            "images": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg", ".ico", ".webp"],
            "videos": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp"],
            "audio": [".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a"],
            "archives": [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"],
            "executables": [".exe", ".msi", ".bat", ".cmd", ".com", ".scr"],
            "code": [".py", ".js", ".html", ".css", ".cpp", ".c", ".java", ".cs", ".php", ".rb", ".go"],
        }
    
    def should_include(self, result: SearchResult) -> bool:
        """判断是否应该包含此结果"""
        # 检查文件夹
        if result.item_type == "folder":
            if not self.include_folders:
                return False
            # 如果没有文件类型过滤，包含所有文件夹
            if not self.file_types:
                return True
            # 如果有文件类型过滤，只有明确包含"folders"时才包含文件夹
            return "folders" in self.file_types

        # 检查文件类型
        if self.file_types:
            # 有文件类型过滤
            result_ext = result.suffix.lower()
            
            # 检查是否匹配任何选中的文件类型
            for file_type in self.file_types:
                if file_type != "folders" and file_type in self.file_type_extensions:
                    if result_ext in self.file_type_extensions[file_type]:
                        return True
            
            # 如果没有匹配任何文件类型，返回False
            return False

        # 没有文件类型过滤，包含所有文件
        return True


class DirectoryFilter(SearchFilter):
    """目录过滤器"""
    
    def __init__(self, scan_directories: List[str]):
        self.scan_directories = [os.path.normpath(d) for d in scan_directories]
    
    def should_include(self, result: SearchResult) -> bool:
        """判断文件是否在指定的扫描目录中"""
        if not self.scan_directories:
            return True

        result_path = os.path.normpath(result.filepath)

        for scan_dir in self.scan_directories:
            # 检查文件路径是否在扫描目录下
            if result_path.startswith(scan_dir):
                return True

        return False


class DateRangeFilter(SearchFilter):
    """日期范围过滤器"""
    
    def __init__(self, days: int):
        self.days = days
        self.cutoff_date = datetime.now() - timedelta(days=days)
    
    def should_include(self, result: SearchResult) -> bool:
        """判断文件是否在指定的日期范围内"""
        if not result.date_modified_dt:
            # 如果没有日期信息，默认包含
            return True
        
        return result.date_modified_dt >= self.cutoff_date


class SizeFilter(SearchFilter):
    """文件大小过滤器"""
    
    def __init__(self, min_size_mb: Optional[int] = None, max_size_mb: Optional[int] = None):
        self.min_size_bytes = min_size_mb * 1024 * 1024 if min_size_mb else None
        self.max_size_bytes = max_size_mb * 1024 * 1024 if max_size_mb else None
    
    def should_include(self, result: SearchResult) -> bool:
        """判断文件大小是否在指定范围内"""
        # 文件夹通常没有大小限制
        if result.item_type == "folder":
            return True
        
        file_size = result.size_bytes
        
        if self.min_size_bytes is not None and file_size < self.min_size_bytes:
            return False
        
        if self.max_size_bytes is not None and file_size > self.max_size_bytes:
            return False
        
        return True


class ExtensionFilter(SearchFilter):
    """文件扩展名过滤器"""

    def __init__(self, extensions: Set[str], include_folders: bool = True):
        # 确保所有扩展名都以点开头
        self.extensions = set()
        for ext in extensions:
            if not ext.startswith("."):
                ext = f".{ext}"
            self.extensions.add(ext.lower())
        self.include_folders = include_folders

    def should_include(self, result: SearchResult) -> bool:
        """判断文件扩展名是否匹配"""
        if result.item_type == "folder":
            return self.include_folders  # 根据设置决定是否包含文件夹

        return result.suffix.lower() in self.extensions


class FilterPipeline:
    """过滤器管道，用于组合多个过滤器"""
    
    def __init__(self):
        self.filters: List[SearchFilter] = []
    
    def add_filter(self, filter_obj: SearchFilter):
        """添加过滤器"""
        self.filters.append(filter_obj)
    
    def apply_filters(self, results: List[SearchResult], max_results: Optional[int] = None) -> List[SearchResult]:
        """
        应用所有过滤器 - 高性能版本，支持大结果集的高效处理

        Args:
            results: 原始搜索结果列表
            max_results: 最大返回结果数，达到此数量后停止处理以提高性能

        Returns:
            过滤后的结果列表
        """
        if not self.filters:
            # 如果没有过滤器，直接返回原结果（可能截断）
            return results[:max_results] if max_results else results

        if not results:
            return []

        # 性能优化：对过滤器按选择性排序，将最严格的过滤器放在前面
        # 这样可以更早地排除不符合条件的结果
        sorted_filters = self._sort_filters_by_selectivity()

        filtered_results = []
        processed_count = 0

        try:
            for result in results:
                processed_count += 1

                # 检查是否通过所有过滤器
                should_include = True
                for filter_obj in sorted_filters:
                    try:
                        if not filter_obj.should_include(result):
                            should_include = False
                            break
                    except Exception as e:
                        # 单个过滤器出错时，记录错误但继续处理
                        print(f"Filter error for {result.filename}: {e}")
                        should_include = False
                        break

                if should_include:
                    filtered_results.append(result)

                    # 如果达到最大结果数，提前退出以提高性能
                    if max_results and len(filtered_results) >= max_results:
                        break

                # 每处理1000个结果检查一次，避免长时间阻塞
                if processed_count % 1000 == 0:
                    # 可以在这里添加进度回调或中断检查
                    pass

        except Exception as e:
            print(f"Filter pipeline error after processing {processed_count} results: {e}")
            # 返回已处理的结果

        return filtered_results

    def _sort_filters_by_selectivity(self) -> List[SearchFilter]:
        """
        按选择性对过滤器排序，将最严格的过滤器放在前面
        这是一个简单的启发式排序，可以根据实际使用情况调整
        """
        # 定义过滤器的选择性权重（越高越严格，应该越早执行）
        filter_weights = {
            'ExtensionFilter': 5,      # 扩展名过滤通常很严格
            'SizeFilter': 4,           # 大小过滤也比较严格
            'DateRangeFilter': 3,      # 日期过滤中等严格
            'FileTypeFilter': 2,       # 文件类型过滤相对宽松
            'DirectoryFilter': 1,      # 目录过滤通常最宽松
        }

        def get_filter_weight(filter_obj):
            filter_name = filter_obj.__class__.__name__
            return filter_weights.get(filter_name, 0)

        return sorted(self.filters, key=get_filter_weight, reverse=True)
    
    @classmethod
    def from_criteria(cls, criteria: FilterCriteria, file_type_extensions: Optional[dict] = None) -> 'FilterPipeline':
        """从过滤条件创建过滤器管道"""
        pipeline = cls()

        # 添加文件类型过滤器
        if criteria.file_types:
            pipeline.add_filter(FileTypeFilter(criteria.file_types, criteria.include_folders, file_type_extensions))

        # 添加目录过滤器
        if criteria.scan_directories:
            pipeline.add_filter(DirectoryFilter(criteria.scan_directories))

        # 添加日期范围过滤器
        if criteria.date_range_days:
            pipeline.add_filter(DateRangeFilter(criteria.date_range_days))

        # 添加大小过滤器
        if criteria.min_size_mb or criteria.max_size_mb:
            pipeline.add_filter(SizeFilter(criteria.min_size_mb, criteria.max_size_mb))

        # 添加扩展名过滤器
        if criteria.extensions:
            pipeline.add_filter(ExtensionFilter(criteria.extensions, criteria.include_folders))

        return pipeline
