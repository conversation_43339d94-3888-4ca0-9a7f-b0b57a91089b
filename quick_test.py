"""
快速测试MUN搜索
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mun_search():
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        print("测试MUN搜索:")
        results = engine.search('MUN', limit=20)
        print(f"结果数: {len(results)}")
        
        found_md = False
        for result in results:
            if 'MUN.md' in result.filename:
                print(f"✅ 找到: {result.filename} - {result.filepath}")
                found_md = True
        
        if not found_md:
            print("❌ 未找到MUN.md")
            
        print("\n测试增强查询:")
        enhanced = engine._build_enhanced_query('MUN')
        print(f"增强查询: {enhanced}")
        
        # 直接测试Everything SDK
        print("\n直接测试Everything SDK:")
        sdk_results = engine.everything_sdk.search(
            query=enhanced,
            max_results=50,
            match_case=False,
            match_whole_word=False,
            use_regex=False
        )
        print(f"SDK结果数: {len(sdk_results)}")
        
        for result in sdk_results:
            if 'MUN.md' in result.filename:
                print(f"✅ SDK找到: {result.filename} - {result.full_path}")
                break
        else:
            print("❌ SDK未找到MUN.md")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mun_search()
