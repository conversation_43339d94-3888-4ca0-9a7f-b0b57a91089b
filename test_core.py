#!/usr/bin/env python3
"""
测试核心功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_everything_sdk():
    """测试Everything SDK"""
    print("=== 测试Everything SDK ===")
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk, is_everything_available
        
        # 检查Everything是否可用
        available = is_everything_available()
        print(f"Everything可用性: {available}")
        
        if available:
            sdk = get_everything_sdk()
            version = sdk.get_version_info()
            print(f"Everything版本: {version}")
            
            # 测试简单搜索
            results = sdk.search("test", max_results=5)
            print(f"搜索'test'结果数量: {len(results)}")
            
            for i, result in enumerate(results[:3]):
                print(f"  {i+1}. {result.filename} - {result.full_path}")
        
        print("✓ Everything SDK测试完成")
        
    except Exception as e:
        print(f"✗ Everything SDK测试失败: {e}")

def test_config_manager():
    """测试配置管理器"""
    print("\n=== 测试配置管理器 ===")
    try:
        from simple_desktop.core.config import config_manager
        
        # 测试基本配置
        current_profile = config_manager.get_current_profile_id()
        print(f"当前Profile ID: {current_profile}")
        
        # 测试Profile配置
        profile = config_manager.get_current_profile()
        if profile:
            print(f"当前Profile名称: {profile.name}")
            print(f"扫描目录数量: {len(profile.scan_directories)}")
            print(f"文件类型过滤: {profile.enabled_file_types}")
        
        # 测试默认行为
        default_action = config_manager.get_default_action()
        print(f"默认行为: {default_action}")
        
        print("✓ 配置管理器测试完成")
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")

def test_search_engine():
    """测试搜索引擎"""
    print("\n=== 测试搜索引擎 ===")
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 检查Everything可用性
        available = engine.is_everything_available()
        print(f"搜索引擎可用性: {available}")
        
        if available:
            # 测试搜索
            results = engine.search("txt", limit=5)
            print(f"搜索'txt'结果数量: {len(results)}")
            
            for i, result in enumerate(results[:3]):
                print(f"  {i+1}. {result.filename} ({result.item_type}) - {result.filepath}")
        
        # 测试文件类型
        file_types = engine.get_all_file_types()
        print(f"支持的文件类型: {file_types}")
        
        print("✓ 搜索引擎测试完成")
        
    except Exception as e:
        print(f"✗ 搜索引擎测试失败: {e}")

def test_profile_manager():
    """测试Profile管理器"""
    print("\n=== 测试Profile管理器 ===")
    try:
        from simple_desktop.core.profile_manager import profile_manager
        
        # 测试当前Profile
        current_id = profile_manager.current_profile_id
        print(f"当前Profile ID: {current_id}")
        
        current_profile = profile_manager.current_profile
        if current_profile:
            print(f"当前Profile名称: {current_profile.name}")
        
        # 测试所有Profile信息
        all_profiles = profile_manager.get_all_profiles_info()
        print(f"Profile数量: {len(all_profiles)}")
        
        for pid, info in all_profiles.items():
            print(f"  Profile {pid}: {info['name']} (目录: {info['scan_directories_count']})")
        
        print("✓ Profile管理器测试完成")
        
    except Exception as e:
        print(f"✗ Profile管理器测试失败: {e}")

def main():
    """主测试函数"""
    print("Simple Desktop 核心功能测试")
    print("=" * 50)
    
    test_everything_sdk()
    test_config_manager()
    test_search_engine()
    test_profile_manager()
    
    print("\n" + "=" * 50)
    print("测试完成！")

if __name__ == "__main__":
    main()
