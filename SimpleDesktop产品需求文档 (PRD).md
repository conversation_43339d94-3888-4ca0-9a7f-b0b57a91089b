## 文件极速搜索工具（Everything SDK 集成）产品需求文档 (PRD)

### 1. 产品概述

- **产品名称**：Simple Desktop
- **定位**：基于 Windows 原生 Everything 引擎的本地文件搜索工具，提供集中、轻量、秒级搜索体验。
- **核心价值**：零配置、秒响应、极简界面，帮助用户在海量本地文件中瞬间定位目标。

### 2. 目标用户

- **个人用户**：办公人员、学生、开发者，日常需快速定位代码、文档、媒体等文件。
- **IT 专业人士**：系统管理员、支持人员，需要在多级目录中排查或检索配置文件和日志。

### 3. 用户痛点

1. Windows 自带搜索和资源管理器检索速度慢，面对百万级文件需十秒以上。
2. 第三方索引工具常需复杂配置、多次全量重建索引。
3. 现有搜索界面冗余，操作步骤多，缺乏轻量化唤起方式。

### 4. 产品目标

1. **性能**：搜索响应时间 ≤10ms（关键词匹配）。
2. **易用性**：两次 Ctrl 快捷键或托盘点击一键唤出，操作全键盘友好。
3. **简洁界面**：浮窗式毛玻璃圆角窗口，仅保留核心搜索输入与结果列表。
4. **零维护**：无需手动构建索引，依赖 Everything 实时更新。

### 5. 主要功能

以下模块直接映射到界面中的核心控件与交互，实现搜索工具的全部基本功能：

1. **标签栏 (Profile Tabs)**
   - 位于窗口顶部，共10个标签（0-9），分别对应不同的搜索配置（Profile）。
   - 切换标签可立即切换索引目录集合和搜索引擎实例，支持快捷键切换。
2. **搜索输入框 (Search Input)**
   - 包含主输入框和后缀输入框，主输入框占7/8宽度，后缀框占1/8。
   - 文本防抖（延迟100-300ms）后触发后台线程查询，实时响应用户输入。
3. **文件类型筛选 (Type Filter)**
   - 点击“类型”按钮弹出对话框或页面式面板，可多选文档/图片/视频等类型。
   - 选中后动态过滤查询结果，不需重建索引。
4. **扫描目录管理 (Path Management)**
   - 点击“目录”按钮弹出管理对话框或页面，可添加/删除需要索引的根目录。
   - 新增目录时自动启动后台异步索引任务，完成后更新 Profile 索引状态。
5. **默认行为设置 (Default Action)**
   - 点击行为按钮弹出设置对话框，选择双击结果时“打开文件”或“打开所在目录”。
   - 支持 Tab 键快速在两种行为间切换。
6. **操作帮助 (Help Dialog)**
   - 点击“操作方式”按钮打开帮助对话框，展示全局快捷键、搜索操作指南和说明。
7. **搜索结果列表 (Result List)**
   - 匹配结果以列表形式下拉展示，每条结果显示图标、文件名、路径和大小/类型描述。
   - 支持键盘上下导航、Enter 键激活（打开文件/定位目录）。
8. **系统托盘集成 (System Tray)**
   - 在系统托盘驻留图标，支持单击/双击显示或隐藏搜索窗口。
   - 托盘菜单提供“显示搜索窗口”和“退出应用”操作。

### 6. 交互与界面设计 交互与界面设计

交互与界面设计

- **窗口样式**：
  - 磨砂玻璃背景，圆角 8px，阴影 0 10px 30px rgba(0,0,0,0.2)。
  - 宽度自适应输入长度，最小 400px，最大 800px；高度固定 70px，下拉结果区域最大展开至 10 条。
- **交互流程**：
  - 唤出时淡入动画 200ms。
  - 输入防抖 100ms 后查询。
  - 列表动效滑动/渐显 150ms。
  - 关闭方式：Esc。

### 7. 技术方案

- **语言 & 框架**：Python 3.9+，PySide6 构建 GUI。
- **索引 & 查询**：Everything SDK（Everything.dll + ctypes 调用）。
- **配置管理**：YAML via PyYAML。
- **打包部署**：PyInstaller 生成单 exe，捆绑 Everything.dll。
- **快捷键监听**：pywin32 + pynput 全局热键。

### 8. 非功能需求

- **兼容性**：Windows 10/11 x64。
- **性能指标**：冷启动后首个查询 ≤30ms，后续查询 ≤10ms。
- **资源占用**：常驻内存 ≤100MB，CPU 使用率背景态 ≤2%。
- **本地化**：支持中/英文界面切换。

### 9. 安全与隐私

- **权限**：仅读取本地文件系统，没有网络访问，不上传用户数据。
- **卸载**：清理注册表快捷键，无残留服务。

### 