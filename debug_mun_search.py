"""
调试MUN搜索问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_mun_search():
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        from simple_desktop.core.profile_manager import profile_manager
        
        engine = FileSearchEngine(profile_id=0)
        
        # 获取扫描目录
        scan_dirs = profile_manager.get_profile_scan_directories(0)
        print(f"扫描目录: {scan_dirs}")
        
        # 测试1: 直接使用Everything SDK搜索MUN*
        print("\n🔍 步骤1: 直接Everything SDK搜索 'MUN*'")
        sdk_results = engine.everything_sdk.search(
            query="MUN*",
            max_results=50,
            match_case=False,
            match_whole_word=False,
            use_regex=False
        )
        print(f"SDK结果数: {len(sdk_results)}")
        
        found_in_sdk = False
        for result in sdk_results:
            if 'MUN.md' in result.filename:
                print(f"  ✅ SDK找到: {result.filename} - {result.full_path}")
                found_in_sdk = True
                break
        
        if not found_in_sdk:
            print("  ❌ SDK未找到MUN.md")
            # 显示前几个结果
            for i, result in enumerate(sdk_results[:5]):
                print(f"    {i+1}. {result.filename} - {result.full_path}")
        
        # 测试2: 检查过滤器
        print("\n🔍 步骤2: 检查过滤器")
        criteria = FilterCriteria(
            file_types=None,
            include_folders=True,
            scan_directories=scan_dirs
        )
        
        # 手动转换SDK结果
        from simple_desktop.search.models import SearchResult
        search_results = []
        for result in sdk_results:
            search_result = SearchResult.from_everything_result(result)
            search_results.append(search_result)
        
        print(f"转换后结果数: {len(search_results)}")
        
        # 应用过滤器
        from simple_desktop.search.filters import FilterPipeline
        filter_pipeline = FilterPipeline.from_criteria(criteria, engine.file_type_extensions)
        filtered_results = filter_pipeline.apply_filters(search_results)
        
        print(f"过滤后结果数: {len(filtered_results)}")
        
        found_after_filter = False
        for result in filtered_results:
            if 'MUN.md' in result.filename:
                print(f"  ✅ 过滤后找到: {result.filename} - {result.filepath}")
                found_after_filter = True
                break
        
        if not found_after_filter:
            print("  ❌ 过滤后未找到MUN.md")
            # 显示前几个结果
            for i, result in enumerate(filtered_results[:5]):
                print(f"    {i+1}. {result.filename} - {result.filepath}")
        
        # 测试3: 检查目录过滤
        print("\n🔍 步骤3: 检查目录过滤")
        target_path = r"C:\Users\<USER>\Desktop\MUN\MUN.md"
        
        from simple_desktop.search.filters import DirectoryFilter
        dir_filter = DirectoryFilter(scan_dirs)
        
        # 创建模拟结果
        mock_result = SearchResult(
            filename="MUN.md",
            filepath=target_path,
            item_type="file",
            size=897,
            suffix=".md"
        )
        
        should_include = dir_filter.should_include(mock_result)
        print(f"目录过滤器测试: {'✅ 通过' if should_include else '❌ 被过滤'}")
        
        # 详细检查路径匹配
        print("路径匹配详情:")
        target_normalized = os.path.normpath(target_path)
        for scan_dir in scan_dirs:
            scan_normalized = os.path.normpath(scan_dir)
            matches = target_normalized.startswith(scan_normalized)
            print(f"  {scan_normalized} -> {'✅' if matches else '❌'}")
            
        # 测试4: 完整的搜索流程
        print("\n🔍 步骤4: 完整搜索流程")
        results = engine.search("MUN", limit=20)
        print(f"最终结果数: {len(results)}")
        
        for result in results:
            if 'MUN.md' in result.filename:
                print(f"  ✅ 最终找到: {result.filename} - {result.filepath}")
                break
        else:
            print("  ❌ 最终未找到MUN.md")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_mun_search()
