; SimpleDesktop.iss - 完整安装包，包含 Everything 搜索引擎

[Setup]
AppName=Simple Desktop
AppVersion=1.0.0
AppPublisher=Simple Desktop
DefaultDirName={pf}\Simple Desktop
DefaultGroupName=Simple Desktop
UninstallDisplayIcon={app}\SimpleDesktop.exe
Compression=lzma
SolidCompression=yes
OutputBaseFilename=SimpleDesktopSetup
SetupIconFile=ic.ico
OutputDir=SimpleDesktop
AllowNoIcons=yes
DisableProgramGroupPage=no
CreateAppDir=yes
; 需要管理员权限来安装 Everything 服务
PrivilegesRequired=admin
; 添加许可协议页面
LicenseFile=
; 设置安装向导图片
WizardImageFile=
WizardSmallImageFile=



[Files]
; Simple Desktop 主程序文件
Source: "SimpleDesktop.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "ic.ico"; DestDir: "{app}"; Flags: ignoreversion
Source: "install.bat"; DestDir: "{app}"; Flags: ignoreversion

; Everything 搜索引擎文件
Source: "everything.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "Everything64.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "Everything.ini"; DestDir: "{app}"; Flags: ignoreversion onlyifdoesntexist

; 说明文档
Source: "使用说明.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Simple Desktop"; Filename: "{app}\SimpleDesktop.exe"; IconFilename: "{app}\ic.ico"
Name: "{commondesktop}\Simple Desktop"; Filename: "{app}\SimpleDesktop.exe"; IconFilename: "{app}\ic.ico"; Tasks: desktopicon
Name: "{group}\使用说明"; Filename: "{app}\使用说明.txt"
Name: "{group}\卸载 Simple Desktop"; Filename: "{uninstallexe}"

[Tasks]
Name: "desktopicon"; Description: "在桌面创建图标"; GroupDescription: "附加任务"
Name: "startupicon"; Description: "开机自动启动 Simple Desktop"; GroupDescription: "启动选项"

[Run]
; 静默安装 Everything 服务（用户无感知）
Filename: "{app}\everything.exe"; Parameters: "-install-service"; Flags: runhidden waituntilterminated
; 添加 Simple Desktop 开机自启（如果用户选择）
Filename: "reg"; Parameters: "add HKCU\Software\Microsoft\Windows\CurrentVersion\Run /v ""SimpleDesktop"" /t REG_SZ /d ""{app}\SimpleDesktop.exe"" /f"; Flags: runhidden; Tasks: startupicon
; 运行 Simple Desktop（安装后）
Filename: "{app}\SimpleDesktop.exe"; Description: "立即运行 Simple Desktop"; Flags: nowait postinstall skipifsilent

[Registry]
; 如果用户选择开机自启，添加注册表项
Root: HKCU; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "SimpleDesktop"; ValueData: "{app}\SimpleDesktop.exe"; Tasks: startupicon

[UninstallRun]
; 静默卸载 Everything 服务
Filename: "{app}\everything.exe"; Parameters: "-uninstall-service"; Flags: runhidden
; 卸载时删除注册表启动项
Filename: "reg"; Parameters: "delete HKCU\Software\Microsoft\Windows\CurrentVersion\Run /v ""SimpleDesktop"" /f"; Flags: runhidden

[UninstallDelete]
; 删除可能生成的配置文件
Type: files; Name: "{app}\Everything.ini"
Type: files; Name: "{app}\Everything.db"
