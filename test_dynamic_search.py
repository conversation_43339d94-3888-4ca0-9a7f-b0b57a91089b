#!/usr/bin/env python3
"""
测试动态搜索功能
验证Everything SDK能够返回所有可能的结果，而不受固定数量限制
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_dynamic_vs_fixed_search():
    """对比动态搜索和固定限制搜索的结果"""
    print("🔍 对比动态搜索 vs 固定限制搜索")
    print("=" * 70)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        if not sdk.is_everything_running():
            print("❌ Everything未运行")
            return False
        
        # 测试不同的查询
        test_queries = [
            {
                "query": "KAN",
                "description": "短关键词搜索"
            },
            {
                "query": "pdf",
                "description": "常见扩展名搜索"
            },
            {
                "query": "test",
                "description": "通用关键词搜索"
            }
        ]
        
        for test_case in test_queries:
            query = test_case["query"]
            description = test_case["description"]
            
            print(f"\n🎯 测试: {description}")
            print(f"   查询: '{query}'")
            
            # 1. 固定限制搜索（传统方法）
            print(f"\n   📊 固定限制搜索 (max_results=1000):")
            start_time = time.time()
            fixed_results = sdk.search(query, max_results=1000)
            fixed_time = time.time() - start_time
            
            print(f"      结果数量: {len(fixed_results)}")
            print(f"      搜索时间: {fixed_time:.3f}秒")
            
            # 2. 动态搜索（新方法）
            print(f"\n   🚀 动态搜索 (获取所有结果):")
            start_time = time.time()
            dynamic_results = sdk.search_all_results(query, max_safe_limit=15000)
            dynamic_time = time.time() - start_time
            
            print(f"      结果数量: {len(dynamic_results)}")
            print(f"      搜索时间: {dynamic_time:.3f}秒")
            
            # 3. 对比分析
            print(f"\n   📈 对比分析:")
            if len(dynamic_results) > len(fixed_results):
                additional_results = len(dynamic_results) - len(fixed_results)
                print(f"      ✅ 动态搜索多找到 {additional_results} 个结果")
                
                # 显示一些额外找到的结果
                if additional_results > 0:
                    print(f"      额外找到的结果示例:")
                    extra_results = dynamic_results[len(fixed_results):]
                    for i, result in enumerate(extra_results[:3]):
                        print(f"        {i+1}. {result.filename}")
                        print(f"           路径: {result.full_path}")
            elif len(dynamic_results) == len(fixed_results):
                print(f"      ✅ 两种方法找到相同数量的结果")
            else:
                print(f"      ⚠️ 动态搜索结果较少（可能受安全限制影响）")
            
            # 性能对比
            if dynamic_time <= fixed_time * 1.5:
                print(f"      ✅ 动态搜索性能良好")
            else:
                print(f"      ⚠️ 动态搜索耗时较长，但获得了更完整的结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_kan_search_with_dynamic():
    """专门测试KAN搜索的动态效果"""
    print(f"\n🎯 KAN搜索动态效果测试")
    print("=" * 70)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试KAN和EEGKAN搜索
        queries = ["KAN", "EEGKAN"]
        
        for query in queries:
            print(f"\n🔍 测试查询: '{query}'")
            
            start_time = time.time()
            results = engine.search(
                query=query,
                limit=50,
                file_types=None,
                include_folders=True,
                global_search=False
            )
            search_time = time.time() - start_time
            
            print(f"   搜索结果数量: {len(results)}")
            print(f"   搜索时间: {search_time:.3f}秒")
            
            if results:
                print(f"   找到的文件:")
                for result in results:
                    print(f"     - {result.filename}")
                    print(f"       路径: {result.filepath}")
                    print(f"       类型: {result.item_type}")
                
                # 验证预期结果
                if query == "KAN":
                    expected_files = ["KAN.pdf", "EEGKAN.pdf"]
                    found_files = [r.filename for r in results]
                    
                    for expected in expected_files:
                        if expected in found_files:
                            print(f"     ✅ 找到预期文件: {expected}")
                        else:
                            print(f"     ❌ 未找到预期文件: {expected}")
                
                elif query == "EEGKAN":
                    if any("EEGKAN.pdf" in r.filename for r in results):
                        print(f"     ✅ 找到EEGKAN.pdf")
                    else:
                        print(f"     ❌ 未找到EEGKAN.pdf")
            else:
                print(f"   ❌ 没有找到任何结果")
        
        return True
        
    except Exception as e:
        print(f"❌ KAN搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_probe_mechanism():
    """测试探测机制"""
    print(f"\n🔬 测试探测机制")
    print("=" * 70)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试不同查询的探测结果
        test_queries = ["KAN", "pdf", "test", "a"]
        
        for query in test_queries:
            print(f"\n🔍 探测查询: '{query}'")
            
            # 使用探测方法获取实际结果数量
            actual_count = sdk._probe_result_count(query, False, False, False)
            print(f"   探测到的结果数量: {actual_count}")
            
            # 使用传统搜索验证
            traditional_results = sdk.search(query, max_results=1000)
            print(f"   传统搜索结果数量: {len(traditional_results)}")
            
            # 分析
            if actual_count > len(traditional_results):
                print(f"   ✅ 探测发现了更多结果 (+{actual_count - len(traditional_results)})")
            elif actual_count == len(traditional_results):
                print(f"   ✅ 探测结果与传统搜索一致")
            else:
                print(f"   ⚠️ 探测结果少于传统搜索")
        
        return True
        
    except Exception as e:
        print(f"❌ 探测机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 动态搜索功能测试")
    print("验证Everything SDK动态获取所有结果的能力")
    
    # 运行测试
    dynamic_test = test_dynamic_vs_fixed_search()
    kan_test = test_kan_search_with_dynamic()
    probe_test = test_probe_mechanism()
    
    print(f"\n" + "=" * 80)
    print("📊 测试结果:")
    print(f"   动态搜索对比: {'✅ 通过' if dynamic_test else '❌ 失败'}")
    print(f"   KAN搜索测试: {'✅ 通过' if kan_test else '❌ 失败'}")
    print(f"   探测机制测试: {'✅ 通过' if probe_test else '❌ 失败'}")
    
    if dynamic_test and kan_test and probe_test:
        print("\n🎉 动态搜索功能实现成功！")
        print("\n🔧 实现的关键特性:")
        print("   - 探测机制：先获取实际结果数量")
        print("   - 动态获取：根据实际数量获取所有结果")
        print("   - 安全限制：防止系统过载")
        print("   - 性能优化：进度显示和批量处理")
        print("   - 完整覆盖：不再受固定数量限制约束")
    else:
        print("\n⚠️ 部分功能需要进一步优化")

if __name__ == "__main__":
    main()
