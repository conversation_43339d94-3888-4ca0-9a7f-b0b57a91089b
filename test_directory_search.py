#!/usr/bin/env python3
"""
测试目录限制搜索功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_directory_search():
    """测试目录限制搜索功能"""
    print("=== 测试目录限制搜索功能 ===")
    
    try:
        from simple_desktop.core.config import config_manager
        from simple_desktop.core.profile_manager import profile_manager
        from simple_desktop.search.engine import FileSearchEngine
        
        # 测试Profile 1（应该没有配置目录）
        print("\n1. 测试未配置目录的Profile...")
        engine1 = FileSearchEngine(profile_id=1)
        
        print(f"   Profile 1 是否有扫描目录: {engine1.has_scan_directories()}")
        print(f"   Profile 1 扫描目录: {engine1.get_scan_directories()}")
        
        # 尝试搜索（应该返回空结果）
        results1 = engine1.search("test", limit=5)
        print(f"   搜索'test'结果数量: {len(results1)}")
        
        # 测试Profile 0（默认桌面，应该有配置目录）
        print("\n2. 测试已配置目录的Profile...")
        engine0 = FileSearchEngine(profile_id=0)
        
        print(f"   Profile 0 是否有扫描目录: {engine0.has_scan_directories()}")
        scan_dirs = engine0.get_scan_directories()
        print(f"   Profile 0 扫描目录数量: {len(scan_dirs)}")
        for i, dir_path in enumerate(scan_dirs):
            print(f"     {i+1}. {dir_path}")
        
        # 获取扫描目录详细信息
        scan_info = engine0.get_scan_directories_info()
        print(f"   有效目录: {len(scan_info['valid_directories'])}")
        print(f"   无效目录: {len(scan_info['invalid_directories'])}")
        
        if scan_info['invalid_directories']:
            print("   无效目录列表:")
            for invalid_dir in scan_info['invalid_directories']:
                print(f"     - {invalid_dir}")
        
        # 尝试搜索
        if engine0.has_scan_directories():
            results0 = engine0.search("txt", limit=5)
            print(f"   搜索'txt'结果数量: {len(results0)}")
            
            for i, result in enumerate(results0[:3]):
                print(f"     {i+1}. {result.filename} - {result.filepath}")
                
                # 验证结果是否在扫描目录中
                is_in_dirs = engine0._is_in_scan_directories(result.filepath, scan_dirs)
                print(f"        在扫描目录中: {is_in_dirs}")
        
        # 测试添加新目录到Profile 1
        print("\n3. 测试添加目录到Profile 1...")
        test_directory = str(Path.home() / "Desktop")
        if os.path.exists(test_directory):
            success = profile_manager.add_directory_to_profile(test_directory, 1)
            print(f"   添加目录 {test_directory}: {'成功' if success else '失败'}")
            
            if success:
                # 重新创建搜索引擎以获取新配置
                engine1_new = FileSearchEngine(profile_id=1)
                print(f"   Profile 1 现在有扫描目录: {engine1_new.has_scan_directories()}")
                
                # 尝试搜索
                results1_new = engine1_new.search("txt", limit=3)
                print(f"   搜索'txt'结果数量: {len(results1_new)}")
                
                # 清理：移除测试目录
                profile_manager.remove_directory_from_profile(test_directory, 1)
                print(f"   已清理测试目录")
        else:
            print(f"   测试目录不存在: {test_directory}")
        
        print("\n✓ 目录限制搜索功能测试完成")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_everything_query_building():
    """测试Everything查询构建"""
    print("\n=== 测试Everything查询构建 ===")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试目录查询构建
        test_directories = [
            "C:\\Users\\<USER>\\Desktop",
            "D:\\Documents"
        ]
        
        # 测试基础查询
        query1 = engine._build_everything_query_with_directories(
            "test", test_directories
        )
        print(f"基础查询: {query1}")
        
        # 测试带文件类型的查询
        query2 = engine._build_everything_query_with_directories(
            "document", test_directories, file_types=["documents"]
        )
        print(f"文档类型查询: {query2}")
        
        # 测试带后缀的查询
        query3 = engine._build_everything_query_with_directories(
            "file ext:txt", test_directories
        )
        print(f"后缀查询: {query3}")
        
        print("✓ 查询构建测试完成")
        
    except Exception as e:
        print(f"✗ 查询构建测试失败: {e}")

def main():
    """主测试函数"""
    print("Simple Desktop 目录限制搜索测试")
    print("=" * 50)
    
    test_directory_search()
    test_everything_query_building()
    
    print("\n" + "=" * 50)
    print("测试完成！")

if __name__ == "__main__":
    main()
