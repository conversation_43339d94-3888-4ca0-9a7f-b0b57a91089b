"""
Windows资源管理器右键菜单集成模块
"""

import os
import sys
import winreg
import subprocess
import tempfile
import ctypes
from pathlib import Path
from typing import Optional, List, Dict, Any
import json
import time


class ContextMenuManager:
    """Windows右键菜单管理器"""
    
    def __init__(self):
        """初始化右键菜单管理器"""
        self.app_name = "SimpleDesktop"
        self.menu_text = "添加到SimpleDesktop"
        self.registry_key = r"Directory\shell\SimpleDesktop"
        self.command_key = r"Directory\shell\SimpleDesktop\command"

        # 获取当前脚本路径
        self.script_dir = Path(__file__).parent.parent
        self.handler_script = self.script_dir / "scripts" / "context_menu_handler.py"

        # 进程检测相关
        self.process_name = "python.exe"  # 或者更具体的进程名
        self.app_identifier = "simple_desktop"  # 用于识别我们的进程

    def is_admin(self) -> bool:
        """检查是否具有管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except Exception:
            return False

    def run_as_admin(self, command: str) -> bool:
        """以管理员权限运行命令"""
        try:
            # 使用ShellExecute以管理员权限运行
            result = ctypes.windll.shell32.ShellExecuteW(
                None,
                "runas",
                "cmd.exe",
                f"/c {command}",
                None,
                1  # SW_SHOWNORMAL
            )
            # 如果返回值大于32，表示成功
            return result > 32
        except Exception as e:
            print(f"Failed to run as admin: {e}")
            return False
        
    def is_app_running(self) -> bool:
        """检测SimpleDesktop应用程序是否正在运行"""
        try:
            import psutil
            
            # 检查是否有运行中的SimpleDesktop进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and any(self.app_identifier in str(arg).lower() for arg in cmdline):
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            return False
            
        except ImportError:
            # 如果没有psutil，使用备用方法
            return self._is_app_running_fallback()
    
    def _is_app_running_fallback(self) -> bool:
        """备用的进程检测方法"""
        try:
            # 使用tasklist命令检测
            result = subprocess.run(
                ['tasklist', '/FI', f'IMAGENAME eq {self.process_name}'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            # 检查输出中是否包含我们的标识
            return self.app_identifier in result.stdout.lower()
            
        except Exception:
            return False
    
    def register_context_menu(self) -> bool:
        """注册右键菜单项"""
        try:
            # 检查管理员权限
            if not self.is_admin():
                print("Warning: Administrator privileges required for registry modification")
                print("Context menu registration may fail without admin rights")

            # 确保处理脚本存在
            if not self._ensure_handler_script():
                print("Failed to create handler script")
                return False

            # 尝试创建注册表项
            try:
                # 创建主键
                with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, self.registry_key) as key:
                    # 设置菜单显示文本
                    winreg.SetValueEx(key, "", 0, winreg.REG_SZ, self.menu_text)

                    # 设置图标（可选）
                    # winreg.SetValueEx(key, "Icon", 0, winreg.REG_SZ, icon_path)

                # 创建命令子键
                python_exe = sys.executable
                command = f'"{python_exe}" "{self.handler_script}" "%1"'

                with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, self.command_key) as key:
                    winreg.SetValueEx(key, "", 0, winreg.REG_SZ, command)

                print(f"Context menu registered successfully")
                print(f"Command: {command}")
                return True

            except PermissionError as e:
                print(f"Permission denied: {e}")
                print("Try running the application as administrator")
                return False

        except Exception as e:
            print(f"Failed to register context menu: {e}")
            return False
    
    def unregister_context_menu(self) -> bool:
        """注销右键菜单项"""
        try:
            # 尝试删除注册表项
            try:
                # 删除命令子键
                try:
                    winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, self.command_key)
                except FileNotFoundError:
                    pass

                # 删除主键
                try:
                    winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, self.registry_key)
                except FileNotFoundError:
                    pass

                print("Context menu unregistered successfully")
                return True

            except PermissionError as e:
                print(f"Permission denied during unregistration: {e}")
                print("Some registry entries may remain")
                return False

        except Exception as e:
            print(f"Failed to unregister context menu: {e}")
            return False
    
    def is_context_menu_registered(self) -> bool:
        """检查右键菜单是否已注册"""
        try:
            with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, self.registry_key):
                return True
        except FileNotFoundError:
            return False
        except Exception:
            return False
    
    def _ensure_handler_script(self) -> bool:
        """确保处理脚本存在"""
        try:
            # 创建scripts目录
            scripts_dir = self.script_dir / "scripts"
            scripts_dir.mkdir(exist_ok=True)
            
            # 创建处理脚本
            handler_content = self._get_handler_script_content()
            
            with open(self.handler_script, 'w', encoding='utf-8') as f:
                f.write(handler_content)
            
            return True
            
        except Exception as e:
            print(f"Failed to create handler script: {e}")
            return False
    
    def _get_handler_script_content(self) -> str:
        """获取处理脚本内容"""
        return '''"""
SimpleDesktop右键菜单处理脚本
"""

import sys
import os
import json
import tempfile
import subprocess
from pathlib import Path

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: context_menu_handler.py <folder_path>")
        return
    
    folder_path = sys.argv[1]
    
    # 验证路径
    if not os.path.isdir(folder_path):
        print(f"Invalid folder path: {folder_path}")
        return
    
    print(f"Processing folder: {folder_path}")
    
    try:
        # 启动Profile选择对话框
        script_dir = Path(__file__).parent.parent
        dialog_script = script_dir / "scripts" / "profile_selector.py"
        
        # 使用subprocess启动对话框
        result = subprocess.run([
            sys.executable, 
            str(dialog_script), 
            folder_path
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("Folder added successfully")
        else:
            print(f"Failed to add folder: {result.stderr}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
'''
    
    def update_context_menu_visibility(self) -> bool:
        """根据应用程序运行状态更新右键菜单可见性"""
        is_running = self.is_app_running()
        is_registered = self.is_context_menu_registered()
        
        if is_running and not is_registered:
            # 应用程序运行中但菜单未注册，注册菜单
            return self.register_context_menu()
        elif not is_running and is_registered:
            # 应用程序未运行但菜单已注册，注销菜单
            return self.unregister_context_menu()
        
        return True
    
    def start_monitoring(self):
        """开始监控应用程序状态（可在后台线程中运行）"""
        import threading
        import time
        
        def monitor_loop():
            while True:
                try:
                    self.update_context_menu_visibility()
                    time.sleep(5)  # 每5秒检查一次
                except Exception as e:
                    print(f"Monitor error: {e}")
                    time.sleep(10)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        return monitor_thread


# 全局实例
context_menu_manager = ContextMenuManager()
