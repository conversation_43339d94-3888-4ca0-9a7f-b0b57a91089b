#!/usr/bin/env python3
"""
诊断Ctrl+数字键热键注册问题
"""

import sys
import ctypes
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Windows API常量
MOD_CONTROL = 0x0002
VK_0 = 0x30

def test_hotkey_registration():
    """测试热键注册"""
    print("🔍 测试Ctrl+数字键热键注册")
    print("=" * 50)
    
    user32 = ctypes.windll.user32
    
    failed_keys = []
    successful_keys = []
    
    for i in range(10):
        hotkey_id = 4000 + i  # 使用新的ID范围
        vk_code = VK_0 + i
        
        print(f"测试 Ctrl+{i}...")
        
        result = user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
        
        if result:
            successful_keys.append(i)
            print(f"   ✅ Ctrl+{i} 注册成功")
            # 立即注销
            user32.UnregisterHotKey(None, hotkey_id)
        else:
            failed_keys.append(i)
            error_code = ctypes.GetLastError()
            print(f"   ❌ Ctrl+{i} 注册失败")
            print(f"      错误代码: {error_code}")
            
            if error_code == 1409:
                print("      原因: 热键已被其他应用程序注册")
            elif error_code == 5:
                print("      原因: 权限不足")
            elif error_code == 87:
                print("      原因: 参数无效")
            else:
                print(f"      原因: 未知错误")
    
    print(f"\n📊 测试结果:")
    print(f"   成功: {len(successful_keys)}/10")
    print(f"   失败: {len(failed_keys)}/10")
    
    if successful_keys:
        print(f"   成功的键: {successful_keys}")
    
    if failed_keys:
        print(f"   失败的键: {failed_keys}")
        
        print(f"\n💡 解决建议:")
        if 1409 in [ctypes.GetLastError() for _ in failed_keys]:
            print("   - 某些Ctrl+数字键可能被其他应用占用")
            print("   - 考虑使用替代的热键组合")
        if 5 in [ctypes.GetLastError() for _ in failed_keys]:
            print("   - 尝试以管理员身份运行应用程序")
    
    return len(failed_keys) == 0

def test_current_hotkey_manager():
    """测试当前的热键管理器"""
    print("\n🧪 测试当前热键管理器")
    print("=" * 50)
    
    try:
        from simple_desktop.core.hotkey import HotkeyManager
        
        # 创建热键管理器实例
        manager = HotkeyManager()
        
        print("✅ 热键管理器创建成功")
        
        # 设置测试回调
        profile_switches = []
        
        def test_callback(profile_id):
            profile_switches.append(profile_id)
            print(f"[TEST] Profile切换回调: {profile_id}")
        
        manager.set_profile_switch_callback(test_callback)
        
        # 尝试启动监听
        success = manager.start_listening()
        
        if success:
            print("✅ 热键监听启动成功")
            
            # 检查注册的热键
            registered_count = len(manager.registered_hotkeys)
            print(f"   注册的热键数量: {registered_count}")
            
            if registered_count > 0:
                print("   注册的热键ID:")
                for hotkey_id in sorted(manager.registered_hotkeys):
                    if 1000 <= hotkey_id <= 1009:
                        profile_id = hotkey_id - 1000
                        print(f"     {hotkey_id} -> Ctrl+{profile_id}")
            
            # 停止监听
            manager.stop_listening()
            print("✅ 热键监听已停止")
            
            return registered_count > 0
        else:
            print("❌ 热键监听启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_profile_manager():
    """检查Profile管理器"""
    print("\n🔍 检查Profile管理器")
    print("=" * 50)
    
    try:
        from simple_desktop.core.profile_manager import profile_manager
        
        print("✅ Profile管理器导入成功")
        
        # 检查当前Profile
        current_profile = profile_manager.current_profile_id
        print(f"   当前Profile ID: {current_profile}")
        
        # 测试Profile切换
        print("\n测试Profile切换:")
        
        for i in range(3):  # 测试前3个Profile
            try:
                profile_name = profile_manager.get_profile_name(i)
                print(f"   Profile {i}: {profile_name}")
                
                # 尝试切换
                profile_manager.switch_profile(i)
                new_current = profile_manager.current_profile_id
                
                if new_current == i:
                    print(f"     ✅ 切换到Profile {i} 成功")
                else:
                    print(f"     ❌ 切换到Profile {i} 失败")
                    
            except Exception as e:
                print(f"     ❌ Profile {i} 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Profile管理器检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - Ctrl+数字键热键诊断")
    print("=" * 80)
    
    # 执行诊断步骤
    tests = [
        ("热键注册测试", test_hotkey_registration),
        ("当前热键管理器测试", test_current_hotkey_manager),
        ("Profile管理器检查", check_profile_manager),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 热键诊断完成！")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试都通过！")
        print("热键功能应该正常工作")
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 项测试失败")
        print("需要进一步修复")

if __name__ == "__main__":
    main()
