@echo off
chcp 65001 >nul
echo ================================================
echo Simple Desktop 新构建版本测试
echo ================================================
echo.

echo 检查构建文件...
if exist "dist\SimpleDesktop.exe" (
    echo ✓ SimpleDesktop.exe 存在
    for %%f in (dist\SimpleDesktop.exe) do echo   大小: %%~zf 字节 (约 %%~zf/1048576 MB)
) else (
    echo ✗ SimpleDesktop.exe 不存在
    goto :end
)

if exist "dist\Everything64.dll" (
    echo ✓ Everything64.dll 存在
) else (
    echo ✗ Everything64.dll 不存在
)

if exist "dist\ic.ico" (
    echo ✓ ic.ico 存在
) else (
    echo ✗ ic.ico 不存在
)

echo.
echo 检查发布包...
if exist "release\SimpleDesktop_v1.0.0_20250729_171332.zip" (
    echo ✓ 最新发布包存在
    for %%f in (release\SimpleDesktop_v1.0.0_20250729_171332.zip) do echo   大小: %%~zf 字节 (约 %%~zf/1048576 MB)
) else (
    echo ⚠ 最新发布包不存在
)

echo.
echo 更新内容:
echo - ✓ 调整了操作方式按钮的内边距 (padding: 4px 6px)
echo - ✓ 增加了操作方式按钮的宽度 (70px)
echo - ✓ 优化了按钮文字显示效果
echo.

echo 测试选项:
echo 1. 启动程序测试
echo 2. 查看发布包内容
echo 3. 退出
echo.
set /p choice="请选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 启动程序进行测试...
    echo 请检查操作方式按钮的显示效果
    echo.
    start "" "dist\SimpleDesktop.exe"
    echo 程序已启动，请手动测试后关闭
) else if "%choice%"=="2" (
    echo.
    echo 打开发布包目录...
    start "" "release\SimpleDesktop_v1.0.0_20250729_171332"
) else (
    echo 退出测试
)

:end
echo.
echo 测试完成！
pause
