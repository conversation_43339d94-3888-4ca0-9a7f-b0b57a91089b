#!/usr/bin/env python3
"""
Simple Desktop 高级打包脚本
支持多种打包选项和自动化部署
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path
import zipfile
import datetime

class SimpleDesktopBuilder:
    def __init__(self):
        self.project_root = Path(".")
        self.dist_dir = Path("dist")
        self.build_dir = Path("build")
        self.icon_path = Path("D:/withEverything/ic.ico")
        
    def check_dependencies(self):
        """检查构建依赖"""
        print("检查构建依赖...")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"✓ PyInstaller: {PyInstaller.__version__}")
        except ImportError:
            print("安装 PyInstaller...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        
        # 检查图标文件
        if not self.icon_path.exists():
            print(f"✗ 图标文件不存在: {self.icon_path}")
            return False
            
        # 检查主要源文件
        if not Path("main.py").exists():
            print("✗ main.py 不存在")
            return False
            
        if not Path("simple_desktop").exists():
            print("✗ simple_desktop 目录不存在")
            return False
            
        print("✓ 所有依赖检查通过")
        return True
    
    def clean_build(self):
        """清理构建目录"""
        print("清理构建目录...")
        
        for dir_path in [self.build_dir, self.dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"✓ 清理: {dir_path}")
        
        # 清理spec文件
        for spec_file in self.project_root.glob("*.spec"):
            if spec_file.name != "SimpleDesktop.spec":  # 保留我们的spec文件
                spec_file.unlink()
                print(f"✓ 清理: {spec_file}")
    
    def build_onefile(self):
        """构建单文件版本"""
        print("构建单文件版本...")
        
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name=SimpleDesktop",
            f"--icon={self.icon_path}",
            "--add-data=simple_desktop;simple_desktop",
            "--add-data=Everything-SDK;Everything-SDK",
            "--hidden-import=PySide6.QtCore",
            "--hidden-import=PySide6.QtWidgets",
            "--hidden-import=PySide6.QtGui",
            "--hidden-import=win32api",
            "--hidden-import=win32con",
            "--hidden-import=win32gui",
            "--hidden-import=win32process",
            "--collect-all=PySide6",
            "--exclude-module=tkinter",
            "--exclude-module=matplotlib",
            "main.py"
        ]
        
        return self._run_pyinstaller(cmd)
    
    def build_onedir(self):
        """构建目录版本"""
        print("构建目录版本...")
        
        cmd = [
            "pyinstaller",
            "--onedir",
            "--windowed",
            "--name=SimpleDesktop",
            f"--icon={self.icon_path}",
            "--add-data=simple_desktop;simple_desktop",
            "--add-data=Everything-SDK;Everything-SDK",
            "--hidden-import=PySide6.QtCore",
            "--hidden-import=PySide6.QtWidgets",
            "--hidden-import=PySide6.QtGui",
            "--hidden-import=win32api",
            "--hidden-import=win32con",
            "--hidden-import=win32gui",
            "--hidden-import=win32process",
            "--collect-all=PySide6",
            "--exclude-module=tkinter",
            "--exclude-module=matplotlib",
            "main.py"
        ]
        
        return self._run_pyinstaller(cmd)
    
    def build_with_spec(self):
        """使用spec文件构建"""
        print("使用spec文件构建...")
        
        if not Path("SimpleDesktop.spec").exists():
            print("✗ SimpleDesktop.spec 文件不存在")
            return False
            
        cmd = ["pyinstaller", "SimpleDesktop.spec"]
        return self._run_pyinstaller(cmd)
    
    def _run_pyinstaller(self, cmd):
        """运行PyInstaller命令"""
        try:
            subprocess.check_call(cmd)
            print("✓ 构建成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ 构建失败: {e}")
            return False
    
    def copy_dependencies(self):
        """复制依赖文件"""
        print("复制依赖文件...")
        
        if not self.dist_dir.exists():
            print("✗ dist目录不存在")
            return
        
        # 查找可执行文件目录
        exe_dir = None
        if (self.dist_dir / "SimpleDesktop.exe").exists():
            exe_dir = self.dist_dir
        elif (self.dist_dir / "SimpleDesktop" / "SimpleDesktop.exe").exists():
            exe_dir = self.dist_dir / "SimpleDesktop"
        
        if not exe_dir:
            print("✗ 找不到可执行文件")
            return
        
        # 复制Everything DLL
        everything_dll = Path("Everything-SDK/dll/Everything64.dll")
        if everything_dll.exists():
            shutil.copy2(everything_dll, exe_dir / "Everything64.dll")
            print("✓ 复制 Everything64.dll")
        
        # 复制图标文件
        if self.icon_path.exists():
            shutil.copy2(self.icon_path, exe_dir / "ic.ico")
            print("✓ 复制图标文件")
    
    def create_installer(self):
        """创建安装程序"""
        print("创建安装程序...")
        
        installer_script = f"""@echo off
chcp 65001 >nul
echo Simple Desktop 安装程序
echo 版本: 1.0.0
echo 构建时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
echo.

set "INSTALL_DIR=%PROGRAMFILES%\\SimpleDesktop"
set "DESKTOP_SHORTCUT=%USERPROFILE%\\Desktop\\SimpleDesktop.lnk"
set "STARTMENU_SHORTCUT=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\SimpleDesktop.lnk"

echo 正在安装到: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 复制程序文件...
xcopy /E /I /Y "SimpleDesktop\\*" "%INSTALL_DIR%\\"

echo 创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SimpleDesktop.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\\ic.ico'; $Shortcut.Save()"

echo 创建开始菜单快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SimpleDesktop.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\\ic.ico'; $Shortcut.Save()"

echo.
echo ✓ 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
pause
"""
        
        with open(self.dist_dir / "install.bat", "w", encoding="utf-8") as f:
            f.write(installer_script)
        print("✓ 创建安装脚本")
    
    def create_portable_package(self):
        """创建便携版压缩包"""
        print("创建便携版压缩包...")
        
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        zip_name = f"SimpleDesktop_Portable_{timestamp}.zip"
        zip_path = self.dist_dir / zip_name
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加可执行文件目录
            exe_dir = self.dist_dir / "SimpleDesktop"
            if exe_dir.exists():
                for file_path in exe_dir.rglob("*"):
                    if file_path.is_file():
                        arcname = file_path.relative_to(self.dist_dir)
                        zipf.write(file_path, arcname)
        
        print(f"✓ 创建便携版: {zip_name}")
    
    def build(self, build_type="onefile"):
        """执行完整构建流程"""
        print("=" * 60)
        print("Simple Desktop 构建程序")
        print("=" * 60)
        
        if not self.check_dependencies():
            return False
        
        self.clean_build()
        
        # 根据类型选择构建方法
        if build_type == "onefile":
            success = self.build_onefile()
        elif build_type == "onedir":
            success = self.build_onedir()
        elif build_type == "spec":
            success = self.build_with_spec()
        else:
            print(f"✗ 未知的构建类型: {build_type}")
            return False
        
        if success:
            self.copy_dependencies()
            self.create_installer()
            
            if build_type == "onedir":
                self.create_portable_package()
            
            print("\n" + "=" * 60)
            print("构建完成！")
            print("=" * 60)
            print("输出文件:")
            for file_path in self.dist_dir.rglob("*"):
                if file_path.is_file():
                    print(f"  - {file_path.relative_to(self.project_root)}")
        
        return success

def main():
    parser = argparse.ArgumentParser(description="Simple Desktop 构建程序")
    parser.add_argument(
        "--type", 
        choices=["onefile", "onedir", "spec"],
        default="onefile",
        help="构建类型 (默认: onefile)"
    )
    
    args = parser.parse_args()
    
    builder = SimpleDesktopBuilder()
    success = builder.build(args.type)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
