#!/usr/bin/env python3
"""
调试搜索功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_search():
    """调试搜索功能"""
    print("=== 调试搜索功能 ===")
    
    try:
        from simple_desktop.core.config import config_manager
        from simple_desktop.core.profile_manager import profile_manager
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        # 测试Everything SDK直接搜索
        print("\n1. 测试Everything SDK直接搜索...")
        sdk = get_everything_sdk()
        
        # 测试简单搜索
        print("   测试搜索'desktop.ini'...")
        direct_results = sdk.search("desktop.ini", max_results=10)
        print(f"   直接搜索结果数量: {len(direct_results)}")
        
        for i, result in enumerate(direct_results[:5]):
            print(f"     {i+1}. {result.filename} - {result.full_path}")
        
        # 测试桌面路径搜索
        desktop_path = str(Path.home() / "Desktop")
        print(f"\n   测试桌面路径搜索: {desktop_path}")
        
        # 使用Everything路径语法
        desktop_query = f'"{desktop_path}\\" desktop.ini'
        print(f"   查询语句: {desktop_query}")
        
        desktop_results = sdk.search(desktop_query, max_results=10)
        print(f"   桌面路径搜索结果数量: {len(desktop_results)}")
        
        for i, result in enumerate(desktop_results):
            print(f"     {i+1}. {result.filename} - {result.full_path}")
        
        # 测试Profile 0搜索引擎
        print("\n2. 测试Profile 0搜索引擎...")
        engine = FileSearchEngine(profile_id=0)
        
        scan_dirs = engine.get_scan_directories()
        print(f"   扫描目录: {scan_dirs}")
        
        # 测试搜索桌面文件
        print("\n   搜索'desktop.ini'...")
        engine_results = engine.search("desktop.ini", limit=10)
        print(f"   搜索引擎结果数量: {len(engine_results)}")
        
        # 测试搜索常见文件
        print("\n   搜索'txt'...")
        txt_results = engine.search("txt", limit=5)
        print(f"   txt搜索结果数量: {len(txt_results)}")
        
        for i, result in enumerate(txt_results):
            print(f"     {i+1}. {result.filename} - {result.filepath}")
        
        # 测试路径验证逻辑
        print("\n3. 测试路径验证逻辑...")
        test_paths = [
            str(Path.home() / "Desktop" / "test.txt"),
            str(Path.home() / "Documents" / "test.doc"),
            "C:\\Windows\\System32\\test.exe"
        ]
        
        for test_path in test_paths:
            is_in_dirs = engine._is_in_scan_directories(test_path, scan_dirs)
            print(f"   {test_path}: {is_in_dirs}")
        
        print("\n✓ 搜索调试完成")
        
    except Exception as e:
        print(f"✗ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def test_everything_syntax():
    """测试Everything查询语法"""
    print("\n=== 测试Everything查询语法 ===")
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        desktop_path = str(Path.home() / "Desktop")
        
        # 测试不同的路径语法
        test_queries = [
            f'"{desktop_path}"',  # 基本路径
            f'"{desktop_path}\\"',  # 路径+反斜杠
            f'path:"{desktop_path}"',  # path:语法
            f'"{desktop_path}\\" *.txt',  # 路径+通配符
            f'"{desktop_path}\\" ext:txt',  # 路径+扩展名
        ]
        
        for query in test_queries:
            print(f"\n测试查询: {query}")
            try:
                results = sdk.search(query, max_results=5)
                print(f"  结果数量: {len(results)}")
                for i, result in enumerate(results[:3]):
                    print(f"    {i+1}. {result.filename}")
            except Exception as e:
                print(f"  查询失败: {e}")
        
        print("\n✓ 语法测试完成")
        
    except Exception as e:
        print(f"✗ 语法测试失败: {e}")

def main():
    """主函数"""
    print("Simple Desktop 搜索功能调试")
    print("=" * 50)
    
    debug_search()
    test_everything_syntax()
    
    print("\n" + "=" * 50)
    print("调试完成！")

if __name__ == "__main__":
    main()
