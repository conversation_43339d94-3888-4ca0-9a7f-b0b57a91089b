"""
直接测试Everything的搜索行为
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_everything_queries():
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试各种查询模式
        queries = [
            "MUN",
            "MUN.md",
            "*MUN*",
            "MUN*",
            "*.md",
            "MUN *.md",
            "file:MUN.md",
            "filename:MUN.md",
        ]
        
        for query in queries:
            print(f"\n🔍 测试查询: '{query}'")
            results = sdk.search(
                query=query,
                max_results=20,
                match_case=False,
                match_whole_word=False,
                use_regex=False
            )
            print(f"结果数: {len(results)}")
            
            found_md = False
            for result in results:
                if 'MUN.md' in result.filename:
                    print(f"  ✅ 找到: {result.filename} - {result.full_path}")
                    found_md = True
                elif 'MUN' in result.filename or 'MUN' in result.full_path:
                    print(f"  📁 相关: {result.filename} - {result.full_path}")
            
            if not found_md and len(results) > 0:
                print(f"  ❌ 未找到MUN.md，但找到了其他结果")
            elif not found_md:
                print(f"  ❌ 未找到任何结果")
                
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_everything_queries()
