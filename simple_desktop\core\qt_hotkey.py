
"""
与Qt兼容的热键管理器 - 使用Qt的事件系统
"""

import time
import threading
import ctypes
import ctypes.wintypes as wintypes
from typing import Optional, Callable
from PySide6.QtCore import QObject, QTimer, Signal

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_CONTROL = 0x11
VK_0 = 0x30
DOUBLE_CLICK_INTERVAL = 0.5

class QtCompatibleHotkeyManager(QObject):
    """与Qt兼容的热键管理器"""
    
    # Qt信号
    profile_switch_signal = Signal(int)
    toggle_window_signal = Signal()
    
    def __init__(self):
        """初始化热键管理器"""
        super().__init__()
        
        self.is_listening = False
        self.toggle_callback: Optional[Callable[[], None]] = None
        self.profile_switch_callback: Optional[Callable[[int], None]] = None
        
        # 双击Ctrl检测
        self.last_ctrl_press_time = 0
        self.ctrl_press_count = 0
        self.ctrl_check_thread = None
        self.stop_event = threading.Event()
        
        # Windows API函数
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
        # 注册的热键ID
        self.registered_hotkeys = set()
        
        # 消息检查定时器
        self.message_timer = QTimer()
        self.message_timer.timeout.connect(self._check_messages)

        # 连接信号到回调
        self.profile_switch_signal.connect(self._handle_profile_switch)
        self.toggle_window_signal.connect(self._handle_toggle_window)

        # 调试统计
        self.message_check_count = 0
        self.hotkey_event_count = 0

        # 状态监控定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._print_status)

        print("[DEBUG] Qt兼容热键管理器已初始化")
        print("[DEBUG] 调试模式已启用")
    
    def set_callback(self, callback: Callable[[], None]):
        """设置窗口切换回调"""
        self.toggle_callback = callback
        print("[QT-HOTKEY] 窗口切换回调已设置")
    
    def set_profile_switch_callback(self, callback: Callable[[int], None]):
        """设置Profile切换回调"""
        self.profile_switch_callback = callback
        print("[QT-HOTKEY] Profile切换回调已设置")
    
    def start_listening(self) -> bool:
        """开始监听快捷键"""
        if self.is_listening:
            return True
        
        try:
            print("[QT-HOTKEY] 开始注册Profile热键...")
            
            # 注册Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            successful_registrations = 0
            
            for i in range(10):
                hotkey_id = 1000 + i
                vk_code = VK_0 + i
                
                # 尝试注册热键
                result = self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
                if result:
                    self.registered_hotkeys.add(hotkey_id)
                    successful_registrations += 1
                    print(f"[QT-HOTKEY] Ctrl+{i} 注册成功")
                else:
                    error_code = ctypes.GetLastError()
                    print(f"[QT-HOTKEY] Ctrl+{i} 注册失败 (错误: {error_code})")
            
            print(f"[QT-HOTKEY] Profile热键注册完成: {successful_registrations}/10")
            
            if successful_registrations == 0:
                print("[ERROR] 没有成功注册任何Profile热键")
                return False
            
            # 启动双击Ctrl检测线程
            self.stop_event.clear()
            self.ctrl_check_thread = threading.Thread(target=self._ctrl_monitor_thread, daemon=True)
            self.ctrl_check_thread.start()
            
            # 启动消息检查定时器（使用Qt定时器）
            self.message_timer.start(10)  # 每10ms检查一次消息

            # 启动状态监控定时器（每30秒输出一次状态）
            self.status_timer.start(30000)  # 30秒

            self.is_listening = True
            print("[DEBUG] Qt兼容热键监听已启动")
            print("[DEBUG] 消息检查定时器: 每10ms")
            print("[DEBUG] 状态监控定时器: 每30秒")
            return True
            
        except Exception as e:
            print(f"[ERROR] 热键监听启动失败: {e}")
            self.stop_listening()
            return False
    
    def stop_listening(self):
        """停止监听快捷键"""
        if not self.is_listening:
            return
        
        print("[QT-HOTKEY] 停止热键监听...")
        self.is_listening = False
        self.stop_event.set()
        
        # 停止定时器
        self.message_timer.stop()
        self.status_timer.stop()

        # 注销热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()

        # 等待线程结束
        if self.ctrl_check_thread and self.ctrl_check_thread.is_alive():
            self.ctrl_check_thread.join(timeout=1.0)

        print("[DEBUG] Qt兼容热键监听已停止")
        print(f"[DEBUG] 总计检查消息次数: {self.message_check_count}")
        print(f"[DEBUG] 总计热键事件次数: {self.hotkey_event_count}")
    
    def _check_messages(self):
        """检查Windows消息（在Qt主线程中执行）"""
        self.message_check_count += 1
        msg = wintypes.MSG()

        # 使用PeekMessage检查所有消息（与成功测试相同的方法）
        message_count = 0
        hotkey_message_count = 0

        # 检查所有消息，不只是WM_HOTKEY
        while self.user32.PeekMessageW(ctypes.byref(msg), None, 0, 0, 1):  # PM_REMOVE - 检查所有消息
            message_count += 1

            if msg.message == WM_HOTKEY:
                hotkey_id = msg.wParam
                hotkey_message_count += 1
                self.hotkey_event_count += 1
                current_time = time.strftime('%H:%M:%S', time.localtime())

                print(f"[DEBUG] 🎯 热键消息接收: ID={hotkey_id} - 时间: {current_time}")
                print(f"[DEBUG] 消息详情: wParam={msg.wParam}, lParam={msg.lParam}")
                print(f"[DEBUG] 这是第 {self.hotkey_event_count} 个热键事件")

                self._handle_qt_hotkey(hotkey_id)

            # 分发所有消息（与成功测试相同）
            self.user32.TranslateMessage(ctypes.byref(msg))
            self.user32.DispatchMessageW(ctypes.byref(msg))

        # 如果检查到热键消息，输出统计信息
        if hotkey_message_count > 0:
            print(f"[DEBUG] 本次检查处理了 {hotkey_message_count} 个热键消息（总消息: {message_count}）")

    def _print_status(self):
        """打印状态信息"""
        current_time = time.strftime('%H:%M:%S', time.localtime())
        print(f"[STATUS] 热键系统状态 - 时间: {current_time}")
        print(f"[STATUS] 监听状态: {'运行中' if self.is_listening else '已停止'}")
        print(f"[STATUS] 注册热键数: {len(self.registered_hotkeys)}")
        print(f"[STATUS] 消息检查次数: {self.message_check_count}")
        print(f"[STATUS] 热键事件次数: {self.hotkey_event_count}")
        print(f"[STATUS] 回调函数状态: {'已设置' if self.profile_switch_callback else '未设置'}")
        if self.registered_hotkeys:
            print(f"[STATUS] 注册的热键ID: {sorted(self.registered_hotkeys)}")
        print("[STATUS] " + "="*50)
    
    def _handle_qt_hotkey(self, hotkey_id: int):
        """在Qt主线程中处理热键消息"""
        current_time = time.strftime('%H:%M:%S', time.localtime())

        print(f"[DEBUG] 开始处理热键: ID={hotkey_id} - 时间: {current_time}")

        if 1000 <= hotkey_id <= 1009:
            # Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            profile_id = hotkey_id - 1000
            print(f"[DEBUG] 热键触发: Ctrl+{profile_id} (ID: {hotkey_id}) - 时间: {current_time}")
            print(f"[DEBUG] 准备发射Qt信号: profile_switch_signal({profile_id})")

            # 发射Qt信号
            try:
                self.profile_switch_signal.emit(profile_id)
                print(f"[DEBUG] Qt信号发射成功: profile_switch_signal({profile_id})")
            except Exception as e:
                print(f"[ERROR] Qt信号发射失败: {e}")
        else:
            print(f"[DEBUG] 未知热键ID: {hotkey_id}")
            print(f"[DEBUG] 期望范围: 1000-1009，实际: {hotkey_id}")
    
    def _handle_profile_switch(self, profile_id: int):
        """处理Profile切换信号"""
        current_time = time.strftime('%H:%M:%S', time.localtime())
        print(f"[DEBUG] Qt信号接收: profile_switch_signal({profile_id}) - 时间: {current_time}")
        print(f"[DEBUG] 处理Profile切换信号: Profile {profile_id}")

        if self.profile_switch_callback:
            try:
                print(f"[DEBUG] 回调函数存在，准备执行: switch_profile({profile_id})")
                print(f"[DEBUG] 回调函数类型: {type(self.profile_switch_callback)}")

                # 执行回调
                result = self.profile_switch_callback(profile_id)

                print(f"[DEBUG] 回调执行: switch_profile({profile_id}) - 时间: {current_time}")
                print(f"[DEBUG] 回调返回值: {result}")
                print(f"[DEBUG] Profile切换回调执行完成")
            except Exception as e:
                print(f"[ERROR] Profile切换回调错误: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("[ERROR] Profile切换回调未设置！")
            print("[ERROR] 这是问题的根源 - 回调函数为None")
    
    def _handle_toggle_window(self):
        """处理窗口切换信号"""
        if self.toggle_callback:
            try:
                self.toggle_callback()
                print("[QT-HOTKEY] 窗口切换回调执行完成")
            except Exception as e:
                print(f"[ERROR] 窗口切换回调错误: {e}")
    
    def _ctrl_monitor_thread(self):
        """监控Ctrl键双击的线程"""
        last_state = False
        
        while not self.stop_event.is_set():
            try:
                # 检查Ctrl键状态
                ctrl_state = self.user32.GetAsyncKeyState(VK_CONTROL)
                is_pressed = bool(ctrl_state & 0x8000)
                
                # 边沿触发：检测从未按下到按下的转换
                if is_pressed and not last_state:
                    current_time = time.time()
                    
                    # 如果有之前的按键记录，检查双击
                    if self.ctrl_press_count > 0:
                        time_diff = current_time - self.last_ctrl_press_time
                        
                        if time_diff <= DOUBLE_CLICK_INTERVAL:
                            # 双击Ctrl检测到，发射Qt信号
                            self.toggle_window_signal.emit()
                            print("[QT-HOTKEY] 双击Ctrl检测到")
                            
                            # 重置状态
                            self.ctrl_press_count = 0
                            self.last_ctrl_press_time = 0
                            last_state = is_pressed
                            continue
                    
                    # 记录新的按键
                    self.last_ctrl_press_time = current_time
                    self.ctrl_press_count = 1
                
                # 重置超时的计数
                current_time = time.time()
                if (self.ctrl_press_count > 0 and 
                    current_time - self.last_ctrl_press_time > DOUBLE_CLICK_INTERVAL):
                    self.ctrl_press_count = 0
                
                last_state = is_pressed
                
                # 短暂休眠
                time.sleep(0.05)
                
            except Exception as e:
                print(f"[ERROR] Ctrl监控错误: {e}")
                break

# 创建全局实例
hotkey_manager = QtCompatibleHotkeyManager()
