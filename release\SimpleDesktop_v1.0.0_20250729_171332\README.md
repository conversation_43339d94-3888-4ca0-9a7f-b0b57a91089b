# Simple Desktop

基于Everything SDK的文件搜索工具，提供快速、轻量的本地文件搜索体验。

## 特性

- 🚀 **秒级搜索** - 基于Everything引擎，搜索响应时间≤10ms
- 📁 **10个Profile** - 支持10个独立的搜索配置（0-9）
- 🎯 **智能过滤** - 支持文件类型筛选和后缀过滤
- ⌨️ **全局快捷键** - 双击Ctrl显示/隐藏，Ctrl+数字切换Profile
- 🪟 **独立浮动窗口** - 可被其他应用覆盖，支持多任务操作
- 🎨 **磨砂玻璃UI** - 现代化的用户界面设计
- 🔧 **零配置** - 开箱即用，无需复杂设置

## 系统要求

- Windows 10/11 x64
- Everything 1.4+ (需要安装并运行)
- Python 3.9+ (开发环境)

## 安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd withEverything
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 确保Everything运行
确保系统中已安装Everything并正在运行。

### 4. 运行程序
```bash
python main.py
```

## 使用说明

### 快捷键
- **双击Ctrl** - 显示/隐藏搜索窗口
- **Ctrl+0~9** - 快速切换到对应Profile
- **Tab** - 切换打开文件/打开文件夹模式
- **ESC** - 隐藏搜索窗口
- **↑↓** - 在搜索结果中导航
- **Enter** - 打开选中的文件/文件夹
- **→** - 从搜索框切换到后缀框
- **←** - 从后缀框切换到搜索框

### 搜索技巧
- **基础搜索** - 直接输入文件名关键词
- **后缀过滤** - 在后缀框输入文件扩展名（如 .txt）
- **文件类型** - 点击"类型"按钮选择要显示的文件类型
- **Everything语法** - 支持Everything的高级搜索语法

### Profile管理
- **10个Profile** - 标签0-9，每个可配置不同的搜索范围
- **独立配置** - 每个Profile有独立的扫描目录和文件类型过滤
- **快速切换** - 使用Ctrl+数字键或点击标签切换

## 项目结构

```
withEverything/
├── simple_desktop/           # 主程序包
│   ├── core/                # 核心模块
│   │   ├── everything_sdk.py   # Everything SDK接口
│   │   ├── config.py           # 配置管理
│   │   ├── profile_manager.py  # Profile管理
│   │   └── hotkey.py           # 快捷键管理
│   ├── search/              # 搜索模块
│   │   └── engine.py           # 搜索引擎
│   ├── ui/                  # 用户界面
│   │   ├── search_window.py    # 主搜索窗口
│   │   └── dialogs.py          # 对话框组件
│   └── app.py               # 主应用程序
├── Everything-SDK/          # Everything SDK
├── main.py                  # 程序入口
├── requirements.txt         # 依赖列表
└── README.md               # 说明文档
```

## 开发

### 环境设置
```bash
# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 运行开发版本
```bash
python main.py
```

### 打包发布
```bash
# 安装打包工具
pip install pyinstaller

# 打包为单个exe文件
pyinstaller --onefile --windowed --icon=assets/icon.ico main.py
```

## 配置文件

配置文件位于 `~/.simple_desktop/config.json`，包含：
- Profile配置
- 扫描目录设置
- 文件类型过滤
- 快捷键设置
- 窗口位置等

## 故障排除

### Everything不可用
- 确保Everything已安装并正在运行
- 检查Everything服务是否启动
- 尝试重启Everything

### 快捷键不工作
- 检查是否有其他程序占用相同快捷键
- 尝试以管理员权限运行
- 在设置中重新启用快捷键

### 搜索结果为空
- 检查当前Profile的扫描目录设置
- 确保目录路径存在且可访问
- 检查文件类型过滤设置

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 基础搜索功能
- Profile管理
- 全局快捷键
- 系统托盘集成
