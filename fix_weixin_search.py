#!/usr/bin/env python3
"""
修复微信搜索问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def fix_profile_directories():
    """修复Profile目录配置"""
    print("🔧 修复Profile目录配置...")
    
    try:
        from simple_desktop.core.config import config_manager
        from simple_desktop.core.profile_manager import profile_manager
        
        # 获取当前Profile 0的配置
        current_dirs = config_manager.get_scan_directories(0)
        print(f"当前扫描目录: {current_dirs}")
        
        # 添加公共桌面和开始菜单目录
        public_desktop = "C:\\Users\\<USER>\\Desktop"
        start_menu = "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs"
        
        added_dirs = []
        
        if os.path.exists(public_desktop) and public_desktop not in current_dirs:
            success = config_manager.add_scan_directory(public_desktop, 0)
            if success:
                added_dirs.append(public_desktop)
                print(f"✅ 已添加公共桌面: {public_desktop}")
            else:
                print(f"❌ 添加公共桌面失败: {public_desktop}")
        
        if os.path.exists(start_menu) and start_menu not in current_dirs:
            success = config_manager.add_scan_directory(start_menu, 0)
            if success:
                added_dirs.append(start_menu)
                print(f"✅ 已添加开始菜单: {start_menu}")
            else:
                print(f"❌ 添加开始菜单失败: {start_menu}")
        
        if added_dirs:
            print(f"✅ 成功添加 {len(added_dirs)} 个目录")
            
            # 显示更新后的目录列表
            updated_dirs = config_manager.get_scan_directories(0)
            print("更新后的扫描目录:")
            for i, dir_path in enumerate(updated_dirs):
                print(f"  {i+1}. {dir_path}")
        else:
            print("ℹ️ 所有必要目录已存在，无需添加")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复Profile目录配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weixin_search_after_fix():
    """修复后测试微信搜索"""
    print("\n🔍 修复后测试微信搜索...")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        # 测试Everything SDK直接搜索
        print("1. Everything SDK直接搜索:")
        sdk = get_everything_sdk()
        
        results = sdk.search("微信", max_results=10)
        print(f"   '微信'搜索结果: {len(results)}")
        
        weixin_lnk_files = []
        for result in results:
            if result.filename.endswith('.lnk') and '微信' in result.filename:
                weixin_lnk_files.append(result)
                print(f"   ✅ 找到微信快捷方式: {result.filename} -> {result.full_path}")
        
        # 测试搜索引擎
        print("\n2. 搜索引擎测试:")
        engine = FileSearchEngine(profile_id=0)
        
        # 测试中文搜索
        results_cn = engine.search("微信", limit=10)
        print(f"   搜索'微信'结果: {len(results_cn)}")
        
        for result in results_cn:
            print(f"     {result.filename} ({result.item_type}) -> {result.filepath}")
            if result.suffix.lower() == ".lnk":
                print(f"       ✅ 快捷方式文件!")
        
        # 测试英文搜索
        results_en = engine.search("weixin", limit=10)
        print(f"   搜索'weixin'结果: {len(results_en)}")
        
        for result in results_en:
            print(f"     {result.filename} ({result.item_type}) -> {result.filepath}")
        
        # 测试文件类型过滤
        print("\n3. 文件类型过滤测试:")
        results_exe = engine.search("微信", limit=10, file_types=["executables"])
        print(f"   搜索'微信'(可执行文件)结果: {len(results_exe)}")
        
        for result in results_exe:
            print(f"     {result.filename} ({result.suffix}) -> {result.filepath}")
        
        # 总结
        total_weixin_results = len(results_cn) + len(results_en)
        if total_weixin_results > 0:
            print(f"\n✅ 修复成功！现在可以搜索到微信相关文件")
            return True
        else:
            print(f"\n⚠️ 仍然无法搜索到微信文件，可能需要进一步调试")
            return False
        
    except Exception as e:
        print(f"❌ 测试搜索失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_search():
    """测试GUI搜索功能"""
    print("\n🖥️ 测试GUI搜索功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        from simple_desktop.ui.search_window import FloatingSearchWindow
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        print("✅ 搜索窗口创建成功")
        
        # 显示窗口
        search_window.show_window()
        
        print("\n📝 GUI测试说明:")
        print("   1. 搜索窗口已显示")
        print("   2. 尝试搜索'微信'应该能找到快捷方式")
        print("   3. 快捷方式应该显示在结果列表顶部")
        print("   4. 点击快捷方式应该能打开微信")
        
        # 5秒后自动关闭
        QTimer.singleShot(5000, search_window.close)
        QTimer.singleShot(5500, app.quit)
        
        print("\n🚀 GUI测试窗口已启动！")
        print("   - 5秒后自动关闭")
        print("   - 或按Ctrl+C手动退出")
        
        # 运行应用
        app.exec()
        
        print("\n✅ GUI测试完成")
        return True
        
    except KeyboardInterrupt:
        print("\n   测试已退出")
        return True
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 微信搜索问题修复")
    print("=" * 60)
    
    # 步骤1：修复Profile目录配置
    fix_success = fix_profile_directories()
    
    if fix_success:
        # 步骤2：测试修复后的搜索功能
        search_success = test_weixin_search_after_fix()
        
        if search_success:
            # 步骤3：测试GUI搜索功能
            test_gui_search()
        
    print("\n" + "=" * 60)
    print("🎯 修复完成！")
    
    if fix_success:
        print("\n📋 修复总结:")
        print("1. ✅ 已添加公共桌面和开始菜单到扫描目录")
        print("2. ✅ 已将.lnk文件添加到可执行文件分类")
        print("3. ✅ 现在应该可以搜索到微信快捷方式")
        print("\n💡 使用建议:")
        print("- 搜索'微信'可以找到中文名称的快捷方式")
        print("- 快捷方式会显示在搜索结果的最前面")
        print("- 如果仍然无法找到，请检查微信是否已安装")
    else:
        print("\n❌ 修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
