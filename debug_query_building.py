#!/usr/bin/env python3
"""
调试查询构建问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_query_building():
    """调试查询构建"""
    print("🔍 调试查询构建")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        engine = FileSearchEngine(profile_id=0)
        sdk = get_everything_sdk()
        scan_dirs = engine.get_scan_directories()
        
        print(f"扫描目录: {scan_dirs}")
        
        # 测试不同的查询构建
        test_cases = [
            {"query": "sjl", "file_types": None, "include_folders": True},
            {"query": "sjl", "file_types": ["folders"], "include_folders": True},
            {"query": "sjl", "file_types": ["documents"], "include_folders": True},
            {"query": "sjl", "file_types": None, "include_folders": False},
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n🔍 测试用例 {i+1}: {test_case}")
            
            # 构建查询
            everything_query = engine._build_everything_query_with_directories(
                test_case["query"], 
                scan_dirs, 
                test_case["file_types"], 
                test_case["include_folders"]
            )
            print(f"   构建的查询: {everything_query}")
            
            # 直接用Everything SDK测试查询
            try:
                sdk_results = sdk.search(everything_query, max_results=10)
                print(f"   Everything SDK结果: {len(sdk_results)}")
                
                for j, result in enumerate(sdk_results):
                    folder_icon = "📁" if result.is_folder else "📄"
                    print(f"     {j+1}. {folder_icon} {result.filename} -> {result.full_path}")
                
            except Exception as e:
                print(f"   ❌ Everything SDK查询失败: {e}")
            
            # 用搜索引擎测试
            try:
                engine_results = engine.search(
                    test_case["query"],
                    limit=10,
                    file_types=test_case["file_types"],
                    include_folders=test_case["include_folders"]
                )
                print(f"   搜索引擎结果: {len(engine_results)}")
                
                for j, result in enumerate(engine_results):
                    folder_icon = "📁" if result.item_type == "folder" else "📄"
                    print(f"     {j+1}. {folder_icon} {result.filename} -> {result.filepath}")
                
            except Exception as e:
                print(f"   ❌ 搜索引擎查询失败: {e}")
        
        # 测试手动构建的查询
        print(f"\n🔍 手动构建查询测试:")
        
        desktop_path = str(Path.home() / "Desktop")
        manual_queries = [
            f'"{desktop_path}\\" sjl',
            f'"{desktop_path}\\" folder:sjl',
            f'"{desktop_path}\\" type:folder sjl',
        ]
        
        for query in manual_queries:
            print(f"\n   查询: {query}")
            try:
                results = sdk.search(query, max_results=5)
                print(f"   结果: {len(results)}")
                
                for j, result in enumerate(results):
                    folder_icon = "📁" if result.is_folder else "📄"
                    print(f"     {j+1}. {folder_icon} {result.filename} -> {result.full_path}")
                
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        print("\n✅ 查询构建调试完成")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 查询构建调试")
    print("=" * 60)
    
    debug_query_building()
    
    print("\n" + "=" * 60)
    print("🎯 调试完成！")

if __name__ == "__main__":
    main()
