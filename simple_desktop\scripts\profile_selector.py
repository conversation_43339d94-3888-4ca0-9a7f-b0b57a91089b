"""
Profile选择对话框
用于右键菜单集成时选择目标Profile
"""

import sys
import os
from pathlib import Path
from typing import Optional, List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from PyQt5.QtWidgets import (
        QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
        QLabel, QListWidget, QListWidgetItem, QPushButton,
        QMessageBox, QProgressBar, QTextEdit
    )
    from PyQt5.QtCore import Qt, QThread, pyqtSignal
    from PyQt5.QtGui import QFont, QIcon
except ImportError:
    print("PyQt5 not available, using tkinter fallback")
    import tkinter as tk
    from tkinter import messagebox, simpledialog


class ProfileSelectorDialog(QDialog):
    """Profile选择对话框"""
    
    def __init__(self, folder_path: str, parent=None):
        super().__init__(parent)
        self.folder_path = folder_path
        self.selected_profile_id = None
        self.profiles_info = {}
        
        self.setWindowTitle("添加到SimpleDesktop")
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        self.init_ui()
        self.load_profiles()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("选择要添加到的Profile")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 文件夹路径显示
        path_label = QLabel(f"文件夹: {self.folder_path}")
        path_label.setWordWrap(True)
        path_label.setStyleSheet("color: #666; margin: 5px 0;")
        layout.addWidget(path_label)
        
        # Profile列表
        self.profile_list = QListWidget()
        self.profile_list.itemDoubleClicked.connect(self.on_profile_double_clicked)
        layout.addWidget(self.profile_list)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.add_button = QPushButton("添加")
        self.add_button.clicked.connect(self.add_to_profile)
        self.add_button.setEnabled(False)
        
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 连接选择事件
        self.profile_list.itemSelectionChanged.connect(self.on_selection_changed)
    
    def load_profiles(self):
        """加载Profile信息"""
        try:
            # 导入配置管理器
            from simple_desktop.core.config import config_manager
            
            # 获取所有Profile信息
            for i in range(10):
                profile = config_manager.get_profile(i)
                if profile:
                    self.profiles_info[i] = {
                        'name': profile.name,
                        'scan_directories': profile.scan_directories,
                        'enabled_file_types': profile.enabled_file_types
                    }
                    
                    # 创建列表项
                    item_text = f"Profile {i}: {profile.name}"
                    if profile.scan_directories:
                        item_text += f" ({len(profile.scan_directories)} 个目录)"
                    
                    item = QListWidgetItem(item_text)
                    item.setData(Qt.UserRole, i)  # 存储Profile ID
                    self.profile_list.addItem(item)
            
            if self.profile_list.count() > 0:
                self.profile_list.setCurrentRow(0)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载Profile失败: {e}")
    
    def on_selection_changed(self):
        """处理选择变化"""
        current_item = self.profile_list.currentItem()
        if current_item:
            self.selected_profile_id = current_item.data(Qt.UserRole)
            self.add_button.setEnabled(True)
        else:
            self.selected_profile_id = None
            self.add_button.setEnabled(False)
    
    def on_profile_double_clicked(self, item):
        """处理双击事件"""
        self.selected_profile_id = item.data(Qt.UserRole)
        self.add_to_profile()
    
    def add_to_profile(self):
        """添加文件夹到选中的Profile"""
        if self.selected_profile_id is None:
            return
        
        try:
            # 导入Profile管理器
            from simple_desktop.core.profile_manager import profile_manager
            
            # 检查文件夹是否已存在
            existing_dirs = profile_manager.get_profile_scan_directories(self.selected_profile_id)
            if self.folder_path in existing_dirs:
                QMessageBox.information(
                    self, 
                    "信息", 
                    f"文件夹已存在于Profile {self.selected_profile_id}中"
                )
                return
            
            # 添加文件夹到Profile
            success = profile_manager.add_directory_to_profile(
                self.folder_path, 
                self.selected_profile_id
            )
            
            if success:
                profile_name = self.profiles_info[self.selected_profile_id]['name']
                QMessageBox.information(
                    self, 
                    "成功", 
                    f"文件夹已成功添加到 {profile_name}"
                )
                self.accept()
            else:
                QMessageBox.critical(self, "错误", "添加文件夹失败")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败: {e}")


class TkinterProfileSelector:
    """Tkinter版本的Profile选择器（备用）"""
    
    def __init__(self, folder_path: str):
        self.folder_path = folder_path
        self.selected_profile_id = None
        
    def show_dialog(self) -> bool:
        """显示对话框"""
        try:
            # 导入配置管理器
            from simple_desktop.core.config import config_manager
            from simple_desktop.core.profile_manager import profile_manager
            
            # 获取Profile列表
            profiles = []
            for i in range(10):
                profile = config_manager.get_profile(i)
                if profile:
                    profiles.append(f"Profile {i}: {profile.name}")
            
            if not profiles:
                messagebox.showerror("错误", "没有可用的Profile")
                return False
            
            # 显示选择对话框
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            
            # 简单的选择对话框
            choice = simpledialog.askstring(
                "选择Profile",
                f"要将文件夹添加到哪个Profile？\n文件夹: {self.folder_path}\n\n" +
                "\n".join(f"{i}: {profiles[i]}" for i in range(len(profiles))) +
                "\n\n请输入Profile编号 (0-9):"
            )
            
            if choice and choice.isdigit():
                profile_id = int(choice)
                if 0 <= profile_id <= 9:
                    # 添加文件夹
                    success = profile_manager.add_directory_to_profile(
                        self.folder_path, 
                        profile_id
                    )
                    
                    if success:
                        messagebox.showinfo("成功", f"文件夹已添加到Profile {profile_id}")
                        return True
                    else:
                        messagebox.showerror("错误", "添加失败")
            
            return False
            
        except Exception as e:
            messagebox.showerror("错误", f"操作失败: {e}")
            return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: profile_selector.py <folder_path>")
        sys.exit(1)
    
    folder_path = sys.argv[1]
    
    # 验证路径
    if not os.path.isdir(folder_path):
        print(f"Invalid folder path: {folder_path}")
        sys.exit(1)
    
    try:
        # 尝试使用PyQt5
        app = QApplication(sys.argv)
        dialog = ProfileSelectorDialog(folder_path)
        
        if dialog.exec_() == QDialog.Accepted:
            sys.exit(0)  # 成功
        else:
            sys.exit(1)  # 取消
            
    except Exception as e:
        print(f"PyQt5 dialog failed: {e}")
        
        # 使用Tkinter备用方案
        try:
            selector = TkinterProfileSelector(folder_path)
            if selector.show_dialog():
                sys.exit(0)
            else:
                sys.exit(1)
        except Exception as e2:
            print(f"Tkinter dialog also failed: {e2}")
            sys.exit(1)


if __name__ == "__main__":
    main()
