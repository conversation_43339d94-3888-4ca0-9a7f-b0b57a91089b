#!/usr/bin/env python3
"""
实时热键监控和修复工具
"""

import sys
import time
import threading
import ctypes
import ctypes.wintypes as wintypes
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_0 = 0x30

class RealtimeHotkeyMonitor:
    """实时热键监控器"""
    
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        self.is_monitoring = False
        self.registered_hotkeys = set()
        self.hotkey_events = []
        self.monitor_thread = None
        
    def register_profile_hotkeys(self):
        """注册Profile热键"""
        print("🔧 注册Profile热键...")
        
        success_count = 0
        
        for i in range(10):
            hotkey_id = 5000 + i  # 使用新的ID范围避免冲突
            vk_code = VK_0 + i
            
            result = self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
            
            if result:
                self.registered_hotkeys.add(hotkey_id)
                success_count += 1
                print(f"   ✅ Ctrl+{i} 注册成功 (ID: {hotkey_id})")
            else:
                error_code = ctypes.GetLastError()
                print(f"   ❌ Ctrl+{i} 注册失败 (错误: {error_code})")
        
        print(f"📊 注册结果: {success_count}/10 成功")
        return success_count > 0
    
    def start_monitoring(self):
        """开始监控热键"""
        if self.is_monitoring:
            return True
        
        if not self.register_profile_hotkeys():
            print("❌ 热键注册失败，无法开始监控")
            return False
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        print("🎯 实时热键监控已启动")
        print("请按 Ctrl+0 到 Ctrl+9 测试热键响应...")
        
        return True
    
    def stop_monitoring(self):
        """停止监控热键"""
        self.is_monitoring = False
        
        # 注销热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()
        
        print("⏹️ 热键监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        msg = wintypes.MSG()
        
        print("🔄 消息循环已启动，等待热键事件...")
        
        while self.is_monitoring:
            try:
                # 获取消息，设置超时
                result = self.user32.GetMessage(ctypes.byref(msg), None, 0, 0)
                
                if result == -1:  # 错误
                    print("❌ GetMessage 错误")
                    break
                elif result == 0:  # WM_QUIT
                    print("🔚 收到退出消息")
                    break
                
                # 处理热键消息
                if msg.message == WM_HOTKEY:
                    hotkey_id = msg.wParam
                    self._handle_hotkey_event(hotkey_id)
                
                # 分发消息
                self.user32.TranslateMessage(ctypes.byref(msg))
                self.user32.DispatchMessage(ctypes.byref(msg))
                
            except Exception as e:
                print(f"❌ 监控循环错误: {e}")
                break
    
    def _handle_hotkey_event(self, hotkey_id):
        """处理热键事件"""
        current_time = time.time()
        
        if 5000 <= hotkey_id <= 5009:
            profile_id = hotkey_id - 5000
            
            event_info = {
                'time': current_time,
                'profile_id': profile_id,
                'hotkey_id': hotkey_id
            }
            
            self.hotkey_events.append(event_info)
            
            print(f"🎯 热键事件检测到!")
            print(f"   时间: {time.strftime('%H:%M:%S', time.localtime(current_time))}")
            print(f"   热键: Ctrl+{profile_id}")
            print(f"   热键ID: {hotkey_id}")
            print(f"   事件总数: {len(self.hotkey_events)}")
            
            # 尝试触发Profile切换
            self._trigger_profile_switch(profile_id)
    
    def _trigger_profile_switch(self, profile_id):
        """触发Profile切换"""
        try:
            print(f"🔄 尝试切换到Profile {profile_id}...")
            
            # 导入Profile管理器
            from simple_desktop.core.profile_manager import profile_manager
            
            # 获取当前Profile
            current_profile = profile_manager.current_profile_id
            print(f"   当前Profile: {current_profile}")
            
            # 切换Profile
            profile_manager.switch_profile(profile_id)
            
            # 验证切换结果
            new_profile = profile_manager.current_profile_id
            print(f"   切换后Profile: {new_profile}")
            
            if new_profile == profile_id:
                print(f"   ✅ Profile切换成功: {current_profile} -> {profile_id}")
            else:
                print(f"   ❌ Profile切换失败: 期望{profile_id}, 实际{new_profile}")
            
        except Exception as e:
            print(f"   ❌ Profile切换异常: {e}")
    
    def get_statistics(self):
        """获取统计信息"""
        return {
            'total_events': len(self.hotkey_events),
            'registered_hotkeys': len(self.registered_hotkeys),
            'is_monitoring': self.is_monitoring,
            'events': self.hotkey_events[-5:]  # 最近5个事件
        }

def test_with_simple_desktop():
    """与Simple Desktop集成测试"""
    print("\n🧪 与Simple Desktop集成测试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.core.profile_manager import profile_manager
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        search_window.show_window()
        
        print("✅ Simple Desktop组件创建成功")
        
        # 创建热键监控器
        monitor = RealtimeHotkeyMonitor()
        
        # 启动监控
        if monitor.start_monitoring():
            print("\n📝 集成测试说明:")
            print("1. Simple Desktop搜索窗口已显示")
            print("2. 实时热键监控已启动")
            print("3. 请按 Ctrl+0 到 Ctrl+9 测试")
            print("4. 观察控制台输出和窗口变化")
            print("5. 测试将在30秒后自动结束")
            
            # 运行测试
            start_time = time.time()
            while time.time() - start_time < 30:
                app.processEvents()
                time.sleep(0.1)
            
            # 显示统计信息
            stats = monitor.get_statistics()
            print(f"\n📊 测试统计:")
            print(f"   热键事件总数: {stats['total_events']}")
            print(f"   注册的热键数: {stats['registered_hotkeys']}")
            
            if stats['total_events'] > 0:
                print("   ✅ 热键响应正常")
                print("   最近的事件:")
                for event in stats['events']:
                    event_time = time.strftime('%H:%M:%S', time.localtime(event['time']))
                    print(f"     {event_time} - Ctrl+{event['profile_id']}")
            else:
                print("   ❌ 未检测到热键事件")
            
            # 停止监控
            monitor.stop_monitoring()
            search_window.hide()
            
            return stats['total_events'] > 0
        else:
            print("❌ 热键监控启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_existing_app():
    """诊断现有运行的应用程序"""
    print("\n🔍 诊断现有运行的应用程序")
    print("=" * 60)
    
    try:
        # 检查是否有Simple Desktop进程在运行
        import psutil
        
        simple_desktop_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'main.py' in cmdline or 'simple_desktop' in cmdline.lower():
                        simple_desktop_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if simple_desktop_processes:
            print(f"✅ 发现 {len(simple_desktop_processes)} 个Simple Desktop进程:")
            for proc in simple_desktop_processes:
                print(f"   PID: {proc['pid']}, 命令: {' '.join(proc['cmdline'])}")
        else:
            print("⚠️ 未发现运行中的Simple Desktop进程")
        
        # 创建独立的热键监控器
        print("\n🎯 创建独立热键监控器...")
        monitor = RealtimeHotkeyMonitor()
        
        if monitor.start_monitoring():
            print("\n📝 独立监控测试:")
            print("1. 独立热键监控器已启动")
            print("2. 这将测试热键注册和检测是否工作")
            print("3. 请按 Ctrl+0 到 Ctrl+9")
            print("4. 如果看到热键事件，说明热键系统正常")
            print("5. 测试将在15秒后结束")
            
            # 等待测试
            time.sleep(15)
            
            # 显示结果
            stats = monitor.get_statistics()
            print(f"\n📊 独立监控结果:")
            print(f"   检测到的热键事件: {stats['total_events']}")
            
            if stats['total_events'] > 0:
                print("   ✅ 热键系统工作正常")
                print("   问题可能在于Simple Desktop的热键集成")
            else:
                print("   ❌ 热键系统可能有问题")
                print("   可能的原因:")
                print("     - 权限不足")
                print("     - 热键被其他应用占用")
                print("     - Windows消息循环问题")
            
            monitor.stop_monitoring()
            return stats['total_events'] > 0
        else:
            print("❌ 独立热键监控器启动失败")
            return False
        
    except ImportError:
        print("⚠️ psutil未安装，跳过进程检查")
        return False
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 实时热键监控和修复工具")
    print("=" * 80)
    
    # 执行诊断和测试
    tests = [
        ("诊断现有运行的应用程序", diagnose_existing_app),
        ("与Simple Desktop集成测试", test_with_simple_desktop),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 成功")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 实时热键监控完成！")
    print(f"成功测试: {passed_tests}/{total_tests}")
    
    if passed_tests > 0:
        print("\n💡 诊断结果:")
        print("- 如果独立监控检测到热键事件，说明热键系统正常")
        print("- 如果集成测试也成功，说明Profile切换功能正常")
        print("- 如果只有独立监控成功，问题在于Simple Desktop的热键集成")
    else:
        print("\n⚠️ 所有测试都失败，可能的问题:")
        print("- Windows热键系统权限问题")
        print("- 热键被其他应用程序占用")
        print("- 需要以管理员身份运行")

if __name__ == "__main__":
    main()
