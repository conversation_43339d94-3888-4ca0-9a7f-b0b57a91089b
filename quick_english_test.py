# -*- coding: utf-8 -*-
"""
快速测试英文模糊查询修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_english_search_fix():
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试英文短词查询
        test_queries = ["cur", "doc", "win", "app"]
        
        for query in test_queries:
            print(f"\n测试查询: '{query}'")
            
            # 检查增强查询
            enhanced = engine._build_enhanced_query(query)
            print(f"  增强查询: '{enhanced}'")
            
            # 测试搜索结果
            results = engine.search(query, limit=5)
            print(f"  结果数: {len(results)}")
            
            for i, result in enumerate(results[:3]):
                print(f"    {i+1}. {result.filename}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_english_search_fix()
