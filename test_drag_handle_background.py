#!/usr/bin/env python3
"""
测试拖拽手柄背景显示
"""

import sys
from PySide6.QtWidgets import QApplication
from simple_desktop.ui.search_window import FloatingSearchWindow

def main():
    """测试拖拽手柄背景"""
    print("🎨 测试拖拽手柄背景显示")
    
    app = QApplication(sys.argv)
    
    # 创建搜索窗口
    search_window = FloatingSearchWindow()
    
    print("✅ 搜索窗口已创建")
    print("📝 统一设计测试要点:")
    print("   - 拖拽手柄应该有纯白色背景 #ffffff，与关闭按钮一致")
    print("   - 拖拽手柄应该是圆角长方形（6px圆角），关闭按钮是圆形（12px圆角）")
    print("   - 两者应该有相同的边框样式：1px solid rgba(200, 200, 200, 0.8)")
    print("   - 拖拽手柄从左边缘延伸到关闭按钮左侧，保持10px间距")
    print("   - 拖拽手柄内应该显示 '≡' 图标和 '拖拽移动窗口' 文字")
    print("   - 悬停时两者都应该变为 #f8f8f8 背景色")
    print("   - 悬停时边框都应该变为 rgba(160, 160, 160, 0.9)")
    print("   - 整体视觉风格应该统一协调")
    
    # 显示窗口
    search_window.show_window()
    
    print("\n🎯 测试窗口已启动！")
    print("请检查拖拽手柄的白色背景是否正确显示")
    print("按ESC键或点击关闭按钮退出")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
