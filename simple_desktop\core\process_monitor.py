"""
进程监控模块
用于检测SimpleDesktop应用程序的运行状态
"""

import os
import sys
import time
import threading
import tempfile
import json
from pathlib import Path
from typing import Optional, Callable, Dict, Any
import subprocess


class ProcessMonitor:
    """进程监控器"""
    
    def __init__(self):
        """初始化进程监控器"""
        self.app_name = "SimpleDesktop"
        self.process_identifier = "simple_desktop"
        
        # 状态文件路径
        self.temp_dir = Path(tempfile.gettempdir()) / "simple_desktop"
        self.temp_dir.mkdir(exist_ok=True)
        self.status_file = self.temp_dir / "app_status.json"
        self.pid_file = self.temp_dir / "app.pid"
        
        # 监控状态
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._status_callbacks: list[Callable[[bool], None]] = []
        
        # 当前状态
        self._last_known_status = False
    
    def register_app_running(self, pid: Optional[int] = None) -> bool:
        """注册应用程序为运行状态"""
        try:
            if pid is None:
                pid = os.getpid()
            
            # 写入PID文件
            with open(self.pid_file, 'w') as f:
                f.write(str(pid))
            
            # 写入状态文件
            status_data = {
                "running": True,
                "pid": pid,
                "timestamp": time.time(),
                "executable": sys.executable,
                "working_dir": os.getcwd()
            }
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2)
            
            print(f"App registered as running (PID: {pid})")
            return True
            
        except Exception as e:
            print(f"Failed to register app as running: {e}")
            return False
    
    def unregister_app_running(self) -> bool:
        """注销应用程序运行状态"""
        try:
            # 删除PID文件
            if self.pid_file.exists():
                self.pid_file.unlink()
            
            # 更新状态文件
            status_data = {
                "running": False,
                "timestamp": time.time()
            }
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2)
            
            print("App unregistered from running state")
            return True
            
        except Exception as e:
            print(f"Failed to unregister app: {e}")
            return False
    
    def is_app_running(self) -> bool:
        """检查应用程序是否正在运行"""
        try:
            # 方法1: 检查PID文件
            if self.pid_file.exists():
                try:
                    with open(self.pid_file, 'r') as f:
                        pid = int(f.read().strip())
                    
                    # 检查进程是否存在
                    if self._is_process_running(pid):
                        return True
                    else:
                        # PID文件存在但进程不存在，清理文件
                        self.pid_file.unlink()
                except (ValueError, FileNotFoundError):
                    pass
            
            # 方法2: 检查状态文件
            if self.status_file.exists():
                try:
                    with open(self.status_file, 'r', encoding='utf-8') as f:
                        status_data = json.load(f)
                    
                    if status_data.get("running", False):
                        pid = status_data.get("pid")
                        if pid and self._is_process_running(pid):
                            return True
                        else:
                            # 状态文件显示运行但进程不存在，更新状态
                            self.unregister_app_running()
                except (json.JSONDecodeError, FileNotFoundError):
                    pass
            
            # 方法3: 使用psutil检查进程
            return self._check_process_with_psutil()
            
        except Exception as e:
            print(f"Error checking app status: {e}")
            return False
    
    def _is_process_running(self, pid: int) -> bool:
        """检查指定PID的进程是否运行"""
        try:
            # Windows方法
            if os.name == 'nt':
                import ctypes
                kernel32 = ctypes.windll.kernel32
                handle = kernel32.OpenProcess(0x400, False, pid)  # PROCESS_QUERY_INFORMATION
                if handle:
                    kernel32.CloseHandle(handle)
                    return True
                return False
            else:
                # Unix方法
                os.kill(pid, 0)
                return True
        except (OSError, AttributeError):
            return False
    
    def _check_process_with_psutil(self) -> bool:
        """使用psutil检查进程"""
        try:
            import psutil
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline:
                        cmdline_str = ' '.join(str(arg) for arg in cmdline).lower()
                        if self.process_identifier in cmdline_str:
                            return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
            
        except ImportError:
            # psutil不可用，使用系统命令
            return self._check_process_with_tasklist()
    
    def _check_process_with_tasklist(self) -> bool:
        """使用tasklist命令检查进程"""
        try:
            if os.name != 'nt':
                return False
            
            result = subprocess.run(
                ['tasklist', '/FO', 'CSV'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                output = result.stdout.lower()
                return self.process_identifier in output
            
            return False
            
        except Exception:
            return False
    
    def add_status_callback(self, callback: Callable[[bool], None]):
        """添加状态变化回调"""
        if callback not in self._status_callbacks:
            self._status_callbacks.append(callback)
    
    def remove_status_callback(self, callback: Callable[[bool], None]):
        """移除状态变化回调"""
        if callback in self._status_callbacks:
            self._status_callbacks.remove(callback)
    
    def start_monitoring(self, interval: float = 5.0) -> bool:
        """开始监控进程状态"""
        if self._monitoring:
            return True
        
        self._monitoring = True
        
        def monitor_loop():
            while self._monitoring:
                try:
                    current_status = self.is_app_running()
                    
                    # 检查状态是否发生变化
                    if current_status != self._last_known_status:
                        print(f"App status changed: {self._last_known_status} -> {current_status}")
                        self._last_known_status = current_status
                        
                        # 调用回调函数
                        for callback in self._status_callbacks:
                            try:
                                callback(current_status)
                            except Exception as e:
                                print(f"Status callback error: {e}")
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    print(f"Monitor loop error: {e}")
                    time.sleep(interval * 2)
        
        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        print(f"Process monitoring started (interval: {interval}s)")
        return True
    
    def stop_monitoring(self):
        """停止监控进程状态"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
        print("Process monitoring stopped")
    
    def get_app_info(self) -> Dict[str, Any]:
        """获取应用程序信息"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception:
            return {}
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
            if self.status_file.exists():
                self.status_file.unlink()
            print("Process monitor cleanup completed")
        except Exception as e:
            print(f"Cleanup error: {e}")


# 全局实例
process_monitor = ProcessMonitor()
