#!/usr/bin/env python3
"""
Simple Desktop 打包脚本
使用PyInstaller构建可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """检查打包所需的依赖"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 已安装: {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装完成")

def clean_build():
    """清理之前的构建文件"""
    dirs_to_clean = ["build", "dist", "__pycache__"]
    files_to_clean = ["*.spec"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理spec文件
    for spec_file in Path(".").glob("*.spec"):
        print(f"清理文件: {spec_file}")
        spec_file.unlink()

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # Windows下不显示控制台
        "--name=SimpleDesktop",         # 可执行文件名称
        "--icon=D:\\withEverything\\ic.ico",  # 图标文件
        "--add-data=simple_desktop;simple_desktop",  # 包含simple_desktop目录
        "--add-data=Everything-SDK;Everything-SDK",  # 包含Everything SDK
        "--hidden-import=PySide6.QtCore",
        "--hidden-import=PySide6.QtWidgets", 
        "--hidden-import=PySide6.QtGui",
        "--hidden-import=win32api",
        "--hidden-import=win32con",
        "--hidden-import=win32gui",
        "--hidden-import=win32process",
        "--collect-all=PySide6",        # 收集所有PySide6模块
        "main.py"                       # 入口文件
    ]
    
    try:
        subprocess.check_call(cmd)
        print("✓ 可执行文件构建成功!")
        print("✓ 输出位置: dist/SimpleDesktop.exe")
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False
    
    return True

def copy_dependencies():
    """复制必要的依赖文件到dist目录"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("✗ dist目录不存在，构建可能失败")
        return

    # 复制Everything相关文件
    everything_files = [
        ("Everything-SDK/dll/Everything64.dll", "Everything64.dll"),
        # 注意：这里需要您提供 everything.exe 的实际路径
        # 如果您有 everything.exe，请取消下面这行的注释并修改路径
        # ("path/to/everything.exe", "everything.exe"),
    ]

    for src_path, dst_name in everything_files:
        src = Path(src_path)
        if src.exists():
            shutil.copy2(src, dist_dir / dst_name)
            print(f"✓ 复制 {dst_name}")
        else:
            print(f"⚠ 警告: {src_path} 未找到")

    # 复制图标文件
    icon_file = Path("ic.ico")
    if icon_file.exists():
        shutil.copy2(icon_file, dist_dir / "ic.ico")
        print("✓ 复制图标文件")

    # 复制Everything配置文件（如果存在）
    config_files = [
        "Everything.ini",
    ]

    for config_file in config_files:
        config_path = Path(config_file)
        if config_path.exists():
            shutil.copy2(config_path, dist_dir / config_file)
            print(f"✓ 复制 {config_file}")
        else:
            print(f"ℹ {config_file} 不存在，跳过")

def create_installer_script():
    """创建安装脚本"""
    installer_script = """@echo off
echo Simple Desktop 安装程序
echo.

set "INSTALL_DIR=%PROGRAMFILES%\\SimpleDesktop"
set "DESKTOP_SHORTCUT=%USERPROFILE%\\Desktop\\SimpleDesktop.lnk"
set "STARTMENU_SHORTCUT=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\SimpleDesktop.lnk"

echo 正在安装到: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

copy "SimpleDesktop.exe" "%INSTALL_DIR%\\"
copy "Everything64.dll" "%INSTALL_DIR%\\"
copy "ic.ico" "%INSTALL_DIR%\\"

echo 创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SimpleDesktop.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\\ic.ico'; $Shortcut.Save()"

echo 创建开始菜单快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SimpleDesktop.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\\ic.ico'; $Shortcut.Save()"

echo.
echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
pause
"""
    
    with open("dist/install.bat", "w", encoding="utf-8") as f:
        f.write(installer_script)
    print("✓ 创建安装脚本: dist/install.bat")

def main():
    """主函数"""
    print("=" * 50)
    print("Simple Desktop 打包程序")
    print("=" * 50)
    
    # 检查图标文件
    icon_path = Path("D:/withEverything/ic.ico")
    if not icon_path.exists():
        print(f"✗ 图标文件不存在: {icon_path}")
        return
    
    # 检查依赖
    check_requirements()
    
    # 清理之前的构建
    clean_build()
    
    # 构建可执行文件
    if build_executable():
        # 复制依赖文件
        copy_dependencies()
        
        # 创建安装脚本
        create_installer_script()
        
        print("\n" + "=" * 50)
        print("打包完成!")
        print("=" * 50)
        print("输出文件:")
        print("  - dist/SimpleDesktop.exe (主程序)")
        print("  - dist/Everything64.dll (Everything搜索引擎)")
        print("  - dist/ic.ico (图标文件)")
        print("  - dist/install.bat (安装脚本)")
        print("\n使用方法:")
        print("  1. 直接运行: dist/SimpleDesktop.exe")
        print("  2. 安装到系统: 运行 dist/install.bat")
    else:
        print("\n✗ 打包失败")

if __name__ == "__main__":
    main()
