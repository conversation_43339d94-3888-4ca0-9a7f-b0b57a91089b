"""
Profile管理器
管理10个不同的搜索配置Profile（0-9）
"""

from typing import Dict, List, Optional, Callable
from .config import config_manager, ProfileConfig


class ProfileManager:
    """Profile管理器"""
    
    def __init__(self):
        """初始化Profile管理器"""
        self.current_profile_id = config_manager.get_current_profile_id()
        self._profile_switch_callbacks: List[Callable[[int], None]] = []
    
    @property
    def current_profile(self) -> Optional[ProfileConfig]:
        """获取当前Profile配置"""
        return config_manager.get_profile(self.current_profile_id)
    
    def switch_profile(self, profile_id: int, progress_callback: Optional[Callable[[int], None]] = None):
        """
        切换到指定Profile
        
        Args:
            profile_id: Profile ID (0-9)
            progress_callback: 进度回调函数
        """
        if not (0 <= profile_id <= 9):
            raise ValueError(f"Invalid profile ID: {profile_id}")
        
        if profile_id == self.current_profile_id:
            return
        
        old_profile_id = self.current_profile_id
        self.current_profile_id = profile_id
        
        # 更新配置
        config_manager.set_current_profile_id(profile_id)
        
        # 调用切换回调
        for callback in self._profile_switch_callbacks:
            try:
                callback(profile_id)
            except Exception as e:
                print(f"Profile switch callback error: {e}")
        
        print(f"Switched from Profile {old_profile_id} to Profile {profile_id}")
    
    def add_profile_switch_callback(self, callback: Callable[[int], None]):
        """添加Profile切换回调"""
        if callback not in self._profile_switch_callbacks:
            self._profile_switch_callbacks.append(callback)
    
    def remove_profile_switch_callback(self, callback: Callable[[int], None]):
        """移除Profile切换回调"""
        if callback in self._profile_switch_callbacks:
            self._profile_switch_callbacks.remove(callback)
    
    def get_profile_name(self, profile_id: int) -> str:
        """获取Profile名称"""
        profile = config_manager.get_profile(profile_id)
        return profile.name if profile else f"Profile {profile_id}"
    
    def set_profile_name(self, profile_id: int, name: str):
        """设置Profile名称"""
        config_manager.update_profile(profile_id, name=name)
    
    def get_profile_scan_directories(self, profile_id: Optional[int] = None) -> List[str]:
        """获取Profile的扫描目录"""
        if profile_id is None:
            profile_id = self.current_profile_id
        return config_manager.get_scan_directories(profile_id)
    
    def add_directory_to_profile(self, directory: str, profile_id: Optional[int] = None) -> bool:
        """向Profile添加扫描目录"""
        if profile_id is None:
            profile_id = self.current_profile_id
        return config_manager.add_scan_directory(directory, profile_id)
    
    def remove_directory_from_profile(self, directory: str, profile_id: Optional[int] = None) -> bool:
        """从Profile移除扫描目录"""
        if profile_id is None:
            profile_id = self.current_profile_id
        return config_manager.remove_scan_directory(directory, profile_id)
    
    def add_directory_to_current(self, directory: str) -> bool:
        """向当前Profile添加扫描目录"""
        return self.add_directory_to_profile(directory, self.current_profile_id)
    
    def get_profile_file_types(self, profile_id: Optional[int] = None) -> List[str]:
        """获取Profile的文件类型过滤"""
        if profile_id is None:
            profile_id = self.current_profile_id
        return config_manager.get_enabled_file_types(profile_id)
    
    def set_profile_file_types(self, file_types: List[str], profile_id: Optional[int] = None):
        """设置Profile的文件类型过滤"""
        if profile_id is None:
            profile_id = self.current_profile_id
        config_manager.set_enabled_file_types(file_types, profile_id)
    
    def get_profile_search_delay(self, profile_id: Optional[int] = None) -> int:
        """获取Profile的搜索延迟"""
        if profile_id is None:
            profile_id = self.current_profile_id
        return config_manager.get_search_delay(profile_id)
    
    def set_profile_search_delay(self, delay: int, profile_id: Optional[int] = None):
        """设置Profile的搜索延迟"""
        if profile_id is None:
            profile_id = self.current_profile_id
        config_manager.update_profile(profile_id, search_delay=delay)
    
    def is_current_index_ready(self) -> bool:
        """检查当前Profile的索引是否准备好"""
        # 对于Everything SDK，索引总是准备好的（由Everything服务维护）
        from .everything_sdk import is_everything_available
        return is_everything_available()
    
    def get_profile_stats(self, profile_id: Optional[int] = None) -> Dict[str, any]:
        """获取Profile统计信息"""
        if profile_id is None:
            profile_id = self.current_profile_id
        
        profile = config_manager.get_profile(profile_id)
        if not profile:
            return {}
        
        return {
            "name": profile.name,
            "scan_directories_count": len(profile.scan_directories),
            "enabled_file_types_count": len(profile.enabled_file_types),
            "search_delay": profile.search_delay,
            "max_results": profile.max_results,
            "index_ready": self.is_current_index_ready() if profile_id == self.current_profile_id else True
        }
    
    def get_all_profiles_info(self) -> Dict[int, Dict[str, any]]:
        """获取所有Profile的信息"""
        profiles_info = {}
        for i in range(10):
            profiles_info[i] = self.get_profile_stats(i)
        return profiles_info
    
    def reset_profile(self, profile_id: int):
        """重置Profile到默认状态"""
        if not (0 <= profile_id <= 9):
            return
        
        name = "默认桌面" if profile_id == 0 else f"标签{profile_id}"
        config_manager.update_profile(
            profile_id,
            name=name,
            scan_directories=[],
            enabled_file_types=[],
            search_delay=200,
            max_results=50
        )
    
    def copy_profile(self, source_id: int, target_id: int):
        """复制Profile配置"""
        if not (0 <= source_id <= 9) or not (0 <= target_id <= 9):
            return
        
        source_profile = config_manager.get_profile(source_id)
        if not source_profile:
            return
        
        config_manager.update_profile(
            target_id,
            name=f"{source_profile.name} (副本)",
            scan_directories=source_profile.scan_directories.copy(),
            enabled_file_types=source_profile.enabled_file_types.copy(),
            search_delay=source_profile.search_delay,
            max_results=source_profile.max_results
        )


# 全局Profile管理器实例
profile_manager = ProfileManager()
