# Simple Desktop 热键使用指南

## 🎯 当前状态

Simple Desktop已经成功启动并注册了所有Profile热键：

```
[QT-HOTKEY] Ctrl+0 注册成功
[QT-HOTKEY] Ctrl+1 注册成功
[QT-HOTKEY] Ctrl+2 注册成功
[QT-HOTKEY] Ctrl+3 注册成功
[QT-HOTKEY] Ctrl+4 注册成功
[QT-HOTKEY] Ctrl+5 注册成功
[QT-HOTKEY] Ctrl+6 注册成功
[QT-HOTKEY] Ctrl+7 注册成功
[QT-HOTKEY] Ctrl+8 注册成功
[QT-HOTKEY] Ctrl+9 注册成功
[QT-HOTKEY] Profile热键注册完成: 10/10
[QT-HOTKEY] Qt兼容热键监听已启动
```

## 🔧 如何使用Profile热键

### 基本操作
1. **确保Simple Desktop正在运行**
   - 控制台应该显示"Simple Desktop v1.0.0 已启动"
   - 系统托盘应该有Simple Desktop图标

2. **使用Profile热键**
   - 按 `Ctrl+0` 切换到Profile 0 (默认桌面)
   - 按 `Ctrl+1` 切换到Profile 1 (标签1)
   - 按 `Ctrl+2` 切换到Profile 2 (标签2)
   - ...以此类推到 `Ctrl+9`

### 预期行为
当您按下Profile热键时，应该看到：

1. **控制台输出**（如果可见）：
   ```
   [QT-HOTKEY] 在Qt主线程中收到热键消息: ID=1001
   [QT-HOTKEY] 检测到Profile热键: Ctrl+1
   [QT-HOTKEY] 执行Profile切换回调: Profile 1
   快捷键触发: 切换到Profile 1
   ```

2. **搜索窗口变化**：
   - 如果窗口隐藏，会自动显示
   - 窗口的标签页会切换到对应的Profile
   - 搜索结果会更新为该Profile的配置

### 其他热键
- **双击Ctrl键**: 显示/隐藏搜索窗口
- **ESC键**: 退出Simple Desktop应用程序

## 🔍 故障排除

### 如果热键不响应

1. **检查应用程序状态**
   ```bash
   # 确认应用程序正在运行
   # 控制台应该显示启动消息
   ```

2. **检查热键冲突**
   - 关闭其他可能使用Ctrl+数字键的应用程序
   - 检查Windows系统热键设置

3. **权限问题**
   - 尝试以管理员身份运行
   - 右键点击命令提示符 → "以管理员身份运行"
   - 然后运行 `python main.py`

4. **重启应用程序**
   - 按ESC键退出Simple Desktop
   - 重新运行 `python main.py`

### 调试信息
如果需要调试，观察控制台输出：
- 启动时应该看到所有热键注册成功
- 按热键时应该看到相应的检测消息
- Profile切换时应该看到切换确认

## 💡 技术说明

Simple Desktop现在使用Qt兼容的热键管理器：
- 完全集成到Qt事件系统
- 使用Qt定时器检查Windows消息
- 通过Qt信号机制处理热键事件
- 确保线程安全和稳定性

这个新的实现解决了之前的消息循环冲突问题，提供了更可靠的热键响应。
