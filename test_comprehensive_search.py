# -*- coding: utf-8 -*-
"""
综合测试中文和英文模糊查询修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_comprehensive_search():
    """综合测试搜索功能"""
    print("综合测试搜索功能修复")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试用例
        test_cases = [
            # 英文模糊查询
            ("cur", "英文短词"),
            ("doc", "英文短词"),
            ("win", "英文短词"),
            ("app", "英文短词"),
            
            # 中文查询
            ("远征队", "中文词汇"),
            ("光与影", "中文词汇"),
            ("33号", "中文+数字"),
            
            # 数字查询
            ("33", "纯数字"),
            
            # 长英文词
            ("cursor", "英文长词"),
            ("document", "英文长词"),
        ]
        
        for query, description in test_cases:
            print(f"\n测试 {description}: '{query}'")
            
            # 检查增强查询
            enhanced = engine._build_enhanced_query(query)
            print(f"  增强查询: '{enhanced}'")
            
            # 测试搜索结果
            results = engine.search(query, limit=5)
            print(f"  结果数: {len(results)}")
            
            if len(results) > 0:
                print("  ✅ 搜索成功")
                for i, result in enumerate(results[:3]):
                    print(f"    {i+1}. {result.filename}")
            else:
                print("  ❌ 无搜索结果")
        
        # 测试后缀筛选
        print(f"\n测试后缀筛选:")
        suffix_results = engine.search("微信 ext:lnk", limit=5)
        print(f"  '微信 ext:lnk' 结果数: {len(suffix_results)}")
        
        if len(suffix_results) > 0:
            all_lnk = all(result.suffix == ".lnk" for result in suffix_results)
            print(f"  后缀筛选正确: {'✅' if all_lnk else '❌'}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_comprehensive_search()
