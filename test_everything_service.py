#!/usr/bin/env python3
"""
Everything 服务自动启动功能测试脚本
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_service_manager():
    """测试服务管理器功能"""
    print("🧪 测试 Everything 服务管理器")
    print("=" * 60)
    
    try:
        from simple_desktop.core.everything_service import get_service_manager
        
        service_manager = get_service_manager()
        
        print("1. 检查 Everything.exe 路径...")
        if service_manager.everything_exe_path:
            print(f"   ✓ 找到 Everything.exe: {service_manager.everything_exe_path}")
        else:
            print("   ⚠️ 未找到 Everything.exe")
        
        print("\n2. 检查服务安装状态...")
        if service_manager.is_service_installed():
            print("   ✓ Everything 服务已安装")
        else:
            print("   ⚠️ Everything 服务未安装")
        
        print("\n3. 检查服务运行状态...")
        if service_manager.is_service_running():
            print("   ✓ Everything 服务正在运行")
        else:
            print("   ⚠️ Everything 服务未运行")
        
        print("\n4. 获取详细服务状态...")
        status = service_manager.get_service_status()
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        print("\n5. 测试服务启动功能...")
        if service_manager.ensure_service_running():
            print("   ✓ 服务启动成功")
        else:
            print("   ✗ 服务启动失败")
        
        print("\n6. 验证 Everything SDK 连接...")
        time.sleep(3)  # 等待服务完全启动
        
        try:
            from simple_desktop.core.everything_sdk import is_everything_available
            if is_everything_available():
                print("   ✓ Everything SDK 连接成功")
            else:
                print("   ⚠️ Everything SDK 连接失败")
        except Exception as e:
            print(f"   ✗ Everything SDK 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_integration():
    """测试应用集成"""
    print("\n🚀 测试应用集成")
    print("=" * 60)
    
    try:
        # 模拟应用启动过程
        print("1. 导入应用模块...")
        from simple_desktop.core.everything_service import get_service_manager
        from simple_desktop.core.everything_sdk import is_everything_available
        
        print("2. 初始化服务管理器...")
        service_manager = get_service_manager()
        
        print("3. 确保服务运行...")
        if service_manager.ensure_service_running():
            print("   ✓ 服务确保成功")
        else:
            print("   ⚠️ 服务确保失败")
        
        print("4. 等待服务初始化...")
        time.sleep(3)
        
        print("5. 验证搜索功能...")
        if is_everything_available():
            print("   ✓ 搜索功能可用")
            
            # 尝试简单搜索
            try:
                from simple_desktop.core.everything_sdk import get_everything_sdk
                sdk = get_everything_sdk()
                results = sdk.search("*.txt", max_results=5)
                print(f"   ✓ 搜索测试成功，找到 {len(results)} 个结果")
            except Exception as e:
                print(f"   ⚠️ 搜索测试失败: {e}")
        else:
            print("   ✗ 搜索功能不可用")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 应用集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_service_monitoring():
    """测试服务监控功能"""
    print("\n📊 测试服务监控功能")
    print("=" * 60)
    
    try:
        from simple_desktop.core.everything_service import get_service_manager
        
        service_manager = get_service_manager()
        
        # 添加状态回调
        def status_callback(status):
            print(f"   📡 服务状态回调: {status}")
        
        service_manager.add_status_callback(status_callback)
        
        print("1. 启动服务监控...")
        if service_manager.start_monitoring(interval=5.0):
            print("   ✓ 监控启动成功")
        else:
            print("   ✗ 监控启动失败")
        
        print("2. 监控运行中，等待 15 秒...")
        time.sleep(15)
        
        print("3. 停止监控...")
        service_manager.stop_monitoring()
        print("   ✓ 监控已停止")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 服务监控测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 Everything 服务自动启动功能测试")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("服务管理器功能", test_service_manager),
        ("应用集成", test_app_integration),
        ("服务监控", test_service_monitoring),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！Everything 服务自动启动功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查配置和权限。")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生未处理的异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
