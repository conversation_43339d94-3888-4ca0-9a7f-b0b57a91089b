#!/usr/bin/env python3
"""
测试拖拽栏功能
"""

import sys
from PySide6.QtWidgets import QApplication
from simple_desktop.ui.search_window import FloatingSearchWindow

def main():
    """测试拖拽栏功能"""
    print("🚀 测试拖拽栏功能")
    
    app = QApplication(sys.argv)
    
    # 创建搜索窗口
    search_window = FloatingSearchWindow()
    
    print("✅ 搜索窗口已创建")
    print("📝 测试说明:")
    print("   - 窗口顶部应该有一个灰色的拖拽栏")
    print("   - 左侧应该有一个白色圆角矩形拖拽手柄，带有三个点的指示器")
    print("   - 右侧应该有一个白色圆形关闭按钮，内含红色 ✕")
    print("   - 只能通过点击白色拖拽手柄来拖动窗口")
    print("   - 鼠标悬停在拖拽手柄上时光标应该变为移动光标")
    print("   - 点击关闭按钮应该退出程序")
    print("   - ESC键也可以退出程序")
    
    # 显示窗口
    search_window.show_window()
    
    print("\n🎯 测试窗口已启动！")
    print("请测试拖拽和关闭功能")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
