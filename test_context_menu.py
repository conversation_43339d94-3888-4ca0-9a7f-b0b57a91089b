#!/usr/bin/env python3
"""
测试Windows右键菜单集成功能
"""

import sys
import os
import time
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_process_monitor():
    """测试进程监控功能"""
    print("\n=== 测试进程监控功能 ===")
    try:
        from simple_desktop.core.process_monitor import process_monitor
        
        # 测试注册应用程序状态
        print("1. 测试注册应用程序状态...")
        success = process_monitor.register_app_running()
        print(f"   注册结果: {'成功' if success else '失败'}")
        
        # 测试检查应用程序状态
        print("2. 测试检查应用程序状态...")
        is_running = process_monitor.is_app_running()
        print(f"   运行状态: {'运行中' if is_running else '未运行'}")
        
        # 测试获取应用程序信息
        print("3. 测试获取应用程序信息...")
        app_info = process_monitor.get_app_info()
        print(f"   应用信息: {app_info}")
        
        # 测试注销应用程序状态
        print("4. 测试注销应用程序状态...")
        success = process_monitor.unregister_app_running()
        print(f"   注销结果: {'成功' if success else '失败'}")
        
        # 再次检查状态
        print("5. 再次检查应用程序状态...")
        is_running = process_monitor.is_app_running()
        print(f"   运行状态: {'运行中' if is_running else '未运行'}")
        
        print("✓ 进程监控功能测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 进程监控功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_context_menu_manager():
    """测试右键菜单管理器"""
    print("\n=== 测试右键菜单管理器 ===")
    try:
        from simple_desktop.core.context_menu import context_menu_manager
        
        # 测试检查菜单注册状态
        print("1. 检查当前菜单注册状态...")
        is_registered = context_menu_manager.is_context_menu_registered()
        print(f"   当前状态: {'已注册' if is_registered else '未注册'}")
        
        # 测试注册右键菜单
        print("2. 测试注册右键菜单...")
        success = context_menu_manager.register_context_menu()
        print(f"   注册结果: {'成功' if success else '失败'}")
        
        # 再次检查注册状态
        print("3. 再次检查菜单注册状态...")
        is_registered = context_menu_manager.is_context_menu_registered()
        print(f"   当前状态: {'已注册' if is_registered else '未注册'}")
        
        # 测试应用程序运行检测
        print("4. 测试应用程序运行检测...")
        is_app_running = context_menu_manager.is_app_running()
        print(f"   应用运行状态: {'运行中' if is_app_running else '未运行'}")
        
        # 测试注销右键菜单
        print("5. 测试注销右键菜单...")
        success = context_menu_manager.unregister_context_menu()
        print(f"   注销结果: {'成功' if success else '失败'}")
        
        # 最终检查注册状态
        print("6. 最终检查菜单注册状态...")
        is_registered = context_menu_manager.is_context_menu_registered()
        print(f"   最终状态: {'已注册' if is_registered else '未注册'}")
        
        print("✓ 右键菜单管理器测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 右键菜单管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_handler_scripts():
    """测试处理脚本"""
    print("\n=== 测试处理脚本 ===")
    try:
        # 检查脚本文件是否存在
        script_dir = project_root / "simple_desktop" / "scripts"
        
        handler_script = script_dir / "context_menu_handler.py"
        selector_script = script_dir / "profile_selector.py"
        
        print("1. 检查脚本文件...")
        print(f"   处理脚本: {'存在' if handler_script.exists() else '不存在'} - {handler_script}")
        print(f"   选择脚本: {'存在' if selector_script.exists() else '不存在'} - {selector_script}")
        
        if not handler_script.exists() or not selector_script.exists():
            print("   ⚠️ 脚本文件缺失，可能影响功能")
            return False
        
        # 测试脚本语法
        print("2. 测试脚本语法...")
        
        try:
            import py_compile
            py_compile.compile(str(handler_script), doraise=True)
            print("   处理脚本语法: ✓")
        except Exception as e:
            print(f"   处理脚本语法: ✗ ({e})")
            return False
        
        try:
            py_compile.compile(str(selector_script), doraise=True)
            print("   选择脚本语法: ✓")
        except Exception as e:
            print(f"   选择脚本语法: ✗ ({e})")
            return False
        
        print("✓ 处理脚本测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 处理脚本测试失败: {e}")
        return False


def test_profile_integration():
    """测试Profile集成"""
    print("\n=== 测试Profile集成 ===")
    try:
        from simple_desktop.core.config import config_manager
        from simple_desktop.core.profile_manager import profile_manager
        
        # 测试获取Profile信息
        print("1. 测试获取Profile信息...")
        all_profiles = profile_manager.get_all_profiles_info()
        print(f"   Profile数量: {len(all_profiles)}")
        
        for i in range(min(3, len(all_profiles))):  # 只显示前3个
            profile_info = all_profiles.get(i, {})
            name = profile_info.get('name', f'Profile {i}')
            dir_count = profile_info.get('scan_directories_count', 0)
            print(f"   Profile {i}: {name} ({dir_count} 个目录)")
        
        # 测试添加目录到Profile
        print("2. 测试添加目录到Profile...")
        test_dir = str(Path.home() / "Desktop")  # 使用桌面目录作为测试
        
        if os.path.exists(test_dir):
            # 获取Profile 0的当前目录
            current_dirs = profile_manager.get_profile_scan_directories(0)
            print(f"   Profile 0当前目录数: {len(current_dirs)}")
            
            # 添加测试目录
            success = profile_manager.add_directory_to_profile(test_dir, 0)
            print(f"   添加目录结果: {'成功' if success else '失败'}")
            
            # 检查是否添加成功
            new_dirs = profile_manager.get_profile_scan_directories(0)
            print(f"   Profile 0新目录数: {len(new_dirs)}")
            
            # 移除测试目录（清理）
            if test_dir in new_dirs:
                remove_success = profile_manager.remove_directory_from_profile(test_dir, 0)
                print(f"   移除目录结果: {'成功' if remove_success else '失败'}")
        else:
            print(f"   测试目录不存在: {test_dir}")
        
        print("✓ Profile集成测试完成")
        return True
        
    except Exception as e:
        print(f"✗ Profile集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_full_integration():
    """测试完整集成流程"""
    print("\n=== 测试完整集成流程 ===")
    try:
        from simple_desktop.core.process_monitor import process_monitor
        from simple_desktop.core.context_menu import context_menu_manager
        
        print("1. 模拟应用程序启动...")
        
        # 注册应用程序状态
        process_monitor.register_app_running()
        print("   ✓ 应用程序状态已注册")
        
        # 注册右键菜单
        context_menu_manager.register_context_menu()
        print("   ✓ 右键菜单已注册")
        
        print("2. 检查集成状态...")
        
        # 检查应用程序状态
        is_running = process_monitor.is_app_running()
        print(f"   应用程序状态: {'运行中' if is_running else '未运行'}")
        
        # 检查菜单状态
        is_registered = context_menu_manager.is_context_menu_registered()
        print(f"   菜单注册状态: {'已注册' if is_registered else '未注册'}")
        
        print("3. 模拟应用程序退出...")
        
        # 注销应用程序状态
        process_monitor.unregister_app_running()
        print("   ✓ 应用程序状态已注销")
        
        # 注销右键菜单
        context_menu_manager.unregister_context_menu()
        print("   ✓ 右键菜单已注销")
        
        # 清理
        process_monitor.cleanup()
        print("   ✓ 清理完成")
        
        print("✓ 完整集成流程测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 完整集成流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 Windows右键菜单集成功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("进程监控功能", test_process_monitor()))
    test_results.append(("右键菜单管理器", test_context_menu_manager()))
    test_results.append(("处理脚本", test_handler_scripts()))
    test_results.append(("Profile集成", test_profile_integration()))
    test_results.append(("完整集成流程", test_full_integration()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！右键菜单集成功能已准备就绪。")
        print("\n📝 使用说明:")
        print("   1. 启动SimpleDesktop应用程序")
        print("   2. 在Windows资源管理器中右键点击任意文件夹")
        print("   3. 选择'添加到SimpleDesktop'菜单项")
        print("   4. 在弹出的对话框中选择目标Profile")
        print("   5. 确认添加文件夹到选定的Profile")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
