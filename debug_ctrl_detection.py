#!/usr/bin/env python3
"""
调试Ctrl键检测逻辑
"""

import sys
import time
import threading
import ctypes
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Windows API常量
VK_CONTROL = 0x11
DOUBLE_CLICK_INTERVAL = 0.5

def test_ctrl_detection():
    """测试Ctrl键检测"""
    print("🔍 测试Ctrl键检测逻辑")
    print("=" * 50)
    
    user32 = ctypes.windll.user32
    
    last_ctrl_press_time = 0
    ctrl_press_count = 0
    stop_event = threading.Event()
    
    def ctrl_monitor():
        nonlocal last_ctrl_press_time, ctrl_press_count
        
        print("🎯 Ctrl键监控已启动")
        print("请按Ctrl键进行测试...")
        
        last_state = False
        
        while not stop_event.is_set():
            try:
                # 检查Ctrl键状态
                ctrl_state = user32.GetAsyncKeyState(VK_CONTROL)
                is_pressed = bool(ctrl_state & 0x8000)
                
                # 检测按键按下事件（从未按下到按下的转换）
                if is_pressed and not last_state:
                    current_time = time.time()
                    print(f"🔑 Ctrl键按下 - 时间: {current_time:.3f}")
                    
                    # 检查是否是双击
                    if ctrl_press_count > 0:
                        time_diff = current_time - last_ctrl_press_time
                        print(f"   时间间隔: {time_diff:.3f}s")
                        
                        if time_diff <= DOUBLE_CLICK_INTERVAL:
                            print("   🎉 双击Ctrl检测到!")
                            ctrl_press_count = 0
                        else:
                            print("   ⏰ 超时，重置计数")
                            ctrl_press_count = 1
                    else:
                        ctrl_press_count = 1
                    
                    last_ctrl_press_time = current_time
                
                # 检测按键释放事件
                elif not is_pressed and last_state:
                    print(f"🔓 Ctrl键释放")
                
                last_state = is_pressed
                
                # 重置计数器（如果超时）
                if time.time() - last_ctrl_press_time > DOUBLE_CLICK_INTERVAL and ctrl_press_count > 0:
                    print("   ⏰ 计数器超时重置")
                    ctrl_press_count = 0
                
                # 短暂休眠
                time.sleep(0.05)
                
            except Exception as e:
                print(f"❌ Ctrl监控错误: {e}")
                break
    
    # 启动监控线程
    monitor_thread = threading.Thread(target=ctrl_monitor, daemon=True)
    monitor_thread.start()
    
    print("\n📝 测试说明:")
    print("1. 请尝试单击Ctrl键")
    print("2. 请尝试双击Ctrl键")
    print("3. 观察检测结果")
    print("4. 测试将在10秒后自动结束")
    print("5. 或按Enter键提前结束")
    
    # 等待用户输入或超时
    start_time = time.time()
    try:
        while time.time() - start_time < 10:
            if sys.stdin in select.select([sys.stdin], [], [], 0)[0]:
                input()
                break
            time.sleep(0.1)
    except:
        # 如果select不可用，就简单等待
        try:
            import msvcrt
            start_time = time.time()
            while time.time() - start_time < 10:
                if msvcrt.kbhit():
                    msvcrt.getch()
                    break
                time.sleep(0.1)
        except:
            time.sleep(10)
    
    stop_event.set()
    monitor_thread.join(timeout=1.0)
    
    print("\n✅ Ctrl键检测测试完成")

def test_improved_ctrl_detection():
    """测试改进的Ctrl键检测逻辑"""
    print("\n🔍 测试改进的Ctrl键检测逻辑")
    print("=" * 50)
    
    user32 = ctypes.windll.user32
    
    class CtrlDetector:
        def __init__(self):
            self.last_press_time = 0
            self.press_count = 0
            self.last_state = False
            self.callback_count = 0
        
        def on_double_click(self):
            self.callback_count += 1
            print(f"🎉 双击回调 #{self.callback_count} 被触发!")
        
        def check_ctrl_state(self):
            ctrl_state = user32.GetAsyncKeyState(VK_CONTROL)
            is_pressed = bool(ctrl_state & 0x8000)
            
            # 检测按键按下事件（边沿触发）
            if is_pressed and not self.last_state:
                current_time = time.time()
                
                # 如果有之前的按键记录
                if self.press_count > 0:
                    time_diff = current_time - self.last_press_time
                    
                    if time_diff <= DOUBLE_CLICK_INTERVAL:
                        # 双击检测到
                        self.on_double_click()
                        self.press_count = 0
                        self.last_press_time = 0
                        self.last_state = is_pressed
                        return
                
                # 记录新的按键
                self.press_count = 1
                self.last_press_time = current_time
                print(f"🔑 Ctrl按下 #{self.press_count} - 时间: {current_time:.3f}")
            
            # 重置超时的计数
            current_time = time.time()
            if (self.press_count > 0 and 
                current_time - self.last_press_time > DOUBLE_CLICK_INTERVAL):
                print("   ⏰ 重置超时计数")
                self.press_count = 0
            
            self.last_state = is_pressed
    
    detector = CtrlDetector()
    stop_event = threading.Event()
    
    def monitor_loop():
        print("🎯 改进的Ctrl监控已启动")
        while not stop_event.is_set():
            detector.check_ctrl_state()
            time.sleep(0.05)
    
    # 启动监控
    monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
    monitor_thread.start()
    
    print("\n📝 改进版测试说明:")
    print("1. 使用边沿触发检测Ctrl按键")
    print("2. 请尝试双击Ctrl键")
    print("3. 观察是否能正确检测双击")
    print("4. 测试将在10秒后自动结束")
    
    # 等待测试
    time.sleep(10)
    
    stop_event.set()
    monitor_thread.join(timeout=1.0)
    
    print(f"\n📊 改进版测试结果:")
    print(f"   双击检测次数: {detector.callback_count}")
    
    if detector.callback_count > 0:
        print("   ✅ 改进的检测逻辑工作正常")
        return True
    else:
        print("   ❌ 改进的检测逻辑仍有问题")
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - Ctrl键检测调试")
    print("=" * 60)
    
    # 基础检测测试
    test_ctrl_detection()
    
    # 改进版检测测试
    improved_works = test_improved_ctrl_detection()
    
    print("\n" + "=" * 60)
    print("🎯 Ctrl键检测调试完成！")
    
    if improved_works:
        print("\n💡 建议:")
        print("1. 使用改进的边沿触发检测逻辑")
        print("2. 确保正确处理按键状态转换")
        print("3. 优化双击时间间隔判断")
    else:
        print("\n⚠️ 可能的问题:")
        print("1. Windows API调用权限不足")
        print("2. 其他应用程序干扰按键检测")
        print("3. 系统级按键钩子冲突")

if __name__ == "__main__":
    # 导入select模块用于输入检测
    try:
        import select
    except ImportError:
        select = None
    
    main()
