#!/usr/bin/env python3
"""
测试文件类型过滤
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_file_type_filter():
    """测试文件类型过滤"""
    print("🔍 测试文件类型过滤")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        engine = FileSearchEngine(profile_id=0)
        sdk = get_everything_sdk()
        scan_dirs = engine.get_scan_directories()
        
        # 测试查询构建
        print("1. 查询构建测试:")
        
        # 测试可执行文件查询
        exe_query = engine._build_everything_query_with_directories(
            "微信", scan_dirs, ["executables"], True
        )
        print(f"   可执行文件查询: {exe_query}")
        
        # 直接测试Everything SDK
        print("\n2. Everything SDK测试:")
        try:
            sdk_results = sdk.search(exe_query, max_results=10)
            print(f"   结果数量: {len(sdk_results)}")
            
            for i, result in enumerate(sdk_results):
                icon = "🔗" if result.filename.lower().endswith(".lnk") else "⚙️"
                print(f"     {i+1}. {icon} {result.filename}")
                print(f"        路径: {result.full_path}")
                print(f"        扩展名: '{result.extension}'")
                
        except Exception as e:
            print(f"   ❌ Everything SDK查询失败: {e}")
        
        # 测试简化查询
        print("\n3. 简化查询测试:")
        simple_queries = [
            "微信 ext:lnk",
            "微信 (ext:exe | ext:lnk)",
            "微信 ext:exe",
        ]
        
        for query in simple_queries:
            print(f"   查询: {query}")
            try:
                results = sdk.search(query, max_results=5)
                print(f"   结果: {len(results)}")
                
                for i, result in enumerate(results):
                    print(f"     {i+1}. {result.filename} (ext: {result.extension})")
                    
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        # 测试搜索引擎
        print("\n4. 搜索引擎测试:")
        
        # 检查文件类型定义
        file_types = engine.file_type_extensions
        print(f"   executables类型包含: {file_types.get('executables', [])}")
        
        # 测试搜索引擎的可执行文件搜索
        try:
            engine_results = engine.search("微信", limit=10, file_types=["executables"])
            print(f"   搜索引擎结果: {len(engine_results)}")
            
            for i, result in enumerate(engine_results):
                print(f"     {i+1}. {result.filename} (suffix: {result.suffix})")
                print(f"        路径: {result.filepath}")
                
        except Exception as e:
            print(f"   ❌ 搜索引擎查询失败: {e}")
        
        # 测试_should_include_result方法
        print("\n5. _should_include_result方法测试:")
        
        # 创建测试结果
        class TestResult:
            def __init__(self, filename, suffix, item_type):
                self.filename = filename
                self.suffix = suffix
                self.item_type = item_type
        
        test_result = TestResult("微信.lnk", ".lnk", "file")
        
        should_include = engine._should_include_result(
            test_result, ["executables"], True
        )
        print(f"   微信.lnk应该包含在executables中: {should_include}")
        
        # 检查.lnk是否在executables中
        if ".lnk" in file_types.get("executables", []):
            print(f"   ✅ .lnk在executables分类中")
        else:
            print(f"   ❌ .lnk不在executables分类中")
        
        print("\n✅ 文件类型过滤测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 文件类型过滤测试")
    print("=" * 60)
    
    test_file_type_filter()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")

if __name__ == "__main__":
    main()
