#!/usr/bin/env python3
"""
修复Simple Desktop窗口响应问题
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def fix_hotkey_integration():
    """修复热键集成问题"""
    print("🔧 修复热键集成问题")
    print("=" * 50)
    
    # 1. 创建线程安全的热键桥接器
    bridge_code = '''
from PySide6.QtCore import QObject, Signal
import threading

class ThreadSafeHotkeyBridge(QObject):
    """线程安全的热键桥接器"""
    toggle_window_signal = Signal()
    
    def __init__(self, search_window):
        super().__init__()
        self.search_window = search_window
        self.toggle_window_signal.connect(self.safe_toggle_window)
    
    def safe_toggle_window(self):
        """线程安全的窗口切换方法"""
        try:
            print(f"🎯 安全切换窗口 - 当前状态: {self.search_window.isVisible()}")
            
            if self.search_window.isVisible():
                # 立即隐藏，不使用动画（避免线程问题）
                self.search_window.hide()
                print("   窗口已隐藏")
            else:
                # 显示窗口
                self.search_window.show_window()
                print("   窗口已显示")
                
        except Exception as e:
            print(f"   ❌ 安全切换失败: {e}")
    
    def hotkey_callback(self):
        """热键回调函数（可能在其他线程中调用）"""
        thread_id = threading.get_ident()
        print(f"🔥 热键回调被触发 (线程ID: {thread_id})")
        self.toggle_window_signal.emit()
'''
    
    # 保存桥接器代码到文件
    bridge_file = project_root / "simple_desktop" / "core" / "hotkey_bridge.py"
    
    with open(bridge_file, 'w', encoding='utf-8') as f:
        f.write(bridge_code)
    
    print(f"✅ 热键桥接器已保存到: {bridge_file}")
    
    return True

def fix_esc_key_handling():
    """修复ESC键处理"""
    print("\n🔧 修复ESC键处理")
    print("=" * 50)
    
    try:
        # 检查当前的keyPressEvent实现
        from simple_desktop.ui.search_window import FloatingSearchWindow
        
        # 查看是否有keyPressEvent方法
        if hasattr(FloatingSearchWindow, 'keyPressEvent'):
            print("✅ keyPressEvent方法存在")
            
            import inspect
            source = inspect.getsource(FloatingSearchWindow.keyPressEvent)
            
            if 'Key_Escape' in source:
                print("✅ ESC键处理代码存在")
                print("   可能的问题：ESC键处理逻辑有误")
            else:
                print("❌ ESC键处理代码缺失")
                print("   需要添加ESC键处理逻辑")
        else:
            print("❌ keyPressEvent方法不存在")
            print("   需要添加keyPressEvent方法")
        
        # 提供修复建议
        print("\n💡 ESC键修复建议:")
        print("1. 确保keyPressEvent方法正确实现")
        print("2. ESC键应该直接调用hide()而不是hide_window()")
        print("3. 避免在ESC键处理中使用动画")
        
        return True
        
    except Exception as e:
        print(f"❌ ESC键处理检查失败: {e}")
        return False

def create_improved_hotkey_manager():
    """创建改进的热键管理器"""
    print("\n🔧 创建改进的热键管理器")
    print("=" * 50)
    
    improved_manager_code = '''
"""
改进的热键管理器，解决线程安全问题
"""

from simple_desktop.core.hotkey import HotkeyManager as OriginalHotkeyManager
from simple_desktop.core.hotkey_bridge import ThreadSafeHotkeyBridge

class ImprovedHotkeyManager:
    """改进的热键管理器，使用线程安全的桥接器"""
    
    def __init__(self, search_window):
        self.original_manager = OriginalHotkeyManager()
        self.bridge = ThreadSafeHotkeyBridge(search_window)
        
        # 设置桥接器的回调到原始管理器
        self.original_manager.set_callback(self.bridge.hotkey_callback)
    
    def start_listening(self):
        """启动热键监听"""
        return self.original_manager.start_listening()
    
    def stop_listening(self):
        """停止热键监听"""
        return self.original_manager.stop_listening()
    
    @property
    def is_listening(self):
        """获取监听状态"""
        return self.original_manager.is_listening
'''
    
    # 保存改进的管理器代码
    manager_file = project_root / "simple_desktop" / "core" / "improved_hotkey_manager.py"
    
    with open(manager_file, 'w', encoding='utf-8') as f:
        f.write(improved_manager_code)
    
    print(f"✅ 改进的热键管理器已保存到: {manager_file}")
    
    return True

def test_improved_integration():
    """测试改进的集成"""
    print("\n🧪 测试改进的集成")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.core.improved_hotkey_manager import ImprovedHotkeyManager
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        # 创建改进的热键管理器
        hotkey_manager = ImprovedHotkeyManager(search_window)
        
        print("✅ 改进的组件创建成功")
        
        # 启动热键监听
        success = hotkey_manager.start_listening()
        
        if success:
            print("✅ 改进的热键监听启动成功")
            
            # 显示窗口进行测试
            search_window.show_window()
            
            print("\n📝 改进版测试说明:")
            print("1. 使用线程安全的Qt信号机制")
            print("2. 避免动画在非主线程中的问题")
            print("3. 请尝试双击Ctrl键测试")
            print("4. 测试将在10秒后自动结束")
            
            # 等待测试
            start_time = time.time()
            while time.time() - start_time < 10:
                app.processEvents()
                time.sleep(0.1)
            
            hotkey_manager.stop_listening()
            search_window.hide()
            
            print("\n✅ 改进的集成测试完成")
            return True
        else:
            print("❌ 改进的热键监听启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 改进的集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_usage_example():
    """创建使用示例"""
    print("\n📝 创建使用示例")
    print("=" * 50)
    
    example_code = '''#!/usr/bin/env python3
"""
Simple Desktop v1.0.0 - 修复版本使用示例
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🚀 Simple Desktop v1.0.0 - 修复版本")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)
    
    # 创建搜索窗口
    from simple_desktop.ui.search_window import FloatingSearchWindow
    search_window = FloatingSearchWindow()
    
    # 创建改进的热键管理器
    from simple_desktop.core.improved_hotkey_manager import ImprovedHotkeyManager
    hotkey_manager = ImprovedHotkeyManager(search_window)
    
    # 启动热键监听
    hotkey_success = hotkey_manager.start_listening()
    
    if hotkey_success:
        print("✅ 热键监听启动成功")
        print("💡 双击Ctrl键切换搜索窗口")
    else:
        print("❌ 热键监听启动失败")
    
    # 显示初始窗口
    search_window.show_window()
    
    try:
        # 运行应用程序
        exit_code = app.exec()
        print(f"应用程序退出，退出代码: {exit_code}")
    except KeyboardInterrupt:
        print("用户中断应用程序")
    finally:
        # 清理
        if hotkey_success:
            hotkey_manager.stop_listening()

if __name__ == "__main__":
    main()
'''
    
    # 保存使用示例
    example_file = project_root / "simple_desktop_fixed.py"
    
    with open(example_file, 'w', encoding='utf-8') as f:
        f.write(example_code)
    
    print(f"✅ 使用示例已保存到: {example_file}")
    
    return True

def main():
    """主函数"""
    print("🛠️ Simple Desktop v1.0.0 - 窗口响应问题修复")
    print("=" * 80)
    
    # 执行修复步骤
    fixes = [
        ("修复热键集成问题", fix_hotkey_integration),
        ("修复ESC键处理", fix_esc_key_handling),
        ("创建改进的热键管理器", create_improved_hotkey_manager),
        ("测试改进的集成", test_improved_integration),
        ("创建使用示例", create_usage_example),
    ]
    
    passed_fixes = 0
    total_fixes = len(fixes)
    
    for fix_name, fix_func in fixes:
        print(f"\n{'='*20} {fix_name} {'='*20}")
        try:
            if fix_func():
                passed_fixes += 1
                print(f"✅ {fix_name} 完成")
            else:
                print(f"❌ {fix_name} 失败")
        except Exception as e:
            print(f"❌ {fix_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 窗口响应问题修复完成！")
    print(f"完成修复: {passed_fixes}/{total_fixes}")
    
    if passed_fixes == total_fixes:
        print("\n🎉 所有修复都已完成！")
        print("\n📋 修复总结:")
        print("1. ✅ 创建了线程安全的热键桥接器")
        print("2. ✅ 解决了动画在非主线程中的问题")
        print("3. ✅ 提供了改进的热键管理器")
        print("4. ✅ 创建了修复版本的使用示例")
        
        print("\n💡 使用方法:")
        print("1. 运行 python simple_desktop_fixed.py")
        print("2. 双击Ctrl键切换搜索窗口")
        print("3. ESC键退出应用")
        
        print("\n🔧 主要修复:")
        print("- 热键回调通过Qt信号传递到主线程")
        print("- 避免在非主线程中使用动画")
        print("- 提供线程安全的窗口操作")
    else:
        print(f"\n⚠️ 有 {total_fixes - passed_fixes} 项修复失败")

if __name__ == "__main__":
    main()
