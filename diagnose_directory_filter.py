# -*- coding: utf-8 -*-
"""
诊断目录过滤器问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def diagnose_directory_filtering():
    """诊断目录过滤问题"""
    print("诊断目录过滤器问题")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.profile_manager import profile_manager
        
        engine = FileSearchEngine(profile_id=0)
        
        # 1. 检查Profile配置
        print("1. 检查Profile配置:")
        scan_dirs = profile_manager.get_profile_scan_directories(0)
        print(f"   扫描目录: {scan_dirs}")
        
        # 2. 测试搜索结果
        print("\n2. 测试搜索结果:")
        query = "cur"
        results = engine.search(query, limit=10)
        print(f"   查询 '{query}' 结果数: {len(results)}")
        
        # 分析结果路径
        desktop_files = []
        d_drive_files = []
        other_files = []
        
        for result in results:
            if any(result.filepath.startswith(scan_dir) for scan_dir in scan_dirs):
                desktop_files.append(result)
            elif result.filepath.startswith("D:"):
                d_drive_files.append(result)
            else:
                other_files.append(result)
        
        print(f"   桌面目录文件: {len(desktop_files)}")
        print(f"   D盘文件: {len(d_drive_files)}")
        print(f"   其他文件: {len(other_files)}")
        
        # 显示D盘文件（问题文件）
        if d_drive_files:
            print("\n   ❌ 发现D盘文件（不应该出现）:")
            for file in d_drive_files[:5]:
                print(f"      {file.filename} - {file.filepath}")
        
        # 3. 测试全局搜索
        print("\n3. 测试全局搜索:")
        global_results = engine.search(query, limit=10, global_search=True)
        print(f"   全局搜索结果数: {len(global_results)}")

        global_d_files = [r for r in global_results if r.filepath.startswith("D:")]
        if global_d_files:
            print(f"   ✅ 全局搜索有D盘文件: {len(global_d_files)}")
        else:
            print(f"   ❌ 全局搜索无D盘文件")

        # 4. 测试智能搜索（带回退）
        print("\n4. 测试智能搜索:")
        smart_results = engine.search_with_fallback(query, limit=10)
        print(f"   智能搜索结果数: {len(smart_results)}")

        smart_d_files = [r for r in smart_results if r.filepath.startswith("D:")]
        smart_desktop_files = [r for r in smart_results if any(r.filepath.startswith(scan_dir) for scan_dir in scan_dirs)]

        print(f"   桌面文件: {len(smart_desktop_files)}")
        print(f"   D盘文件: {len(smart_d_files)}")

        if len(smart_desktop_files) > 0:
            print("   ✅ 优先显示桌面文件")
        if len(smart_d_files) > 0:
            print("   ✅ 回退到全局搜索")
        
        # 5. 测试目录过滤器
        print("\n5. 测试目录过滤器:")
        from simple_desktop.search.filters import DirectoryFilter
        from simple_desktop.search.models import SearchResult
        
        dir_filter = DirectoryFilter(scan_dirs)
        
        # 创建测试文件
        test_files = [
            ("desktop_file.txt", "C:\\Users\\<USER>\\Desktop\\test.txt"),
            ("d_drive_file.txt", "D:\\test\\file.txt"),
        ]
        
        for filename, filepath in test_files:
            mock_result = SearchResult(
                filename=filename,
                filepath=filepath,
                item_type="file",
                size=100,
                suffix=".txt"
            )
            
            should_include = dir_filter.should_include(mock_result)
            print(f"   {filepath}: {'✅ 通过' if should_include else '❌ 被过滤'}")
        
        # 6. 检查当前搜索方法的默认行为
        print("\n6. 检查搜索方法默认行为:")
        
        # 检查search方法的默认参数
        import inspect
        search_signature = inspect.signature(engine.search)
        print(f"   search方法签名: {search_signature}")
        
        # 检查_search_with_filters中的逻辑
        print("\n7. 手动测试过滤逻辑:")
        from simple_desktop.search.filters import FilterCriteria
        
        # 测试无目录限制
        criteria_no_limit = FilterCriteria(
            file_types=None,
            include_folders=True,
            scan_directories=None  # 无限制
        )
        
        # 测试有目录限制
        criteria_with_limit = FilterCriteria(
            file_types=None,
            include_folders=True,
            scan_directories=scan_dirs  # 有限制
        )
        
        print(f"   无限制条件: scan_directories = {criteria_no_limit.scan_directories}")
        print(f"   有限制条件: scan_directories = {criteria_with_limit.scan_directories}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_directory_filtering()
