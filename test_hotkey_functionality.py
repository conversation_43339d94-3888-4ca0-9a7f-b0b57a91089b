#!/usr/bin/env python3
"""
测试Simple Desktop热键功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_hotkey_functionality():
    """测试热键功能"""
    print("🔍 Simple Desktop - 热键功能测试")
    print("=" * 60)
    
    try:
        from simple_desktop.core.hotkey import HotkeyManager
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建组件
        search_window = FloatingSearchWindow()
        hotkey_manager = HotkeyManager()
        
        print("✅ 组件创建成功")
        
        # 测试计数器
        toggle_count = 0
        
        def hotkey_callback():
            nonlocal toggle_count
            toggle_count += 1
            print(f"\n🎯 热键触发 #{toggle_count}")
            
            try:
                before_visible = search_window.isVisible()
                search_window.toggle_window()
                after_visible = search_window.isVisible()
                
                print(f"   窗口状态: {'显示' if before_visible else '隐藏'} -> {'显示' if after_visible else '隐藏'}")
                
                if before_visible != after_visible:
                    print("   ✅ 窗口切换成功")
                else:
                    print("   ❌ 窗口切换失败")
                    
            except Exception as e:
                print(f"   ❌ 窗口切换异常: {e}")
        
        # 设置回调并启动热键监听
        hotkey_manager.set_callback(hotkey_callback)
        success = hotkey_manager.start_listening()
        
        if success:
            print("✅ 热键监听启动成功")
            
            # 显示窗口以便观察
            search_window.show_window()
            print("✅ 搜索窗口已显示")
            
            print("\n📝 测试说明:")
            print("1. 热键监听已启动")
            print("2. 请尝试双击Ctrl键")
            print("3. 观察窗口是否正确切换显示/隐藏状态")
            print("4. 测试将在15秒后自动结束")
            print("5. 或按Ctrl+C提前结束测试")
            
            # 等待用户测试
            start_time = time.time()
            try:
                while time.time() - start_time < 15:
                    app.processEvents()
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print("\n⏹️ 用户中断测试")
            
            print(f"\n📊 测试结果:")
            print(f"   热键触发次数: {toggle_count}")
            
            if toggle_count > 0:
                print("   ✅ 热键功能正常工作")
                result = True
            else:
                print("   ❌ 热键功能未响应")
                result = False
            
            # 清理
            hotkey_manager.stop_listening()
            search_window.hide_window()
            
            print("✅ 清理完成")
            
            return result
            
        else:
            print("❌ 热键监听启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hotkey_integration():
    """测试热键与应用程序的完整集成"""
    print("\n🔗 测试热键与应用程序的完整集成")
    print("=" * 60)
    
    try:
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.core.hotkey import hotkey_manager
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        print("✅ 搜索窗口创建成功")
        
        # 设置热键回调
        def integrated_callback():
            print("🎯 集成热键回调被触发")
            search_window.toggle_window()
        
        hotkey_manager.set_callback(integrated_callback)
        
        # 启动热键监听
        success = hotkey_manager.start_listening()
        
        if success:
            print("✅ 集成热键监听启动成功")
            
            # 显示窗口
            search_window.show_window()
            
            print("\n📝 集成测试说明:")
            print("1. 这是完整的应用程序集成测试")
            print("2. 双击Ctrl键应该切换搜索窗口")
            print("3. 测试将在10秒后自动结束")
            
            # 运行测试
            start_time = time.time()
            try:
                while time.time() - start_time < 10:
                    app.processEvents()
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print("\n⏹️ 用户中断测试")
            
            # 清理
            hotkey_manager.stop_listening()
            search_window.hide_window()
            
            print("\n✅ 集成测试完成")
            return True
            
        else:
            print("❌ 集成热键监听启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Simple Desktop - 热键功能完整测试")
    print("=" * 80)
    
    # 基础功能测试
    basic_test_passed = test_hotkey_functionality()
    
    if basic_test_passed:
        # 集成测试
        integration_test_passed = test_hotkey_integration()
        
        print("\n" + "=" * 80)
        print("🎯 热键功能测试完成！")
        
        if basic_test_passed and integration_test_passed:
            print("\n🎉 热键功能测试全部通过！")
            print("✅ 双击Ctrl键可以正确切换搜索窗口")
            print("✅ 热键监听功能正常工作")
            print("✅ 窗口显示/隐藏逻辑正确")
            
            print("\n💡 使用说明:")
            print("- 双击Ctrl键：切换Simple Desktop搜索窗口")
            print("- Ctrl+0~9：切换Profile（如果配置了多个Profile）")
            print("- 热键在全局范围内工作，无论当前焦点在哪个应用程序")
        else:
            print("\n⚠️ 部分测试失败，请检查相关配置")
    else:
        print("\n❌ 基础热键功能测试失败")
        print("请检查:")
        print("1. 是否以管理员权限运行")
        print("2. 是否与其他应用程序的热键冲突")
        print("3. Windows API调用是否正常")

if __name__ == "__main__":
    main()
