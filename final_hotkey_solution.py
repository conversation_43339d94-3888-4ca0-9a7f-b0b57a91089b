#!/usr/bin/env python3
"""
Simple Desktop热键问题的最终解决方案
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_qt_compatible_hotkey_manager():
    """创建与Qt兼容的热键管理器"""
    print("🔧 创建与Qt兼容的热键管理器")
    print("=" * 50)
    
    qt_compatible_code = '''
"""
与Qt兼容的热键管理器 - 使用Qt的事件系统
"""

import time
import threading
import ctypes
import ctypes.wintypes as wintypes
from typing import Optional, Callable
from PySide6.QtCore import QObject, QTimer, Signal

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_CONTROL = 0x11
VK_0 = 0x30
DOUBLE_CLICK_INTERVAL = 0.5

class QtCompatibleHotkeyManager(QObject):
    """与Qt兼容的热键管理器"""
    
    # Qt信号
    profile_switch_signal = Signal(int)
    toggle_window_signal = Signal()
    
    def __init__(self):
        """初始化热键管理器"""
        super().__init__()
        
        self.is_listening = False
        self.toggle_callback: Optional[Callable[[], None]] = None
        self.profile_switch_callback: Optional[Callable[[int], None]] = None
        
        # 双击Ctrl检测
        self.last_ctrl_press_time = 0
        self.ctrl_press_count = 0
        self.ctrl_check_thread = None
        self.stop_event = threading.Event()
        
        # Windows API函数
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
        # 注册的热键ID
        self.registered_hotkeys = set()
        
        # 消息检查定时器
        self.message_timer = QTimer()
        self.message_timer.timeout.connect(self._check_messages)
        
        # 连接信号到回调
        self.profile_switch_signal.connect(self._handle_profile_switch)
        self.toggle_window_signal.connect(self._handle_toggle_window)
        
        print("[QT-HOTKEY] Qt兼容热键管理器已初始化")
    
    def set_callback(self, callback: Callable[[], None]):
        """设置窗口切换回调"""
        self.toggle_callback = callback
        print("[QT-HOTKEY] 窗口切换回调已设置")
    
    def set_profile_switch_callback(self, callback: Callable[[int], None]):
        """设置Profile切换回调"""
        self.profile_switch_callback = callback
        print("[QT-HOTKEY] Profile切换回调已设置")
    
    def start_listening(self) -> bool:
        """开始监听快捷键"""
        if self.is_listening:
            return True
        
        try:
            print("[QT-HOTKEY] 开始注册Profile热键...")
            
            # 注册Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            successful_registrations = 0
            
            for i in range(10):
                hotkey_id = 1000 + i
                vk_code = VK_0 + i
                
                # 尝试注册热键
                result = self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
                if result:
                    self.registered_hotkeys.add(hotkey_id)
                    successful_registrations += 1
                    print(f"[QT-HOTKEY] Ctrl+{i} 注册成功")
                else:
                    error_code = ctypes.GetLastError()
                    print(f"[QT-HOTKEY] Ctrl+{i} 注册失败 (错误: {error_code})")
            
            print(f"[QT-HOTKEY] Profile热键注册完成: {successful_registrations}/10")
            
            if successful_registrations == 0:
                print("[ERROR] 没有成功注册任何Profile热键")
                return False
            
            # 启动双击Ctrl检测线程
            self.stop_event.clear()
            self.ctrl_check_thread = threading.Thread(target=self._ctrl_monitor_thread, daemon=True)
            self.ctrl_check_thread.start()
            
            # 启动消息检查定时器（使用Qt定时器）
            self.message_timer.start(10)  # 每10ms检查一次消息
            
            self.is_listening = True
            print("[QT-HOTKEY] Qt兼容热键监听已启动")
            return True
            
        except Exception as e:
            print(f"[ERROR] 热键监听启动失败: {e}")
            self.stop_listening()
            return False
    
    def stop_listening(self):
        """停止监听快捷键"""
        if not self.is_listening:
            return
        
        print("[QT-HOTKEY] 停止热键监听...")
        self.is_listening = False
        self.stop_event.set()
        
        # 停止消息检查定时器
        self.message_timer.stop()
        
        # 注销热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()
        
        # 等待线程结束
        if self.ctrl_check_thread and self.ctrl_check_thread.is_alive():
            self.ctrl_check_thread.join(timeout=1.0)
        
        print("[QT-HOTKEY] Qt兼容热键监听已停止")
    
    def _check_messages(self):
        """检查Windows消息（在Qt主线程中执行）"""
        msg = wintypes.MSG()
        
        # 使用PeekMessage非阻塞检查消息
        while self.user32.PeekMessageW(ctypes.byref(msg), None, WM_HOTKEY, WM_HOTKEY, 1):  # PM_REMOVE
            if msg.message == WM_HOTKEY:
                hotkey_id = msg.wParam
                print(f"[QT-HOTKEY] 在Qt主线程中收到热键消息: ID={hotkey_id}")
                self._handle_qt_hotkey(hotkey_id)
    
    def _handle_qt_hotkey(self, hotkey_id: int):
        """在Qt主线程中处理热键消息"""
        if 1000 <= hotkey_id <= 1009:
            # Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            profile_id = hotkey_id - 1000
            print(f"[QT-HOTKEY] 检测到Profile热键: Ctrl+{profile_id}")
            
            # 发射Qt信号
            self.profile_switch_signal.emit(profile_id)
    
    def _handle_profile_switch(self, profile_id: int):
        """处理Profile切换信号"""
        print(f"[QT-HOTKEY] 处理Profile切换信号: Profile {profile_id}")
        
        if self.profile_switch_callback:
            try:
                print(f"[QT-HOTKEY] 执行Profile切换回调: Profile {profile_id}")
                self.profile_switch_callback(profile_id)
                print(f"[QT-HOTKEY] Profile切换回调执行完成")
            except Exception as e:
                print(f"[ERROR] Profile切换回调错误: {e}")
        else:
            print("[WARN] Profile切换回调未设置")
    
    def _handle_toggle_window(self):
        """处理窗口切换信号"""
        if self.toggle_callback:
            try:
                self.toggle_callback()
                print("[QT-HOTKEY] 窗口切换回调执行完成")
            except Exception as e:
                print(f"[ERROR] 窗口切换回调错误: {e}")
    
    def _ctrl_monitor_thread(self):
        """监控Ctrl键双击的线程"""
        last_state = False
        
        while not self.stop_event.is_set():
            try:
                # 检查Ctrl键状态
                ctrl_state = self.user32.GetAsyncKeyState(VK_CONTROL)
                is_pressed = bool(ctrl_state & 0x8000)
                
                # 边沿触发：检测从未按下到按下的转换
                if is_pressed and not last_state:
                    current_time = time.time()
                    
                    # 如果有之前的按键记录，检查双击
                    if self.ctrl_press_count > 0:
                        time_diff = current_time - self.last_ctrl_press_time
                        
                        if time_diff <= DOUBLE_CLICK_INTERVAL:
                            # 双击Ctrl检测到，发射Qt信号
                            self.toggle_window_signal.emit()
                            print("[QT-HOTKEY] 双击Ctrl检测到")
                            
                            # 重置状态
                            self.ctrl_press_count = 0
                            self.last_ctrl_press_time = 0
                            last_state = is_pressed
                            continue
                    
                    # 记录新的按键
                    self.last_ctrl_press_time = current_time
                    self.ctrl_press_count = 1
                
                # 重置超时的计数
                current_time = time.time()
                if (self.ctrl_press_count > 0 and 
                    current_time - self.last_ctrl_press_time > DOUBLE_CLICK_INTERVAL):
                    self.ctrl_press_count = 0
                
                last_state = is_pressed
                
                # 短暂休眠
                time.sleep(0.05)
                
            except Exception as e:
                print(f"[ERROR] Ctrl监控错误: {e}")
                break

# 创建全局实例
hotkey_manager = QtCompatibleHotkeyManager()
'''
    
    try:
        # 保存Qt兼容的热键管理器
        qt_file = project_root / "simple_desktop" / "core" / "qt_hotkey.py"
        
        with open(qt_file, 'w', encoding='utf-8') as f:
            f.write(qt_compatible_code)
        
        print(f"✅ Qt兼容热键管理器已保存到: {qt_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建Qt兼容版本失败: {e}")
        return False

def update_app_to_use_qt_hotkey():
    """更新app.py使用Qt兼容的热键管理器"""
    print("\n🔧 更新app.py使用Qt兼容的热键管理器")
    print("=" * 50)
    
    try:
        # 读取当前的app.py
        app_file = project_root / "simple_desktop" / "app.py"
        
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入语句
        if "from .core.enhanced_hotkey import hotkey_manager" in content:
            new_content = content.replace(
                "from .core.enhanced_hotkey import hotkey_manager",
                "from .core.qt_hotkey import hotkey_manager"
            )
            
            # 保存修改后的文件
            with open(app_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ app.py已更新为使用Qt兼容的热键管理器")
            return True
        else:
            print("⚠️ 在app.py中未找到enhanced_hotkey导入语句")
            return False
        
    except Exception as e:
        print(f"❌ 更新app.py失败: {e}")
        return False

def test_qt_hotkey_manager():
    """测试Qt兼容的热键管理器"""
    print("\n🧪 测试Qt兼容的热键管理器")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from simple_desktop.core.qt_hotkey import hotkey_manager
        
        # 创建Qt应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ Qt兼容热键管理器导入成功")
        
        # 设置测试回调
        profile_switches = []
        
        def test_profile_callback(profile_id):
            profile_switches.append(profile_id)
            print(f"[TEST] Profile切换回调触发: {profile_id}")
        
        hotkey_manager.set_profile_switch_callback(test_profile_callback)
        
        # 启动热键监听
        success = hotkey_manager.start_listening()
        
        if success:
            print("✅ Qt兼容热键管理器启动成功")
            
            print("\n📝 Qt兼容版测试说明:")
            print("1. Qt兼容热键管理器已启动")
            print("2. 使用Qt定时器检查Windows消息")
            print("3. 使用Qt信号系统处理热键事件")
            print("4. 请按 Ctrl+0 到 Ctrl+9 测试Profile切换")
            print("5. 测试将在20秒后自动结束")
            
            # 运行Qt事件循环
            import time
            start_time = time.time()
            while time.time() - start_time < 20:
                app.processEvents()
                time.sleep(0.01)
            
            print(f"\n📊 Qt兼容版测试结果:")
            print(f"   Profile切换次数: {len(profile_switches)}")
            
            if profile_switches:
                print("   ✅ Qt兼容Profile热键功能正常")
                print(f"   切换的Profile: {profile_switches}")
            else:
                print("   ❌ Qt兼容Profile热键功能仍有问题")
            
            # 停止监听
            hotkey_manager.stop_listening()
            
            return len(profile_switches) > 0
        else:
            print("❌ Qt兼容热键管理器启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试Qt兼容热键管理器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 热键问题最终解决方案")
    print("=" * 80)
    
    print("💡 问题分析结果:")
    print("- 热键系统本身完全正常（已验证）")
    print("- Windows消息循环工作正常（已验证）")
    print("- 问题在于与Qt事件循环的冲突")
    print("- 解决方案：使用Qt定时器检查Windows消息")
    
    # 执行最终修复步骤
    steps = [
        ("创建Qt兼容的热键管理器", create_qt_compatible_hotkey_manager),
        ("更新app.py使用Qt兼容版本", update_app_to_use_qt_hotkey),
        ("测试Qt兼容的热键管理器", test_qt_hotkey_manager),
    ]
    
    passed_steps = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            if step_func():
                passed_steps += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 热键问题最终解决方案完成！")
    print(f"完成步骤: {passed_steps}/{total_steps}")
    
    if passed_steps >= 2:
        print("\n🎉 最终解决方案已实施！")
        print("\n💡 技术要点:")
        print("- 使用Qt定时器替代独立的Windows消息循环")
        print("- 在Qt主线程中处理热键消息")
        print("- 使用Qt信号系统确保线程安全")
        print("- 完全兼容Qt应用程序架构")
        
        print("\n📋 下一步:")
        print("1. 重新启动Simple Desktop: python main.py")
        print("2. 测试Ctrl+0~9热键")
        print("3. 观察详细的调试日志")
        print("4. 验证Profile切换功能")
    else:
        print(f"\n⚠️ 最终解决方案未完成")

if __name__ == "__main__":
    main()
