#!/usr/bin/env python3
"""
调试子文件夹搜索问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def step1_verify_directory_existence():
    """步骤1：验证目录存在性"""
    print("=" * 60)
    print("步骤1：验证目录存在性")
    print("=" * 60)
    
    desktop_path = Path.home() / "Desktop"
    sjl_path = desktop_path / "sjl"
    
    print(f"桌面路径: {desktop_path}")
    print(f"桌面路径存在: {desktop_path.exists()}")
    print(f"sjl目录路径: {sjl_path}")
    print(f"sjl目录存在: {sjl_path.exists()}")
    
    if sjl_path.exists():
        print(f"✅ sjl目录确实存在")
        
        # 列出sjl目录中的内容
        try:
            contents = list(sjl_path.iterdir())
            print(f"sjl目录内容 ({len(contents)}项):")
            for i, item in enumerate(contents[:10]):  # 只显示前10项
                item_type = "📁" if item.is_dir() else "📄"
                print(f"  {i+1}. {item_type} {item.name}")
            if len(contents) > 10:
                print(f"  ... 还有 {len(contents) - 10} 项")
        except Exception as e:
            print(f"⚠️ 无法读取sjl目录内容: {e}")
    else:
        print(f"❌ sjl目录不存在")
        
        # 列出桌面上的所有目录
        try:
            desktop_dirs = [item for item in desktop_path.iterdir() if item.is_dir()]
            print(f"\n桌面上的所有目录 ({len(desktop_dirs)}个):")
            for i, dir_item in enumerate(desktop_dirs):
                print(f"  {i+1}. 📁 {dir_item.name}")
                if "sjl" in dir_item.name.lower():
                    print(f"      ✅ 包含'sjl'的目录!")
        except Exception as e:
            print(f"⚠️ 无法读取桌面目录: {e}")
    
    return sjl_path.exists(), str(sjl_path)

def step2_test_everything_sdk(sjl_exists, sjl_path):
    """步骤2：测试Everything SDK"""
    print("\n" + "=" * 60)
    print("步骤2：测试Everything SDK直接搜索")
    print("=" * 60)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试不同的搜索关键词
        test_keywords = ["sjl", "folder:sjl", "type:folder sjl"]
        
        for keyword in test_keywords:
            print(f"\n🔍 搜索关键词: '{keyword}'")
            try:
                results = sdk.search(keyword, max_results=20)
                print(f"   结果数量: {len(results)}")
                
                sjl_found = False
                for i, result in enumerate(results):
                    print(f"     {i+1}. {result.filename}")
                    print(f"        路径: {result.full_path}")
                    print(f"        是否文件夹: {result.is_folder}")
                    
                    # 检查是否是我们要找的sjl目录
                    if sjl_exists and result.full_path.lower() == sjl_path.lower():
                        print(f"        ✅ 匹配目标sjl目录!")
                        sjl_found = True
                
                if sjl_exists and not sjl_found:
                    print(f"   ⚠️ 未找到目标sjl目录: {sjl_path}")
                
            except Exception as e:
                print(f"   ❌ 搜索失败: {e}")
        
        # 测试桌面路径限制搜索
        desktop_path = Path.home() / "Desktop"
        print(f"\n🔍 桌面路径限制搜索")
        
        desktop_queries = [
            f'"{desktop_path}\\" sjl',
            f'"{desktop_path}\\" folder:sjl',
            f'path:"{desktop_path}" sjl'
        ]
        
        for query in desktop_queries:
            print(f"   查询语句: {query}")
            try:
                results = sdk.search(query, max_results=10)
                print(f"   结果数量: {len(results)}")
                
                for i, result in enumerate(results):
                    print(f"     {i+1}. {result.filename} -> {result.full_path}")
                    
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        print("\n✅ Everything SDK测试完成")
        
    except Exception as e:
        print(f"❌ Everything SDK测试失败: {e}")
        import traceback
        traceback.print_exc()

def step3_check_search_query_building():
    """步骤3：检查搜索查询构建"""
    print("\n" + "=" * 60)
    print("步骤3：检查搜索查询构建")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        scan_dirs = engine.get_scan_directories()
        
        print(f"Profile 0扫描目录:")
        for i, dir_path in enumerate(scan_dirs):
            exists = "✅" if os.path.exists(dir_path) else "❌"
            print(f"  {i+1}. {exists} {dir_path}")
        
        # 测试查询构建
        test_query = "sjl"
        print(f"\n🔍 构建查询: '{test_query}'")
        
        # 测试包含文件夹的查询
        everything_query = engine._build_everything_query_with_directories(
            test_query, scan_dirs, file_types=None, include_folders=True
        )
        print(f"   包含文件夹的查询: {everything_query}")
        
        # 测试不包含文件夹的查询
        everything_query_no_folders = engine._build_everything_query_with_directories(
            test_query, scan_dirs, file_types=None, include_folders=False
        )
        print(f"   不包含文件夹的查询: {everything_query_no_folders}")
        
        # 测试只搜索文件夹的查询
        everything_query_folders_only = engine._build_everything_query_with_directories(
            test_query, scan_dirs, file_types=["folders"], include_folders=True
        )
        print(f"   只搜索文件夹的查询: {everything_query_folders_only}")
        
        print("\n✅ 搜索查询构建测试完成")
        
    except Exception as e:
        print(f"❌ 搜索查询构建测试失败: {e}")
        import traceback
        traceback.print_exc()

def step4_test_directory_filtering():
    """步骤4：测试目录过滤逻辑"""
    print("\n" + "=" * 60)
    print("步骤4：测试目录过滤逻辑")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        scan_dirs = engine.get_scan_directories()
        
        # 测试路径
        desktop_path = str(Path.home() / "Desktop")
        sjl_path = str(Path.home() / "Desktop" / "sjl")
        
        test_paths = [
            sjl_path,
            desktop_path,
            str(Path.home() / "Desktop" / "test.txt"),
            str(Path.home() / "Documents" / "test.doc"),
            "C:\\Windows\\System32\\test.exe"
        ]
        
        print(f"扫描目录: {scan_dirs}")
        print(f"\n测试路径过滤:")
        
        for test_path in test_paths:
            is_in_dirs = engine._is_in_scan_directories(test_path, scan_dirs)
            exists = "✅" if os.path.exists(test_path) else "❌"
            print(f"   {exists} {test_path}")
            print(f"      在扫描目录中: {is_in_dirs}")
        
        print("\n✅ 目录过滤逻辑测试完成")
        
    except Exception as e:
        print(f"❌ 目录过滤逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()

def step5_test_folder_search_settings():
    """步骤5：验证文件夹搜索设置"""
    print("\n" + "=" * 60)
    print("步骤5：验证文件夹搜索设置")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试不同的搜索参数组合
        test_cases = [
            {"query": "sjl", "include_folders": True, "file_types": None},
            {"query": "sjl", "include_folders": False, "file_types": None},
            {"query": "sjl", "include_folders": True, "file_types": ["folders"]},
            {"query": "sjl", "include_folders": True, "file_types": ["documents"]},
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n🔍 测试用例 {i+1}: {test_case}")
            
            try:
                results = engine.search(
                    query=test_case["query"],
                    limit=10,
                    file_types=test_case["file_types"],
                    include_folders=test_case["include_folders"]
                )
                
                print(f"   结果数量: {len(results)}")
                
                for j, result in enumerate(results):
                    item_icon = "📁" if result.item_type == "folder" else "📄"
                    print(f"     {j+1}. {item_icon} {result.filename} ({result.item_type})")
                    print(f"        路径: {result.filepath}")
                    print(f"        后缀: {result.suffix}")
                
            except Exception as e:
                print(f"   ❌ 测试用例失败: {e}")
        
        print("\n✅ 文件夹搜索设置测试完成")
        
    except Exception as e:
        print(f"❌ 文件夹搜索设置测试失败: {e}")
        import traceback
        traceback.print_exc()

def step6_comprehensive_diagnosis():
    """步骤6：综合诊断和解决方案"""
    print("\n" + "=" * 60)
    print("步骤6：综合诊断和解决方案")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        # 综合测试
        print("🔍 综合搜索测试:")
        
        # 1. Everything SDK直接搜索
        sdk = get_everything_sdk()
        direct_results = sdk.search("sjl", max_results=20)
        print(f"   Everything SDK直接搜索'sjl': {len(direct_results)} 个结果")
        
        desktop_sjl_found = False
        for result in direct_results:
            if "desktop" in result.full_path.lower() and "sjl" in result.filename.lower():
                desktop_sjl_found = True
                print(f"   ✅ 找到桌面sjl: {result.filename} -> {result.full_path}")
                break
        
        # 2. 搜索引擎测试
        engine = FileSearchEngine(profile_id=0)
        engine_results = engine.search("sjl", limit=10, include_folders=True)
        print(f"   搜索引擎搜索'sjl': {len(engine_results)} 个结果")
        
        for result in engine_results:
            print(f"     {result.filename} ({result.item_type}) -> {result.filepath}")
        
        # 3. 问题诊断
        print(f"\n💡 问题诊断:")
        
        if not desktop_sjl_found:
            print("1. ❌ Everything SDK未找到桌面sjl目录")
            print("   可能原因:")
            print("   - sjl目录不存在")
            print("   - Everything索引未包含该目录")
            print("   - 目录名称不匹配")
        else:
            print("1. ✅ Everything SDK能找到桌面sjl目录")
            
            if not engine_results:
                print("2. ❌ 搜索引擎无法找到目录")
                print("   可能原因:")
                print("   - 路径过滤逻辑问题")
                print("   - 查询构建问题")
                print("   - include_folders参数问题")
            else:
                print("2. ✅ 搜索引擎能找到目录")
        
        # 4. 解决方案
        print(f"\n🛠️ 解决方案:")
        
        if not desktop_sjl_found:
            print("- 检查sjl目录是否真实存在")
            print("- 重建Everything索引")
            print("- 确认目录名称拼写")
        elif not engine_results:
            print("- 检查Profile 0的扫描目录配置")
            print("- 验证路径过滤逻辑")
            print("- 确认include_folders=True")
            print("- 检查查询构建逻辑")
        else:
            print("- 搜索功能正常工作")
        
        print("\n✅ 综合诊断完成")
        
    except Exception as e:
        print(f"❌ 综合诊断失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 Simple Desktop - 子文件夹搜索问题调试")
    print("=" * 80)
    
    # 步骤1：验证目录存在性
    sjl_exists, sjl_path = step1_verify_directory_existence()
    
    # 步骤2：测试Everything SDK
    step2_test_everything_sdk(sjl_exists, sjl_path)
    
    # 步骤3：检查搜索查询构建
    step3_check_search_query_building()
    
    # 步骤4：测试目录过滤逻辑
    step4_test_directory_filtering()
    
    # 步骤5：验证文件夹搜索设置
    step5_test_folder_search_settings()
    
    # 步骤6：综合诊断
    step6_comprehensive_diagnosis()
    
    print("\n" + "=" * 80)
    print("🎯 调试完成！请查看上述输出以确定问题原因。")

if __name__ == "__main__":
    main()
