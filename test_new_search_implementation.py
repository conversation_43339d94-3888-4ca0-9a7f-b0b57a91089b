"""
测试新的搜索实现
验证简化的Everything查询和程序化过滤是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_query_extraction():
    """测试简单查询提取功能"""
    print("🧪 测试简单查询提取功能")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        test_cases = [
            # 原始查询 -> 期望的简化查询
            ("微信", "微信"),
            ("微信 ext:lnk", "微信"),
            ("微信 size:>10mb", "微信"),
            ("微信 dm:last7days", "微信"),
            ('"C:\\Users\\<USER>\\" 微信 ext:lnk', "微信"),
            ("folder: 微信", "微信"),
            ("微信 suffix:.txt", "微信"),
            ("test file ext:pdf size:>5mb dm:last30days", "test file"),
        ]
        
        for original, expected in test_cases:
            result = engine._extract_simple_query(original)
            status = "✅" if result == expected else "❌"
            print(f"  {status} '{original}' -> '{result}' (期望: '{expected}')")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_filter_pipeline():
    """测试过滤器管道功能"""
    print("\n🧪 测试过滤器管道功能")
    
    try:
        from simple_desktop.search.filters import FilterPipeline, FilterCriteria, FileTypeFilter
        from simple_desktop.search.models import SearchResult
        from datetime import datetime
        
        # 创建测试数据
        test_results = [
            SearchResult(
                filename="test.txt",
                filepath="C:\\Users\\<USER>\\test.txt",
                item_type="file",
                size=1024,
                suffix=".txt",
                date_modified="2023-12-01 10:30:45",
                date_modified_dt=datetime(2023, 12, 1, 10, 30, 45),
                size_bytes=1024
            ),
            SearchResult(
                filename="image.jpg",
                filepath="C:\\Users\\<USER>\\image.jpg",
                item_type="file",
                size=2048000,
                suffix=".jpg",
                date_modified="2023-12-15 14:20:30",
                date_modified_dt=datetime(2023, 12, 15, 14, 20, 30),
                size_bytes=2048000
            ),
            SearchResult(
                filename="folder",
                filepath="C:\\Users\\<USER>\\folder",
                item_type="folder",
                size=0,
                suffix="",
                date_modified=None,
                date_modified_dt=None,
                size_bytes=0
            ),
        ]
        
        # 测试文件类型过滤
        print("  测试文件类型过滤:")
        criteria = FilterCriteria(file_types=["documents"], include_folders=False)
        pipeline = FilterPipeline.from_criteria(criteria)
        filtered = pipeline.apply_filters(test_results)
        
        print(f"    原始结果数: {len(test_results)}")
        print(f"    过滤后结果数: {len(filtered)}")
        print(f"    过滤后文件: {[r.filename for r in filtered]}")
        
        # 测试大小过滤
        print("  测试大小过滤:")
        criteria = FilterCriteria(min_size_mb=1, include_folders=False)
        pipeline = FilterPipeline.from_criteria(criteria)
        filtered = pipeline.apply_filters(test_results)
        
        print(f"    大于1MB的文件: {[r.filename for r in filtered]}")
        
        print("✅ 过滤器管道测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_new_search_methods():
    """测试新的搜索方法"""
    print("\n🧪 测试新的搜索方法")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试基本搜索
        print("  测试基本搜索:")
        results = engine.search("微信", limit=5)
        print(f"    搜索'微信'结果数: {len(results)}")
        for i, result in enumerate(results[:3]):
            print(f"      {i+1}. {result.filename} ({result.item_type})")
        
        # 测试后缀搜索
        print("  测试后缀搜索:")
        results = engine.search_with_suffix("微信", ".lnk", limit=5)
        print(f"    搜索'微信 .lnk'结果数: {len(results)}")
        for i, result in enumerate(results[:3]):
            print(f"      {i+1}. {result.filename} ({result.suffix})")
        
        # 测试高级搜索
        print("  测试高级搜索:")
        results = engine.search_advanced(
            query="微信",
            file_types=["executables"],
            include_folders=False,
            limit=5
        )
        print(f"    高级搜索结果数: {len(results)}")
        for i, result in enumerate(results[:3]):
            print(f"      {i+1}. {result.filename} ({result.suffix})")
        
        print("✅ 新搜索方法测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_performance_comparison():
    """测试性能对比"""
    print("\n🧪 测试性能对比")
    
    try:
        import time
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试新方法
        start_time = time.time()
        new_results = engine.search("微信", limit=20, file_types=["executables"])
        new_time = time.time() - start_time
        
        # 测试旧方法
        start_time = time.time()
        old_results = engine.search_legacy("微信", limit=20, file_types=["executables"])
        old_time = time.time() - start_time
        
        print(f"  新方法: {len(new_results)} 结果, 耗时: {new_time:.3f}s")
        print(f"  旧方法: {len(old_results)} 结果, 耗时: {old_time:.3f}s")
        
        # 比较结果质量
        new_files = {r.filepath for r in new_results}
        old_files = {r.filepath for r in old_results}
        
        common_files = new_files & old_files
        print(f"  共同结果: {len(common_files)}")
        print(f"  新方法独有: {len(new_files - old_files)}")
        print(f"  旧方法独有: {len(old_files - new_files)}")
        
        print("✅ 性能对比测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("🚀 开始测试新的搜索实现")
    print("=" * 50)
    
    test_simple_query_extraction()
    test_filter_pipeline()
    test_new_search_methods()
    test_performance_comparison()
    
    print("\n" + "=" * 50)
    print("✅ 所有测试完成")


if __name__ == "__main__":
    main()
