#!/usr/bin/env python3
"""
测试完整的Simple Desktop应用程序，包括热键功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🚀 Simple Desktop - 完整应用程序测试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication, QSystemTrayIcon, QMenu
        from PySide6.QtCore import QTimer
        from PySide6.QtGui import QIcon, QPixmap
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.core.hotkey import hotkey_manager
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(False)  # 不要在最后一个窗口关闭时退出
        
        print("✅ Qt应用程序创建成功")
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        print("✅ 搜索窗口创建成功")
        
        # 设置热键回调
        def hotkey_callback():
            print("🎯 热键回调被触发")
            search_window.toggle_window()
        
        hotkey_manager.set_callback(hotkey_callback)
        
        # 启动热键监听
        hotkey_success = hotkey_manager.start_listening()
        
        if hotkey_success:
            print("✅ 热键监听启动成功")
        else:
            print("❌ 热键监听启动失败")
        
        # 创建系统托盘图标（如果支持）
        if QSystemTrayIcon.isSystemTrayAvailable():
            # 创建一个简单的图标
            pixmap = QPixmap(16, 16)
            pixmap.fill()
            icon = QIcon(pixmap)
            
            tray_icon = QSystemTrayIcon(icon, app)
            
            # 创建托盘菜单
            tray_menu = QMenu()
            
            show_action = tray_menu.addAction("显示搜索窗口")
            show_action.triggered.connect(search_window.show_window)
            
            hide_action = tray_menu.addAction("隐藏搜索窗口")
            hide_action.triggered.connect(search_window.hide_window)
            
            tray_menu.addSeparator()
            
            quit_action = tray_menu.addAction("退出")
            quit_action.triggered.connect(app.quit)
            
            tray_icon.setContextMenu(tray_menu)
            tray_icon.show()
            
            print("✅ 系统托盘图标创建成功")
        else:
            print("⚠️ 系统托盘不可用")
        
        # 显示初始窗口
        search_window.show_window()
        
        print("\n📝 应用程序测试说明:")
        print("1. Simple Desktop应用程序已启动")
        print("2. 搜索窗口已显示")
        print("3. 请尝试双击Ctrl键切换窗口显示/隐藏")
        print("4. 可以在搜索框中输入内容进行搜索测试")
        print("5. 右键点击系统托盘图标查看菜单")
        print("6. 按Ctrl+C或关闭窗口退出应用程序")
        
        # 设置定时器来检查热键状态
        def check_hotkey_status():
            if hotkey_manager.is_listening:
                print("🔄 热键监听状态: 正常")
            else:
                print("⚠️ 热键监听状态: 异常")
        
        status_timer = QTimer()
        status_timer.timeout.connect(check_hotkey_status)
        status_timer.start(5000)  # 每5秒检查一次
        
        # 运行应用程序
        print("\n🚀 应用程序开始运行...")
        
        try:
            exit_code = app.exec()
            print(f"\n📊 应用程序退出，退出代码: {exit_code}")
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断应用程序")
        
        # 清理
        if hotkey_success:
            hotkey_manager.stop_listening()
            print("✅ 热键监听已停止")
        
        print("✅ 应用程序清理完成")
        
    except Exception as e:
        print(f"❌ 应用程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
