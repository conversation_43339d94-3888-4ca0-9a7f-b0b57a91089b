#!/usr/bin/env python3
"""
简单的热键测试工具
"""

import sys
import time
import threading
import ctypes
import ctypes.wintypes as wintypes

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_0 = 0x30

def simple_hotkey_test():
    """简单的热键测试"""
    print("🧪 简单热键测试")
    print("=" * 40)
    
    user32 = ctypes.windll.user32
    kernel32 = ctypes.windll.kernel32
    
    # 注册一个测试热键 Ctrl+1
    test_id = 8888
    vk_code = VK_0 + 1  # Ctrl+1
    
    print("1. 注册测试热键 Ctrl+1...")
    result = user32.RegisterHotKey(None, test_id, MOD_CONTROL, vk_code)
    
    if not result:
        error_code = ctypes.GetLastError()
        print(f"   ❌ 热键注册失败 (错误: {error_code})")
        return False
    
    print("   ✅ 热键注册成功")
    
    # 创建消息循环
    print("2. 启动消息循环...")
    
    msg = wintypes.MSG()
    hotkey_count = 0
    start_time = time.time()
    
    print("3. 请按 Ctrl+1 测试热键...")
    print("   测试将在10秒后自动结束")
    
    while time.time() - start_time < 10:
        try:
            # 使用PeekMessage非阻塞检查
            result = user32.PeekMessageW(ctypes.byref(msg), None, 0, 0, 1)  # PM_REMOVE
            
            if result:
                if msg.message == WM_HOTKEY and msg.wParam == test_id:
                    hotkey_count += 1
                    current_time = time.strftime('%H:%M:%S', time.localtime())
                    print(f"   🎯 热键触发 #{hotkey_count} - 时间: {current_time}")
                
                # 分发消息
                user32.TranslateMessage(ctypes.byref(msg))
                user32.DispatchMessageW(ctypes.byref(msg))
            else:
                # 没有消息，短暂休眠
                time.sleep(0.01)
                
        except Exception as e:
            print(f"   ❌ 消息循环错误: {e}")
            break
    
    # 注销热键
    user32.UnregisterHotKey(None, test_id)
    
    print(f"\n📊 测试结果:")
    print(f"   热键触发次数: {hotkey_count}")
    
    if hotkey_count > 0:
        print("   ✅ 热键系统工作正常")
        return True
    else:
        print("   ❌ 热键系统可能有问题")
        return False

def blocking_hotkey_test():
    """阻塞式热键测试"""
    print("\n🧪 阻塞式热键测试")
    print("=" * 40)
    
    user32 = ctypes.windll.user32
    
    # 注册测试热键 Ctrl+2
    test_id = 9999
    vk_code = VK_0 + 2  # Ctrl+2
    
    print("1. 注册测试热键 Ctrl+2...")
    result = user32.RegisterHotKey(None, test_id, MOD_CONTROL, vk_code)
    
    if not result:
        error_code = ctypes.GetLastError()
        print(f"   ❌ 热键注册失败 (错误: {error_code})")
        return False
    
    print("   ✅ 热键注册成功")
    
    # 使用阻塞式消息循环
    print("2. 启动阻塞式消息循环...")
    print("3. 请按 Ctrl+2 测试热键...")
    print("   按 Ctrl+C 结束测试")
    
    msg = wintypes.MSG()
    hotkey_count = 0
    
    try:
        while True:
            # 使用GetMessage阻塞等待
            result = user32.GetMessageW(ctypes.byref(msg), None, 0, 0)
            
            if result == -1:  # 错误
                print("   ❌ GetMessage 错误")
                break
            elif result == 0:  # WM_QUIT
                print("   🔚 收到退出消息")
                break
            
            if msg.message == WM_HOTKEY and msg.wParam == test_id:
                hotkey_count += 1
                current_time = time.strftime('%H:%M:%S', time.localtime())
                print(f"   🎯 阻塞式热键触发 #{hotkey_count} - 时间: {current_time}")
            
            # 分发消息
            user32.TranslateMessage(ctypes.byref(msg))
            user32.DispatchMessageW(ctypes.byref(msg))
            
    except KeyboardInterrupt:
        print("\n   ⏹️ 用户中断测试")
    except Exception as e:
        print(f"   ❌ 阻塞式消息循环错误: {e}")
    
    # 注销热键
    user32.UnregisterHotKey(None, test_id)
    
    print(f"\n📊 阻塞式测试结果:")
    print(f"   热键触发次数: {hotkey_count}")
    
    if hotkey_count > 0:
        print("   ✅ 阻塞式热键系统工作正常")
        return True
    else:
        print("   ❌ 阻塞式热键系统可能有问题")
        return False

def main():
    """主函数"""
    print("🛠️ 简单热键测试工具")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("简单热键测试", simple_hotkey_test),
        ("阻塞式热键测试", blocking_hotkey_test),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 热键测试完成！")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests > 0:
        print("\n💡 测试结论:")
        print("- 如果简单测试通过，说明热键系统基本正常")
        print("- 如果阻塞式测试也通过，说明GetMessage工作正常")
        print("- Simple Desktop的热键问题可能在于其他方面")
    else:
        print("\n⚠️ 所有测试都失败，可能的问题:")
        print("- Windows热键系统权限问题")
        print("- 需要以管理员身份运行")
        print("- 热键被其他应用程序占用")

if __name__ == "__main__":
    main()
