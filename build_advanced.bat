@echo off
chcp 65001 >nul
echo ================================================
echo Simple Desktop 高级打包程序
echo ================================================
echo.
echo 请选择构建类型:
echo 1. 单文件版本 (推荐)
echo 2. 目录版本
echo 3. 使用spec文件构建
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo 构建单文件版本...
    python build_advanced.py --type onefile
) else if "%choice%"=="2" (
    echo 构建目录版本...
    python build_advanced.py --type onedir
) else if "%choice%"=="3" (
    echo 使用spec文件构建...
    python build_advanced.py --type spec
) else (
    echo 无效选择，使用默认单文件版本...
    python build_advanced.py --type onefile
)

echo.
echo 构建完成！按任意键退出...
pause >nul
