#!/usr/bin/env python3
"""
调试快捷方式搜索回归问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def step1_verify_regression():
    """步骤1：验证回归问题"""
    print("=" * 60)
    print("步骤1：验证快捷方式搜索回归问题")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试微信搜索
        print("🔍 测试微信搜索:")
        
        # 1. 中文搜索
        print("1. 搜索'微信':")
        results_cn = engine.search("微信", limit=10)
        print(f"   结果数量: {len(results_cn)}")
        
        lnk_count = 0
        for i, result in enumerate(results_cn):
            icon = "🔗" if result.suffix.lower() == ".lnk" else "📁" if result.item_type == "folder" else "📄"
            print(f"     {i+1}. {icon} {result.filename} ({result.suffix})")
            print(f"        路径: {result.filepath}")
            
            if result.suffix.lower() == ".lnk":
                lnk_count += 1
        
        print(f"   找到的快捷方式数量: {lnk_count}")
        
        # 2. 英文搜索
        print("\n2. 搜索'weixin':")
        results_en = engine.search("weixin", limit=10)
        print(f"   结果数量: {len(results_en)}")
        
        lnk_count_en = 0
        for i, result in enumerate(results_en):
            icon = "🔗" if result.suffix.lower() == ".lnk" else "📁" if result.item_type == "folder" else "📄"
            print(f"     {i+1}. {icon} {result.filename} ({result.suffix})")
            print(f"        路径: {result.filepath}")
            
            if result.suffix.lower() == ".lnk":
                lnk_count_en += 1
        
        print(f"   找到的快捷方式数量: {lnk_count_en}")
        
        # 3. 只搜索可执行文件
        print("\n3. 搜索'微信'（只搜索可执行文件）:")
        results_exe = engine.search("微信", limit=10, file_types=["executables"])
        print(f"   结果数量: {len(results_exe)}")
        
        lnk_count_exe = 0
        for i, result in enumerate(results_exe):
            icon = "🔗" if result.suffix.lower() == ".lnk" else "⚙️"
            print(f"     {i+1}. {icon} {result.filename} ({result.suffix})")
            print(f"        路径: {result.filepath}")
            
            if result.suffix.lower() == ".lnk":
                lnk_count_exe += 1
        
        print(f"   找到的快捷方式数量: {lnk_count_exe}")
        
        # 总结
        if lnk_count == 0 and lnk_count_en == 0 and lnk_count_exe == 0:
            print("\n❌ 确认回归问题：无法找到任何微信快捷方式")
            return True
        else:
            print("\n✅ 快捷方式搜索功能正常")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return True

def step2_check_query_building():
    """步骤2：检查查询构建逻辑"""
    print("\n" + "=" * 60)
    print("步骤2：检查查询构建逻辑")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        scan_dirs = engine.get_scan_directories()
        
        print(f"扫描目录: {scan_dirs}")
        
        # 测试不同的查询构建
        test_cases = [
            {"query": "微信", "file_types": None, "include_folders": True},
            {"query": "微信", "file_types": ["executables"], "include_folders": True},
            {"query": "微信", "file_types": ["documents"], "include_folders": True},
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n🔍 测试用例 {i+1}: {test_case}")
            
            # 构建查询
            everything_query = engine._build_everything_query_with_directories(
                test_case["query"], 
                scan_dirs, 
                test_case["file_types"], 
                test_case["include_folders"]
            )
            print(f"   构建的查询: {everything_query}")
            
            # 分析查询
            if ".lnk" in everything_query or "executables" in everything_query:
                print(f"   ✅ 查询包含.lnk或executables关键词")
            else:
                print(f"   ⚠️ 查询不包含.lnk或executables关键词")
        
        # 检查文件类型定义
        file_types = engine.file_type_extensions
        print("\n文件类型定义:")
        for category, extensions in file_types.items():
            if ".lnk" in extensions:
                print(f"   ✅ {category}: {extensions}")
            else:
                print(f"   {category}: {extensions}")
        
        print("\n✅ 查询构建检查完成")
        
    except Exception as e:
        print(f"❌ 查询构建检查失败: {e}")
        import traceback
        traceback.print_exc()

def step3_test_everything_sdk():
    """步骤3：测试Everything SDK"""
    print("\n" + "=" * 60)
    print("步骤3：测试Everything SDK直接搜索")
    print("=" * 60)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试不同的搜索关键词
        test_keywords = ["微信", "weixin", "ext:lnk 微信", "folder:微信"]
        
        for keyword in test_keywords:
            print(f"\n🔍 搜索关键词: '{keyword}'")
            try:
                results = sdk.search(keyword, max_results=10)
                print(f"   结果数量: {len(results)}")
                
                lnk_count = 0
                for i, result in enumerate(results):
                    icon = "🔗" if result.filename.lower().endswith(".lnk") else "📁" if result.is_folder else "📄"
                    print(f"     {i+1}. {icon} {result.filename}")
                    print(f"        路径: {result.full_path}")
                    
                    if result.filename.lower().endswith(".lnk"):
                        lnk_count += 1
                
                print(f"   找到的快捷方式数量: {lnk_count}")
                
            except Exception as e:
                print(f"   ❌ 搜索失败: {e}")
        
        # 测试特定目录搜索
        print("\n🔍 特定目录搜索:")
        
        # 公共桌面
        public_desktop = "C:\\Users\\<USER>\\Desktop"
        print(f"1. 搜索公共桌面中的微信:")
        public_query = f'"{public_desktop}\\" 微信'
        
        try:
            results = sdk.search(public_query, max_results=5)
            print(f"   结果数量: {len(results)}")
            
            for i, result in enumerate(results):
                print(f"     {i+1}. {result.filename} -> {result.full_path}")
                
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
        
        # 开始菜单
        start_menu = "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs"
        print(f"\n2. 搜索开始菜单中的微信:")
        start_query = f'"{start_menu}\\" 微信'
        
        try:
            results = sdk.search(start_query, max_results=5)
            print(f"   结果数量: {len(results)}")
            
            for i, result in enumerate(results):
                print(f"     {i+1}. {result.filename} -> {result.full_path}")
                
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
        
        print("\n✅ Everything SDK测试完成")
        
    except Exception as e:
        print(f"❌ Everything SDK测试失败: {e}")
        import traceback
        traceback.print_exc()

def step4_check_file_type_filtering():
    """步骤4：检查文件类型过滤"""
    print("\n" + "=" * 60)
    print("步骤4：检查文件类型过滤")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 检查_should_include_result方法的行为
        print("模拟_should_include_result方法行为:")
        
        # 创建测试结果
        class TestResult:
            def __init__(self, filename, suffix, item_type):
                self.filename = filename
                self.suffix = suffix
                self.item_type = item_type
        
        test_cases = [
            {"result": TestResult("微信.lnk", ".lnk", "file"), "file_types": None, "include_folders": True},
            {"result": TestResult("微信.lnk", ".lnk", "file"), "file_types": ["executables"], "include_folders": True},
            {"result": TestResult("微信.lnk", ".lnk", "file"), "file_types": ["documents"], "include_folders": True},
            {"result": TestResult("文件夹", "", "folder"), "file_types": None, "include_folders": True},
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n测试用例 {i+1}: {test_case['result'].filename} ({test_case['result'].item_type})")
            print(f"   file_types: {test_case['file_types']}")
            print(f"   include_folders: {test_case['include_folders']}")
            
            should_include = engine._should_include_result(
                test_case["result"],
                test_case["file_types"],
                test_case["include_folders"]
            )
            
            print(f"   结果: {'✅ 包含' if should_include else '❌ 不包含'}")
        
        # 测试搜索引擎的文件类型过滤
        print("\n搜索引擎文件类型过滤测试:")
        
        # 1. 无过滤搜索
        print("1. 无文件类型过滤:")
        results1 = engine.search("微信", limit=5, file_types=None)
        print(f"   结果数量: {len(results1)}")
        
        # 2. 只搜索可执行文件
        print("2. 只搜索可执行文件:")
        results2 = engine.search("微信", limit=5, file_types=["executables"])
        print(f"   结果数量: {len(results2)}")
        
        # 3. 搜索所有类型
        print("3. 搜索所有文件类型:")
        all_types = list(engine.file_type_extensions.keys()) + ["folders"]
        results3 = engine.search("微信", limit=5, file_types=all_types)
        print(f"   结果数量: {len(results3)}")
        
        print("\n✅ 文件类型过滤检查完成")
        
    except Exception as e:
        print(f"❌ 文件类型过滤检查失败: {e}")
        import traceback
        traceback.print_exc()

def step5_check_path_filtering():
    """步骤5：检查路径过滤逻辑"""
    print("\n" + "=" * 60)
    print("步骤5：检查路径过滤逻辑")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        scan_dirs = engine.get_scan_directories()
        
        # 测试路径
        test_paths = [
            "C:\\Users\\<USER>\\Desktop\\微信.lnk",
            "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\微信\\微信.lnk",
            "C:\\Program Files\\Tencent\\WeChat\\WeChat.exe",
            "C:\\Users\\<USER>\\Desktop\\微信.txt",
        ]
        
        print(f"扫描目录: {scan_dirs}")
        print(f"\n测试路径过滤:")
        
        for test_path in test_paths:
            is_in_dirs = engine._is_in_scan_directories(test_path, scan_dirs)
            print(f"   {test_path}")
            print(f"      在扫描目录中: {'✅ 是' if is_in_dirs else '❌ 否'}")
        
        print("\n✅ 路径过滤逻辑测试完成")
        
    except Exception as e:
        print(f"❌ 路径过滤逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()

def step6_comprehensive_diagnosis():
    """步骤6：综合诊断和解决方案"""
    print("\n" + "=" * 60)
    print("步骤6：综合诊断和解决方案")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        # 检查源代码
        print("🔍 检查源代码:")
        
        # 1. 检查查询构建方法
        import inspect
        from simple_desktop.search.engine import FileSearchEngine
        
        build_query_code = inspect.getsource(FileSearchEngine._build_everything_query_with_directories)
        print("1. _build_everything_query_with_directories方法:")
        
        # 检查关键问题
        if "executables" in build_query_code:
            print("   ✅ 方法中包含executables关键词")
        else:
            print("   ❌ 方法中不包含executables关键词")
        
        if ".lnk" in build_query_code:
            print("   ✅ 方法中包含.lnk关键词")
        else:
            print("   ❌ 方法中不包含.lnk关键词")
        
        # 2. 检查文件类型过滤方法
        filter_code = inspect.getsource(FileSearchEngine._should_include_result)
        print("\n2. _should_include_result方法:")
        
        if "result_ext" in filter_code and "result.suffix.lower()" in filter_code:
            print("   ✅ 方法正确处理文件后缀")
        else:
            print("   ❌ 方法可能不正确处理文件后缀")
        
        # 3. 问题诊断
        print(f"\n💡 问题诊断:")
        
        # 检查Everything SDK是否能找到微信快捷方式
        sdk = get_everything_sdk()
        sdk_results = sdk.search("ext:lnk 微信", max_results=5)
        
        if sdk_results:
            print("1. ✅ Everything SDK能找到微信快捷方式")
            
            # 检查搜索引擎是否能找到
            engine = FileSearchEngine(profile_id=0)
            engine_results = engine.search("微信", limit=5, file_types=["executables"])
            
            if not engine_results:
                print("2. ❌ 搜索引擎无法找到微信快捷方式")
                print("   可能原因:")
                print("   - 查询构建逻辑问题")
                print("   - 文件类型过滤逻辑问题")
                print("   - 路径过滤逻辑问题")
            else:
                print("2. ✅ 搜索引擎能找到微信快捷方式")
        else:
            print("1. ❌ Everything SDK无法找到微信快捷方式")
            print("   可能原因:")
            print("   - 微信快捷方式不存在")
            print("   - Everything索引问题")
        
        # 4. 解决方案
        print(f"\n🛠️ 解决方案:")
        print("1. 修复查询构建逻辑，确保正确处理.lnk文件")
        print("2. 确保文件类型过滤逻辑正确包含.lnk文件")
        print("3. 验证路径过滤逻辑正确识别快捷方式路径")
        print("4. 检查Everything索引是否包含快捷方式文件")
        
        print("\n✅ 综合诊断完成")
        
    except Exception as e:
        print(f"❌ 综合诊断失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 Simple Desktop - 快捷方式搜索回归问题调试")
    print("=" * 80)
    
    # 步骤1：验证回归问题
    regression_confirmed = step1_verify_regression()
    
    if regression_confirmed:
        # 步骤2：检查查询构建逻辑
        step2_check_query_building()
        
        # 步骤3：测试Everything SDK
        step3_test_everything_sdk()
        
        # 步骤4：检查文件类型过滤
        step4_check_file_type_filtering()
        
        # 步骤5：检查路径过滤逻辑
        step5_check_path_filtering()
        
        # 步骤6：综合诊断
        step6_comprehensive_diagnosis()
    
    print("\n" + "=" * 80)
    print("🎯 调试完成！请查看上述输出以确定问题原因。")

if __name__ == "__main__":
    main()
