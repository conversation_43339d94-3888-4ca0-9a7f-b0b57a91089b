#!/usr/bin/env python3
"""
SimpleDesktop 右键菜单安装向导
"""

import sys
import os
import ctypes
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def is_admin():
    """检查是否具有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except Exception:
        return False


def show_message(title, message, icon_type=0):
    """显示Windows消息框"""
    try:
        ctypes.windll.user32.MessageBoxW(0, message, title, icon_type)
    except Exception:
        print(f"{title}: {message}")


def run_as_admin():
    """以管理员权限重新运行脚本"""
    try:
        # 以管理员权限重新运行
        result = ctypes.windll.shell32.ShellExecuteW(
            None, 
            "runas", 
            sys.executable, 
            f'"{__file__}"', 
            None, 
            1
        )
        return result > 32
    except Exception as e:
        print(f"Failed to run as admin: {e}")
        return False


def install_context_menu():
    """安装右键菜单"""
    try:
        from simple_desktop.core.context_menu import context_menu_manager
        
        print("正在安装右键菜单...")
        
        # 检查是否已安装
        if context_menu_manager.is_context_menu_registered():
            print("右键菜单已经安装")
            show_message(
                "SimpleDesktop", 
                "右键菜单已经安装！\n\n您可以在Windows资源管理器中右键点击文件夹，\n选择'添加到SimpleDesktop'来使用此功能。",
                0x40  # MB_ICONINFORMATION
            )
            return True
        
        # 安装右键菜单
        success = context_menu_manager.register_context_menu()
        
        if success:
            print("✓ 右键菜单安装成功")
            show_message(
                "SimpleDesktop", 
                "右键菜单安装成功！\n\n现在您可以在Windows资源管理器中右键点击文件夹，\n选择'添加到SimpleDesktop'来将文件夹添加到Profile中。\n\n注意：只有在SimpleDesktop应用程序运行时，\n右键菜单才会显示。",
                0x40  # MB_ICONINFORMATION
            )
            return True
        else:
            print("✗ 右键菜单安装失败")
            show_message(
                "SimpleDesktop - 错误", 
                "右键菜单安装失败！\n\n可能的原因：\n- 权限不足\n- 注册表访问被阻止\n- 系统限制\n\n请尝试以管理员身份运行此程序。",
                0x10  # MB_ICONERROR
            )
            return False
            
    except Exception as e:
        print(f"安装失败: {e}")
        show_message(
            "SimpleDesktop - 错误", 
            f"安装过程中发生错误：\n{e}\n\n请检查系统设置或联系技术支持。",
            0x10  # MB_ICONERROR
        )
        return False


def uninstall_context_menu():
    """卸载右键菜单"""
    try:
        from simple_desktop.core.context_menu import context_menu_manager
        
        print("正在卸载右键菜单...")
        
        # 检查是否已安装
        if not context_menu_manager.is_context_menu_registered():
            print("右键菜单未安装")
            show_message(
                "SimpleDesktop", 
                "右键菜单未安装，无需卸载。",
                0x40  # MB_ICONINFORMATION
            )
            return True
        
        # 卸载右键菜单
        success = context_menu_manager.unregister_context_menu()
        
        if success:
            print("✓ 右键菜单卸载成功")
            show_message(
                "SimpleDesktop", 
                "右键菜单卸载成功！\n\n右键菜单项已从Windows资源管理器中移除。",
                0x40  # MB_ICONINFORMATION
            )
            return True
        else:
            print("✗ 右键菜单卸载失败")
            show_message(
                "SimpleDesktop - 错误", 
                "右键菜单卸载失败！\n\n可能的原因：\n- 权限不足\n- 注册表访问被阻止\n\n请尝试以管理员身份运行此程序。",
                0x10  # MB_ICONERROR
            )
            return False
            
    except Exception as e:
        print(f"卸载失败: {e}")
        show_message(
            "SimpleDesktop - 错误", 
            f"卸载过程中发生错误：\n{e}",
            0x10  # MB_ICONERROR
        )
        return False


def show_help():
    """显示帮助信息"""
    help_text = """SimpleDesktop 右键菜单安装向导

用法:
    python install_context_menu.py [选项]

选项:
    install     - 安装右键菜单 (默认)
    uninstall   - 卸载右键菜单
    help        - 显示此帮助信息

功能说明:
    安装后，您可以在Windows资源管理器中右键点击任意文件夹，
    选择"添加到SimpleDesktop"来将文件夹添加到指定的Profile中。

注意事项:
    - 需要管理员权限来修改Windows注册表
    - 只有在SimpleDesktop应用程序运行时，右键菜单才会显示
    - 卸载时会完全移除右键菜单项

技术支持:
    如果遇到问题，请检查：
    1. 是否以管理员身份运行
    2. 防病毒软件是否阻止了注册表修改
    3. Windows用户账户控制(UAC)设置
"""
    
    print(help_text)
    show_message("SimpleDesktop - 帮助", help_text, 0x40)


def main():
    """主函数"""
    print("SimpleDesktop 右键菜单安装向导")
    print("=" * 50)
    
    # 解析命令行参数
    action = "install"  # 默认操作
    if len(sys.argv) > 1:
        action = sys.argv[1].lower()
    
    if action == "help":
        show_help()
        return
    
    # 检查管理员权限
    if not is_admin():
        print("需要管理员权限来修改Windows注册表")
        
        result = ctypes.windll.user32.MessageBoxW(
            0,
            "安装右键菜单需要管理员权限来修改Windows注册表。\n\n点击'是'以管理员身份重新运行程序。",
            "SimpleDesktop - 需要管理员权限",
            0x24  # MB_YESNO | MB_ICONQUESTION
        )
        
        if result == 6:  # IDYES
            print("正在请求管理员权限...")
            if run_as_admin():
                sys.exit(0)  # 成功启动管理员进程
            else:
                show_message(
                    "SimpleDesktop - 错误",
                    "无法获取管理员权限。\n请手动以管理员身份运行此程序。",
                    0x10  # MB_ICONERROR
                )
                sys.exit(1)
        else:
            print("用户取消了权限请求")
            sys.exit(1)
    
    print("✓ 已获得管理员权限")
    
    # 执行操作
    if action == "install":
        success = install_context_menu()
    elif action == "uninstall":
        success = uninstall_context_menu()
    else:
        print(f"未知操作: {action}")
        print("使用 'help' 参数查看帮助信息")
        success = False
    
    if success:
        print("\n操作完成！")
    else:
        print("\n操作失败！")
    
    # 在控制台模式下等待用户输入
    if len(sys.argv) <= 1:
        input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
