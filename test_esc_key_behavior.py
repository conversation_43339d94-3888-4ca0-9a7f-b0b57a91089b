#!/usr/bin/env python3
"""
测试ESC键的新行为：退出应用程序
"""

import sys
import time
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_esc_key_behavior():
    """测试ESC键行为"""
    print("🧪 测试ESC键新行为：退出应用程序")
    print("=" * 60)
    
    try:
        # 启动main.py应用程序
        print("1. 启动Simple Desktop应用程序...")
        
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print(f"   ✅ 应用程序已启动 (PID: {process.pid})")
        
        # 等待应用程序启动
        print("2. 等待应用程序初始化...")
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("   ✅ 应用程序正在运行")
        else:
            print("   ❌ 应用程序启动失败")
            stdout, stderr = process.communicate()
            print(f"   标准输出: {stdout}")
            print(f"   错误输出: {stderr}")
            return False
        
        print("\n📝 ESC键测试说明:")
        print("1. Simple Desktop应用程序已启动")
        print("2. 搜索窗口应该已经显示")
        print("3. 请按ESC键测试退出功能")
        print("4. 预期行为：按ESC键应该完全退出应用程序")
        print("5. 测试将监控应用程序状态15秒")
        
        # 监控应用程序状态
        start_time = time.time()
        app_exited = False
        
        while time.time() - start_time < 15:
            if process.poll() is not None:
                print(f"\n🎉 应用程序已退出 (运行时间: {time.time() - start_time:.1f}秒)")
                app_exited = True
                break
            time.sleep(0.5)
        
        if not app_exited:
            print("\n⏰ 15秒监控时间结束，应用程序仍在运行")
            print("   如果您按了ESC键但应用程序未退出，说明功能可能有问题")
            
            # 手动终止应用程序
            print("   正在手动终止应用程序...")
            process.terminate()
            
            try:
                process.wait(timeout=5)
                print("   ✅ 应用程序已终止")
            except subprocess.TimeoutExpired:
                print("   ⚠️ 强制终止应用程序")
                process.kill()
                process.wait()
        
        # 获取应用程序输出
        try:
            stdout, stderr = process.communicate(timeout=2)
            
            print("\n📊 应用程序输出分析:")
            if stdout:
                print("标准输出:")
                lines = stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        print(f"  {line}")
                        
                # 检查是否有ESC键检测的日志
                if "[HOTKEY] ESC 检测到，退出应用程序" in stdout:
                    print("\n✅ ESC键退出功能正常工作")
                    print("   检测到ESC键退出日志")
                elif "正在退出应用程序..." in stdout:
                    print("\n✅ 应用程序正常退出")
                    print("   检测到正常退出流程")
                else:
                    print("\n⚠️ 未检测到ESC键退出日志")
            
            if stderr:
                print("错误输出:")
                for line in stderr.strip().split('\n'):
                    if line.strip():
                        print(f"  {line}")
        except subprocess.TimeoutExpired:
            print("   ⚠️ 获取输出超时")
        
        return app_exited
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_keypress_simulation():
    """测试键盘事件模拟"""
    print("\n🧪 测试键盘事件模拟")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QKeyEvent
        from simple_desktop.ui.search_window import FloatingSearchWindow
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建模拟的app_manager
        class MockAppManager:
            def __init__(self):
                self.quit_called = False
            
            def quit_app(self):
                self.quit_called = True
                print("[MOCK] quit_app() 被调用")
        
        mock_manager = MockAppManager()
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow(app_manager=mock_manager)
        
        print("✅ 测试环境创建成功")
        
        # 模拟ESC键按下事件
        print("1. 模拟ESC键按下...")
        
        esc_event = QKeyEvent(
            QKeyEvent.Type.KeyPress,
            Qt.Key.Key_Escape,
            Qt.KeyboardModifier.NoModifier
        )
        
        # 发送ESC键事件到窗口
        search_window.keyPressEvent(esc_event)
        
        # 处理Qt事件
        app.processEvents()
        
        # 检查结果
        if mock_manager.quit_called:
            print("✅ ESC键事件处理正确")
            print("   quit_app() 方法被正确调用")
            return True
        else:
            print("❌ ESC键事件处理失败")
            print("   quit_app() 方法未被调用")
            return False
        
    except Exception as e:
        print(f"❌ 键盘事件模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - ESC键行为测试")
    print("=" * 80)
    
    # 执行测试
    tests = [
        ("键盘事件模拟测试", test_keypress_simulation),
        ("ESC键行为测试", test_esc_key_behavior),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 ESC键行为测试完成！")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试都通过！")
        print("\n💡 ESC键新行为:")
        print("- 按ESC键：完全退出Simple Desktop应用程序")
        print("- 不再需要Shift+ESC组合键")
        print("- 退出时会正确停止热键监听和隐藏系统托盘")
        
        print("\n📋 其他热键功能保持不变:")
        print("- 双击Ctrl键：切换搜索窗口显示/隐藏")
        print("- Ctrl+0~9：切换Profile")
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 项测试失败")
        print("请检查ESC键处理逻辑")

if __name__ == "__main__":
    main()
