"""主搜索窗口"""

import sys
import os
from typing import List
from PySide6.QtWidgets import (
    QApplication,
    QWidget,
    QHBoxLayout,
    QVBoxLayout,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QLabel,
    QPushButton,  # Added missing import
    QDialog,  # Added missing import
    QTabBar,  # 新增标签栏
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QMutex, QSize
from PySide6.QtGui import QFont, QPalette, QColor, QDesktopServices
from PySide6.QtCore import QUrl


from src.search.search import FileSearchEngine, SearchResult
from src.core.config import config_manager
from src.indexer.shortcut_parser import execute_shortcut
from src.indexer.profile_manager import profile_manager
from src.ui.dialogs import FileTypeFilterDialog, PathSelectionDialog, DefaultActionDialog, HelpDialog
from src.ui.style import get_white_theme_stylesheet


class SearchWorker(QThread):
    """搜索工作线程"""

    results_ready = Signal(list)

    def __init__(self, search_engine: FileSearchEngine):
        super().__init__()
        self.search_engine = search_engine
        self.query = ""
        self.mutex = QMutex()

    def set_query(self, query: str):
        """设置搜索查询"""
        self.mutex.lock()
        self.query = query
        self.mutex.unlock()

    def run(self):
        """执行搜索"""
        self.mutex.lock()
        current_query = self.query
        self.mutex.unlock()

        if not current_query.strip():
            self.results_ready.emit([])
            return

        try:
            results = self.search_engine.search(current_query, limit=50)  # 搜索更多结果
            self.results_ready.emit(results)
        except Exception as e:
            print(f"搜索错误: {e}")
            self.results_ready.emit([])


class SearchWindow(QWidget):
    """主搜索窗口"""

    def __init__(self, app_manager=None):
        super().__init__()
        self.app_manager = app_manager  # 添加应用管理器引用
        
        # 初始化profile管理器和搜索引擎
        self.profile_manager = profile_manager
        self.current_profile_id = self.profile_manager.current_profile_id
        self.search_engine = FileSearchEngine(profile_id=self.current_profile_id)
        self.search_worker = SearchWorker(self.search_engine)
        self.search_worker.results_ready.connect(self.on_search_results)

        # 延迟搜索定时器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

        # 创建索引状态检查定时器 - 优化检查频率
        self.index_status_timer = QTimer()
        self.index_status_timer.timeout.connect(self.check_index_status)
        # 修改检查频率：索引构建时更频繁，稳定后降低频率
        self.index_status_timer.start(5000)  # 改为每5秒检查一次，减少资源占用
        
        # 索引状态缓存 - 避免频繁检查
        self.last_index_check_time = 0
        self.index_check_interval = 5.0  # 5秒最小检查间隔

        # 索引状态标志
        self.index_ready = False

        # 文件类型筛选状态
        self.enabled_file_types = []  # 默认全不选

        # 当前默认行为状态 - 添加状态跟踪
        self.current_action = config_manager.get_default_action()

        self.init_ui()
        self.setup_style()
        
        # 初始检查索引状态
        self.check_index_status()

    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint
            | Qt.WindowType.WindowStaysOnTopHint
            | Qt.WindowType.Tool
        )

        # 固定窗口大小 - 增加高度以容纳标签栏
        self.setFixedSize(500, 160)

        # 创建主布局 - 减少间距
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(16, 16, 16, 16)  # 减少外边距
        self.main_layout.setSpacing(8)  # 减少垂直间距

        # 创建标签栏
        self.tab_bar = QTabBar()
        self.tab_bar.setShape(QTabBar.Shape.RoundedNorth)
        self.tab_bar.setExpanding(False)
        self.tab_bar.setMovable(False)
        
        # 添加10个标签（0-9）
        for i in range(10):
            tab_name = "默认桌面" if i == 0 else f"标签{i}"
            self.tab_bar.addTab(tab_name)
        
        # 设置当前选中的标签
        self.tab_bar.setCurrentIndex(self.current_profile_id)
        
        # 连接标签切换信号
        self.tab_bar.currentChanged.connect(self.on_profile_changed)
        
        # 设置标签栏样式
        self.tab_bar.setFixedHeight(32)
        self.tab_bar.setStyleSheet("""
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #ccc;
                padding: 4px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 30px;
                font-size: 11px;
            }
            QTabBar::tab:selected {
                background: #ffffff;
                border-bottom-color: #ffffff;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background: #e8e8e8;
            }
        """)
        
        self.main_layout.addWidget(self.tab_bar)

        # 创建输入区域布局
        input_layout = QHBoxLayout()
        input_layout.setSpacing(8)  # 减少输入框之间的间距

        # 创建主搜索输入框（占7/8宽度）
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入文件名进行搜索...")
        self.search_input.textChanged.connect(self.on_text_changed)
        self.search_input.returnPressed.connect(self.on_return_pressed)
        self.search_input.setFont(QFont("Microsoft YaHei", 12))
        self.search_input.setMinimumHeight(40)  # 减少输入框高度

        # 创建后缀输入框（占1/8宽度）
        self.suffix_input = QLineEdit()
        self.suffix_input.setPlaceholderText(".txt")
        self.suffix_input.textChanged.connect(self.on_suffix_changed)
        self.suffix_input.setFont(QFont("Microsoft YaHei", 10))
        self.suffix_input.setMinimumHeight(40)  # 与主输入框保持一致
        self.suffix_input.setMaximumWidth(80)  # 减少宽度

        # 设置焦点切换
        self.search_input.installEventFilter(self)
        self.suffix_input.installEventFilter(self)

        input_layout.addWidget(self.search_input, 7)
        input_layout.addWidget(self.suffix_input, 1)

        self.main_layout.addLayout(input_layout)

        # 创建控制按钮区域 - 简化布局
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(8)  # 减少按钮之间的间距

        # 操作方式按钮
        self.help_btn = QPushButton("操作方式")
        self.help_btn.setFixedSize(65, 28)  # 固定尺寸
        self.help_btn.setToolTip("查看快捷键和操作说明")
        self.help_btn.clicked.connect(self.show_help_dialog)

        # 文件类型筛选按钮 - 简化尺寸
        self.type_filter_btn = QPushButton("类型")
        self.type_filter_btn.setFixedSize(45, 28)  # 固定尺寸
        self.type_filter_btn.setToolTip("文件类型筛选")
        self.type_filter_btn.clicked.connect(self.show_type_filter)

        # 路径管理按钮 - 简化尺寸
        self.path_btn = QPushButton("目录")
        self.path_btn.setFixedSize(45, 28)  # 固定尺寸
        self.path_btn.setToolTip("管理扫描目录")
        self.path_btn.clicked.connect(self.show_path_dialog)

        # 默认行为切换按钮 - 简化尺寸
        self.action_btn = QPushButton()
        self.action_btn.setObjectName("action_toggle")  # 设置对象名以应用特殊样式
        self.action_btn.setFixedSize(80, 28)  # 固定尺寸
        self.action_btn.clicked.connect(self.toggle_action)
        self.update_action_button_text()  # 设置初始文本

        # 将按钮放在右侧，简化布局
        controls_layout.addStretch()
        controls_layout.addWidget(self.help_btn)
        controls_layout.addWidget(self.type_filter_btn)
        controls_layout.addWidget(self.path_btn)
        controls_layout.addWidget(self.action_btn)

        self.main_layout.addLayout(controls_layout)

        # 创建结果列表（初始隐藏）
        self.results_list = QListWidget()
        # 设置合适的列表高度范围
        self.results_list.setMaximumHeight(350)  # 增加最大高度以容纳更多项目
        self.results_list.setMinimumHeight(90)  # 确保至少能显示一个完整项目
        self.results_list.setVerticalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAsNeeded
        )
        self.results_list.setHorizontalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAlwaysOff
        )
        self.results_list.setVisible(False)
        self.results_list.itemActivated.connect(self.on_item_activated)

        self.main_layout.addWidget(self.results_list)

        self.setLayout(self.main_layout)

        # 居中显示窗口
        self.center_window()

    def on_profile_changed(self, profile_id: int):
        """处理profile切换"""
        if profile_id == self.current_profile_id:
            return
        
        print(f"切换到Profile {profile_id}")
        
        # 清空当前搜索结果
        self.clear_results()
        
        # 更新当前profile ID
        self.current_profile_id = profile_id
        
        # 切换profile manager
        self.profile_manager.switch_profile(profile_id, progress_callback=self._on_indexing_progress)
        
        # 更新搜索引擎
        self.search_engine = FileSearchEngine(profile_id=profile_id)
        
        # 更新搜索worker
        self.search_worker.search_engine = self.search_engine
        
        # 重置索引状态检查缓存
        self.last_index_check_time = 0
        self.index_check_interval = 3.0  # 切换后恢复较快的检查频率
        self.index_status_timer.setInterval(3000)
        
        # 立即检查新profile的索引状态
        self.check_index_status()
        
        # 触发搜索以显示当前输入的结果（如果索引准备好）
        if self.search_input.text().strip() and self.index_ready:
            self._trigger_search()
        
        print(f"已切换到Profile {profile_id}")

    def update_action_button_text(self):
        """更新行为按钮的文本显示"""
        if self.current_action == "open_file":
            self.action_btn.setText("打开文件")
            self.action_btn.setToolTip("当前：打开文件，点击或按Tab键切换为打开文件夹")
        else:
            self.action_btn.setText("打开文件夹")
            self.action_btn.setToolTip("当前：打开文件夹，点击或按Tab键切换为打开文件")

    def toggle_action(self):
        """切换默认行为"""
        if self.current_action == "open_file":
            self.current_action = "open_directory"
        else:
            self.current_action = "open_file"

        # 保存到配置
        config_manager.set_default_action(self.current_action)

        # 更新按钮文本
        self.update_action_button_text()

        print(f"默认行为已切换为: {self.current_action} (可按Tab键快速切换)")

    def setup_style(self):
        """设置窗口样式"""
        # 使用统一的白色主题样式
        self.setStyleSheet(get_white_theme_stylesheet())

    def center_window(self):
        """将窗口居中显示在屏幕上方1/3处"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = screen.height() // 3
        self.move(x, y)
    
    def check_index_status(self):
        """检查索引状态并更新UI（带缓存优化）"""
        import time
        
        # 节流机制：避免过于频繁的检查
        current_time = time.time()
        if current_time - self.last_index_check_time < self.index_check_interval:
            return
        
        try:
            # 检查当前profile的索引是否准备好
            new_index_ready = self.profile_manager.is_current_index_ready()
            
            # 只有状态发生变化时才更新UI和记录时间
            if new_index_ready != self.index_ready:
                self.index_ready = new_index_ready
                self.update_ui_based_on_index_status()
                self.last_index_check_time = current_time
                print(f"索引状态变化: {'就绪' if new_index_ready else '未就绪'}")
                
                # 如果索引已就绪，可以降低检查频率
                if new_index_ready:
                    self.index_check_interval = 10.0  # 就绪后每10秒检查一次
                    self.index_status_timer.setInterval(10000)
                else:
                    self.index_check_interval = 3.0   # 未就绪时每3秒检查一次
                    self.index_status_timer.setInterval(3000)
            else:
                # 状态未变化，记录检查时间但不做其他操作
                self.last_index_check_time = current_time
                
        except Exception as e:
            print(f"检查索引状态时出错: {e}")
            # 出错时假设索引未准备好
            if self.index_ready:
                self.index_ready = False
                self.update_ui_based_on_index_status()
                self.last_index_check_time = current_time
    
    def update_ui_based_on_index_status(self):
        """根据索引状态更新UI元素的可用性"""
        if self.index_ready:
            # 索引准备好，启用搜索框和后缀框
            self.search_input.setEnabled(True)
            self.suffix_input.setEnabled(True)
            self.search_input.setPlaceholderText("输入文件名进行搜索...")
            # 恢复样式
            self.search_input.setStyleSheet("")
            self.suffix_input.setStyleSheet("")
        else:
            # 索引未准备好，禁用搜索框和后缀框
            self.search_input.setEnabled(False)
            self.suffix_input.setEnabled(False)
            self.search_input.setPlaceholderText("索引初始化中，请稍等...")
            self.search_input.clear()  # 清空输入内容
            self.suffix_input.clear()  # 清空后缀内容
            # 添加禁用状态的样式
            disabled_style = """
                QLineEdit:disabled {
                    background-color: #f5f5f5;
                    color: #888888;
                    border: 1px solid #cccccc;
                }
            """
            self.search_input.setStyleSheet(disabled_style)
            self.suffix_input.setStyleSheet(disabled_style)
            
            # 清空搜索结果
            self.clear_results()

    def on_text_changed(self, text: str):
        """处理文本输入变化"""
        self._trigger_search()

    def on_suffix_changed(self, text: str):
        """处理后缀输入变化"""
        self._trigger_search()

    def _trigger_search(self):
        """触发搜索"""
        # 如果索引未准备好，不执行搜索
        if not self.index_ready:
            return
            
        # 重置搜索定时器
        self.search_timer.stop()

        main_query = self.search_input.text().strip()
        if main_query:
            # 使用配置的延迟时间
            delay = config_manager.get_search_delay()
            self.search_timer.start(delay)
        else:
            # 清空结果
            self.clear_results()

    def show_type_filter(self):
        """显示文件类型筛选页面"""
        # 创建新的页面式对话框
        from src.ui.pages import FileTypeFilterPage

        page = FileTypeFilterPage(self)
        page.filter_changed.connect(self.on_file_type_filter_changed)
        page.show()

    def show_path_dialog(self):
        """显示路径管理页面"""
        # 创建新的页面式对话框
        from src.ui.pages import PathManagementPage

        page = PathManagementPage(self)
        page.path_added.connect(self.on_path_added)
        page.show()

    def show_help_dialog(self):
        """显示操作方式和快捷键帮助对话框"""
        dialog = HelpDialog(self)
        dialog.exec()

    def on_file_type_filter_changed(self, selected_categories):
        """文件类型筛选改变"""
        self.enabled_file_types = selected_categories
        # 这里可以实时更新搜索结果
        if self.search_input.text().strip():
            self._trigger_search()

    def on_path_added(self, path):
        """路径添加回调，使用Profile管理器"""
        print(f"向Profile {self.current_profile_id} 处理扫描目录: {path}")
        # 使用profile管理器添加目录
        success = self.profile_manager.add_directory_to_current(path)
        if success:
            print(f"✅ 目录处理成功: {path} (Profile {self.current_profile_id})")
        else:
            print(f"❌ 目录处理失败: {path} (Profile {self.current_profile_id})")
    
    def _start_async_indexing(self, path):
        """异步启动索引构建，避免UI无响应"""
        from PySide6.QtCore import QThread, pyqtSignal
        from src.indexer.scanner import FileScanner
        
        class IndexingWorker(QThread):
            progress_updated = pyqtSignal(int)  # 进度信号
            indexing_finished = pyqtSignal(dict)  # 完成信号
            
            def __init__(self, path):
                super().__init__()
                self.path = path
                
            def run(self):
                try:
                    scanner = FileScanner()
                    result = scanner.build_index(
                        self.path, 
                        progress_callback=lambda count: self.progress_updated.emit(count)
                    )
                    self.indexing_finished.emit(result)
                except Exception as e:
                    print(f"索引构建失败: {e}")
                    self.indexing_finished.emit({"error": str(e)})
        
        # 创建并启动索引工作线程
        self.indexing_worker = IndexingWorker(path)
        self.indexing_worker.progress_updated.connect(self._on_indexing_progress)
        self.indexing_worker.indexing_finished.connect(self._on_indexing_finished)
        self.indexing_worker.start()
        print(f"已启动异步索引构建: {path}")
    
    def _on_indexing_progress(self, count):
        """处理索引进度更新"""
        # 这里可以更新UI进度条或状态显示
        if count % 1000 == 0:  # 每1000个文件显示一次进度
            print(f"索引进度: {count} 个文件已处理")
    
    def _on_indexing_finished(self, result):
        """处理索引完成"""
        if "error" in result:
            print(f"索引构建失败: {result['error']}")
        else:
            print(f"索引构建完成! 处理了 {result.get('total_files', 0)} 个文件，耗时 {result.get('elapsed_time', 0):.2f} 秒")
        
        # 清理工作线程引用
        if hasattr(self, 'indexing_worker'):
            self.indexing_worker.deleteLater()

    def perform_search(self):
        """执行搜索"""
        query = self.search_input.text().strip()
        suffix_filter = self.suffix_input.text().strip()

        # 构建搜索查询
        if query:
            search_query = query
            if suffix_filter:
                # 如果有后缀过滤，构建更精确的查询
                if not suffix_filter.startswith("."):
                    suffix_filter = f".{suffix_filter}"
                search_query = f"{query} suffix:{suffix_filter}"

            self.search_worker.set_query(search_query)
            if not self.search_worker.isRunning():
                self.search_worker.start()

    def on_search_results(self, results: List[SearchResult]):
        """处理搜索结果"""
        self.clear_results()

        if not results:
            self.results_list.setVisible(False)
            self.adjust_window_size()
            return

        # 根据启用的文件类型过滤结果
        if self.enabled_file_types:  # 只有当有选中的文件类型时才过滤
            # 检查是否选择了文件夹类型
            show_folders = "folders" in self.enabled_file_types
            
            # 获取文件扩展名（排除folders特殊类型）
            file_categories = [cat for cat in self.enabled_file_types if cat != "folders"]
            allowed_extensions = config_manager.get_all_allowed_extensions(file_categories) if file_categories else []
            
            filtered_results = []

            for result in results:
                if result.item_type == "folder":
                    # 文件夹根据是否选择了文件夹类型来决定
                    if show_folders:
                        filtered_results.append(result)
                elif result.item_type == "file":
                    # 文件根据后缀过滤
                    if file_categories:  # 如果选择了文件类型
                        if not result.suffix or result.suffix in allowed_extensions:
                            filtered_results.append(result)
                    # 如果没有选择任何文件类型但选择了文件夹，则不显示文件
                    elif not show_folders:
                        # 如果既没选文件类型也没选文件夹，这种情况不应该出现
                        filtered_results.append(result)

            results = filtered_results

        # 显示所有搜索结果（窗口高度只显示5条）
        for result in results:
            # 创建列表项
            item = QListWidgetItem()

            # 根据项目类型格式化显示信息
            if hasattr(result, "is_shortcut") and result.is_shortcut:
                # 快捷方式显示
                size_str = "快捷方式"
                type_icon = "🔗"
                # 使用快捷方式的显示名称
                display_name = (
                    result.get_display_name()
                    if hasattr(result, "get_display_name")
                    else result.filename
                )
            elif result.item_type == "folder":
                # 文件夹显示
                size_str = "文件夹"
                type_icon = "📁"
                display_name = result.filename
            else:
                # 文件显示
                size_str = self.format_file_size(result.size)
                type_icon = "📄"
                display_name = result.filename

            # 设置显示文本，包含类型图标
            item.setText(f"{type_icon} {display_name}\n{result.filepath} | {size_str}")

            # 存储完整结果数据
            item.setData(Qt.ItemDataRole.UserRole, result)

            self.results_list.addItem(item)

        # 显示结果列表
        self.results_list.setVisible(True)
        self.adjust_window_size()

        # 选中第一项
        if self.results_list.count() > 0:
            self.results_list.setCurrentRow(0)

    def format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        if size > 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        elif size > 1024:
            return f"{size / 1024:.1f} KB"
        else:
            return f"{size} bytes"

    def clear_results(self):
        """清空搜索结果"""
        self.results_list.clear()

    def adjust_window_size(self):
        """根据结果数量调整窗口大小 - 改进版本"""
        if self.results_list.isVisible() and self.results_list.count() > 0:
            # 更精确的窗口高度计算
            base_height = 160  # 基础高度（标签栏+输入框+按钮+边距）

            # 计算实际需要的列表高度
            item_count = self.results_list.count()
            # 每个项目的实际高度：内容高度(40px) + padding(24px) + margin(6px) + border(2px) = 72px
            single_item_height = 72
            # 列表的额外空间：padding(12px) + border(2px) = 14px
            list_padding = 14

            # 最多显示5个项目，超过则显示滚动条
            visible_items = min(item_count, 5)
            list_height = visible_items * single_item_height + list_padding

            # 确保列表高度不小于最小高度
            list_height = max(list_height, 90)

            new_height = base_height + list_height
            self.setFixedSize(500, new_height)
        else:
            # 恢复原始大小（包含标签栏）
            self.setFixedSize(500, 160)

        # 重新居中
        self.center_window()

    def on_return_pressed(self):
        """处理回车键按下"""
        if self.results_list.isVisible() and self.results_list.count() > 0:
            # 打开选中的文件
            current_item = self.results_list.currentItem()
            if current_item:
                self.open_file(current_item)

    def on_item_activated(self, item: QListWidgetItem):
        """处理列表项激活（双击或回车）"""
        self.open_file(item)

    def open_file(self, item: QListWidgetItem):
        """根据配置打开文件或目录"""
        result = item.data(Qt.ItemDataRole.UserRole)
        if result:
            file_path = result.filepath

            # 检查是否是快捷方式
            if hasattr(result, "is_shortcut") and result.is_shortcut:
                # 快捷方式：在打开文件模式下直接执行快捷方式，否则打开所在目录
                if self.current_action == "open_file":
                    success = execute_shortcut(file_path)
                    if success:
                        print(f"✓ 执行快捷方式成功: {file_path}")
                    else:
                        print(f"✗ 执行快捷方式失败: {file_path}")
                        # 快捷方式执行失败时，尝试打开所在目录作为备用方案
                        directory = os.path.dirname(file_path)
                        if os.path.exists(directory):
                            QDesktopServices.openUrl(QUrl.fromLocalFile(directory))
                            print(f"→ 已改为打开快捷方式所在目录: {directory}")
                        else:
                            print(f"✗ 快捷方式所在目录也不存在: {directory}")
                else:
                    # 打开快捷方式所在目录
                    directory = os.path.dirname(file_path)
                    if os.path.exists(directory):
                        QDesktopServices.openUrl(QUrl.fromLocalFile(directory))
                        print(f"打开快捷方式所在目录: {directory}")
                    else:
                        print(f"快捷方式所在目录不存在: {directory}")
            elif result.item_type == "folder":
                # 对于文件夹，直接打开文件夹本身
                if os.path.exists(file_path):
                    QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
                    print(f"打开文件夹: {file_path}")
                else:
                    print(f"文件夹不存在: {file_path}")
            else:
                # 对于普通文件，根据当前设置打开文件或所在目录
                if self.current_action == "open_file":
                    # 直接打开文件
                    if os.path.exists(file_path):
                        QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
                        print(f"打开文件: {file_path}")
                    else:
                        print(f"文件不存在: {file_path}")
                else:
                    # 打开文件所在目录
                    directory = os.path.dirname(file_path)
                    if os.path.exists(directory):
                        QDesktopServices.openUrl(QUrl.fromLocalFile(directory))
                        print(f"打开目录: {directory}")
                    else:
                        print(f"目录不存在: {directory}")

    def eventFilter(self, obj, event):
        """事件过滤器，处理焦点切换"""
        if event.type() == event.Type.KeyPress:
            # Tab键专门用于切换打开模式，不用于焦点切换
            if event.key() == Qt.Key.Key_Tab:
                self.toggle_action()
                return True
            elif event.key() == Qt.Key.Key_Right and obj == self.search_input:
                # 右箭头键切换到后缀框
                cursor_pos = self.search_input.cursorPosition()
                if cursor_pos == len(self.search_input.text()):
                    self.suffix_input.setFocus()
                    return True
            elif event.key() == Qt.Key.Key_Left and obj == self.suffix_input:
                # 左箭头键切换到搜索框
                cursor_pos = self.suffix_input.cursorPosition()
                if cursor_pos == 0:
                    self.search_input.setFocus()
                    return True

        return super().eventFilter(obj, event)

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            # ESC 键退出程序
            if self.app_manager:
                self.app_manager.quit_app()
        elif event.key() == Qt.Key.Key_Tab:
            # Tab 键在界面显示状态下切换打开模式（打开文件夹/打开文件）
            if self.isVisible():
                self.toggle_action()
            else:
                super().keyPressEvent(event)
        elif event.key() == Qt.Key.Key_Down:
            # 下方向键导航结果列表
            if self.results_list.isVisible():
                if self.results_list.hasFocus():
                    super().keyPressEvent(event)
                else:
                    self.results_list.setFocus()
                    self.results_list.setCurrentRow(0)
        elif event.key() == Qt.Key.Key_Up:
            # 上方向键导航结果列表
            if self.results_list.isVisible() and self.results_list.hasFocus():
                if self.results_list.currentRow() == 0:
                    self.search_input.setFocus()
                else:
                    super().keyPressEvent(event)
        else:
            super().keyPressEvent(event)

    def show_window(self):
        """显示窗口并获取焦点"""
        self.show()
        self.raise_()
        self.activateWindow()
        self.search_input.setFocus()
        self.search_input.selectAll()

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止搜索线程
        if self.search_worker.isRunning():
            self.search_worker.quit()
            self.search_worker.wait()
        
        # 停止索引状态检查定时器
        if hasattr(self, 'index_status_timer'):
            self.index_status_timer.stop()

        event.accept()
