"""应用程序入口"""

import sys
import os
from PySide6.QtWidgets import QApplication, QSystemTrayIcon, QMenu
from PySide6.QtCore import Qt, QCoreApplication
from PySide6.QtGui import QIcon, QAction

from src.ui.main_window import SearchWindow
from src.indexer.monitor import FileMonitor
from src.core.hotkey import hotkey_manager
from src.core.config import config_manager


class CleanTableApp:
    """CleanTable 应用程序主类"""

    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setQuitOnLastWindowClosed(False)  # 关闭窗口不退出程序

        # 创建主搜索窗口
        self.search_window = SearchWindow(app_manager=self)

        # 创建文件监听器
        self.file_monitor = FileMonitor()

        # 设置快捷键回调
        hotkey_manager.set_callback(self.toggle_search_window)
        hotkey_manager.set_profile_switch_callback(self.switch_profile)

        # 连接路径添加信号，自动重启监听
        self.search_window.path_added_signal = self.on_path_added

        # 创建系统托盘图标（可选）
        self.setup_tray_icon()

        # 设置应用程序信息
        QCoreApplication.setApplicationName("CleanTable")
        QCoreApplication.setApplicationVersion("0.2.0")
        QCoreApplication.setOrganizationName("CleanTable")

    def setup_tray_icon(self):
        """设置系统托盘图标"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            print("系统托盘不可用")
            return

        # 创建托盘图标，使用项目图标
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "icon.ico")
        if os.path.exists(icon_path):
            icon = QIcon(icon_path)
        else:
            # 如果图标文件不存在，使用默认图标
            icon = self.app.style().standardIcon(self.app.style().StandardPixmap.SP_ComputerIcon)
        
        self.tray_icon = QSystemTrayIcon(icon)

        # 创建托盘菜单
        tray_menu = QMenu()

        # 显示搜索窗口
        show_action = QAction("显示搜索窗口", self.app)
        show_action.triggered.connect(self.show_search_window)
        tray_menu.addAction(show_action)

        tray_menu.addSeparator()

        # 退出应用
        quit_action = QAction("退出", self.app)
        quit_action.triggered.connect(self.quit_app)
        tray_menu.addAction(quit_action)

        # 设置托盘图标和菜单
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.on_tray_activated)

        # 显示托盘图标
        self.tray_icon.show()
        self.tray_icon.showMessage(
            "CleanTable",
            "文件搜索工具已启动，点击图标或双击 Ctrl 显示搜索窗口",
            QSystemTrayIcon.MessageIcon.Information,
            3000,
        )

    def on_tray_activated(self, reason):
        """处理托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.Trigger:
            # 单击托盘图标显示搜索窗口
            self.show_search_window()

    def show_search_window(self):
        """显示搜索窗口"""
        self.search_window.show_window()

    def toggle_search_window(self):
        """切换搜索窗口显示状态"""
        print(f"toggle_search_window被调用")
        print(f"当前窗口状态: isVisible={self.search_window.isVisible()}")
        print(f"窗口对象: {self.search_window}")

        if self.search_window.isVisible():
            print("窗口可见，准备隐藏...")
            self.search_window.hide()
            print(f"隐藏后状态: isVisible={self.search_window.isVisible()}")
        else:
            print("窗口不可见，准备显示...")
            self.search_window.show_window()
            print(f"显示后状态: isVisible={self.search_window.isVisible()}")

    def switch_profile(self, profile_id: int):
        """切换搜索Profile"""
        print(f"快捷键触发: 切换到Profile {profile_id}")
        # 确保搜索窗口可见
        if not self.search_window.isVisible():
            self.search_window.show_window()
        
        # 切换profile
        self.search_window.tab_bar.setCurrentIndex(profile_id)
        # 触发profile改变事件（如果当前index已经是目标值，手动触发）
        if self.search_window.current_profile_id != profile_id:
            self.search_window.on_profile_changed(profile_id)
        
        print(f"已通过快捷键切换到Profile {profile_id}")

    def on_path_added(self, path: str):
        """路径添加后重启监听"""
        print(f"重启文件监听以包含新路径: {path}")
        self.file_monitor.stop_monitoring()
        scan_dirs = config_manager.get_scan_directories()
        if scan_dirs:
            self.file_monitor.start_monitoring(scan_dirs)

    def quit_app(self):
        """退出应用程序"""
        print("正在停止文件监听...")
        self.file_monitor.stop_monitoring()

        print("正在停止快捷键监听...")
        hotkey_manager.stop_listening()

        print("正在退出应用程序...")
        self.app.quit()

    def _force_reindex_all_directories(self, directories):
        """强制重新索引所有配置的目录"""
        from src.indexer.scanner import FileScanner

        scanner = FileScanner()
        total_dirs = len(directories)
        total_files_processed = 0

        print(f"开始重新索引 {total_dirs} 个目录...")

        for i, directory in enumerate(directories, 1):
            if os.path.exists(directory) and os.path.isdir(directory):
                dir_name = os.path.basename(directory) or directory
                print(f"\n[{i}/{total_dirs}] 正在索引: {dir_name}")
                print(f"    路径: {directory}")
                try:
                    result = scanner.build_index(
                        directory, progress_callback=self._print_reindex_progress
                    )
                    files_count = result.get("total_files", 0)
                    total_files_processed += files_count
                    print(f"    ✓ 完成: 处理了 {files_count} 个文件")
                except Exception as e:
                    print(f"    ✗ 索引失败: {e}")
            else:
                print(f"\n[{i}/{total_dirs}] ✗ 跳过无效目录: {directory}")

        print(f"\n🎉 启动时全量重新索引完成!")
        print(f"   总共处理了 {total_files_processed} 个文件")
        print(f"   索引了 {total_dirs} 个目录")

    def _print_reindex_progress(self, current, total, current_file):
        """打印重新索引进度"""
        if total > 0:
            percentage = (current / total) * 100
            # 每100个文件或完成时打印进度，避免输出过多
            if current % 100 == 0 or current == total:
                file_name = os.path.basename(current_file) if current_file else "处理中..."
                print(f"    进度: {current}/{total} ({percentage:.1f}%) - {file_name}")
        elif current % 50 == 0:  # 当总数未知时，每50个文件打印一次
            file_name = os.path.basename(current_file) if current_file else "处理中..."
            print(f"    已处理: {current} 个文件 - {file_name}")

    def run(self):
        """运行应用程序"""
        # 确保桌面目录已配置
        config_manager.ensure_desktop_directories_configured()

        # 启动文件监听和索引
        scan_dirs = config_manager.get_scan_directories()
        if scan_dirs:
            print(f"发现 {len(scan_dirs)} 个配置的扫描目录:")
            for dir_path in scan_dirs:
                print(f"  - {dir_path}")

            # 每次启动时强制重新索引所有目录
            print(f"\n正在执行启动时全量重新索引 {len(scan_dirs)} 个目录...")
            print("这将检查所有文件的变动并更新索引，请稍候...")
            self._force_reindex_all_directories(scan_dirs)

            self.file_monitor.start_monitoring(scan_dirs, skip_initial_scan=True)
        else:
            print("未配置扫描目录，请手动添加目录以启用实时监听")

        # 启动快捷键监听
        if hotkey_manager.start_listening():
            print("全局快捷键已启动 (双击 Ctrl)")

        # 显示搜索窗口
        self.show_search_window()

        print("\nCleanTable v0.2.0 已启动")
        print("- 双击 Ctrl 键切换搜索窗口")
        print("- Tab 键切换打开文件/打开文件夹模式")
        print("- ESC 键退出程序")
        print("- 点击系统托盘图标重新显示")
        print("- 托盘菜单退出程序")
        print("- 支持实时文件监听和增量索引更新")

        if scan_dirs:
            print("- ✓ 启动时已完成全量重新索引，文件索引为最新状态")
        else:
            print("- 请添加扫描目录以启用文件索引功能")

        # 启动事件循环
        return self.app.exec()


def main():
    """主函数"""
    try:
        app = CleanTableApp()
        sys.exit(app.run())
    except KeyboardInterrupt:
        print("\n程序已退出")
        sys.exit(0)
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
