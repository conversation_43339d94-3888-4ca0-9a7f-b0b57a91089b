#!/usr/bin/env python3
"""
测试简化查询
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_simple_query():
    """测试简化查询"""
    print("🔍 测试简化查询")
    print("=" * 50)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试不同的查询语法
        test_queries = [
            '"C:\\Users\\<USER>\\Desktop\\" folder:sjl',
            '("C:\\Users\\<USER>\\Desktop\\" folder:sjl)',
            '("C:\\Users\\<USER>\\Desktop\\" folder:sjl | "C:\\Users\\<USER>\\Documents\\" folder:sjl)',
            '"C:\\Users\\<USER>\\Desktop\\" folder:sjl | "C:\\Users\\<USER>\\Documents\\" folder:sjl',
        ]
        
        for i, query in enumerate(test_queries):
            print(f"\n🔍 测试查询 {i+1}: {query}")
            try:
                results = sdk.search(query, max_results=5)
                print(f"   结果数量: {len(results)}")
                
                for j, result in enumerate(results):
                    folder_icon = "📁" if result.is_folder else "📄"
                    print(f"     {j+1}. {folder_icon} {result.filename} -> {result.full_path}")
                
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        print("\n✅ 简化查询测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 简化查询测试")
    print("=" * 60)
    
    test_simple_query()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")

if __name__ == "__main__":
    main()
