#!/usr/bin/env python3
"""
修复Simple Desktop的Ctrl+数字键Profile切换功能
"""

import sys
import time
import ctypes
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Windows API常量
MOD_CONTROL = 0x0002
VK_0 = 0x30
VK_9 = 0x39

def diagnose_hotkey_registration():
    """诊断热键注册问题"""
    print("🔍 诊断Ctrl+数字键热键注册问题")
    print("=" * 60)
    
    try:
        user32 = ctypes.windll.user32
        
        print("1. 测试单个热键注册:")
        
        # 测试注册Ctrl+0
        test_id = 9999
        vk_code = VK_0
        
        result = user32.RegisterHotKey(None, test_id, MOD_CONTROL, vk_code)
        
        if result:
            print("   ✅ Ctrl+0 注册成功")
            # 立即注销测试热键
            user32.UnregisterHotKey(None, test_id)
        else:
            error_code = ctypes.GetLastError()
            print(f"   ❌ Ctrl+0 注册失败，错误代码: {error_code}")
            
            if error_code == 1409:  # ERROR_HOTKEY_ALREADY_REGISTERED
                print("      原因: 热键已被其他应用程序注册")
            elif error_code == 5:  # ERROR_ACCESS_DENIED
                print("      原因: 权限不足，可能需要管理员权限")
            else:
                print(f"      原因: 未知错误 ({error_code})")
        
        print("\n2. 测试所有Ctrl+数字键:")
        
        failed_keys = []
        successful_keys = []
        
        for i in range(10):
            hotkey_id = 2000 + i  # 使用不同的ID避免冲突
            vk_code = VK_0 + i
            
            result = user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
            
            if result:
                successful_keys.append(i)
                print(f"   ✅ Ctrl+{i} 注册成功")
                # 立即注销
                user32.UnregisterHotKey(None, hotkey_id)
            else:
                failed_keys.append(i)
                error_code = ctypes.GetLastError()
                print(f"   ❌ Ctrl+{i} 注册失败 (错误: {error_code})")
        
        print(f"\n📊 注册结果统计:")
        print(f"   成功: {len(successful_keys)}/10")
        print(f"   失败: {len(failed_keys)}/10")
        
        if failed_keys:
            print(f"   失败的键: {failed_keys}")
        
        return len(failed_keys) == 0
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_alternative_hotkey_solution():
    """创建替代的热键解决方案"""
    print("\n🔧 创建替代热键解决方案")
    print("=" * 60)
    
    try:
        # 创建改进的热键管理器
        improved_hotkey_code = '''
"""
改进的热键管理器，支持Profile切换
"""

import time
import threading
import ctypes
from typing import Optional, Callable

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_0 = 0x30

class ImprovedHotkeyManager:
    """改进的热键管理器，支持更好的Profile切换"""
    
    def __init__(self):
        """初始化热键管理器"""
        self.is_listening = False
        self.toggle_callback: Optional[Callable[[], None]] = None
        self.profile_switch_callback: Optional[Callable[[int], None]] = None
        
        # 双击Ctrl检测
        self.last_ctrl_press_time = 0
        self.ctrl_press_count = 0
        self.ctrl_check_thread = None
        self.stop_event = threading.Event()
        
        # Windows API函数
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
        # 注册的热键ID
        self.registered_hotkeys = set()
        
        # 使用不同的热键ID范围避免冲突
        self.profile_hotkey_base = 3000
    
    def set_callback(self, callback: Callable[[], None]):
        """设置窗口切换回调"""
        self.toggle_callback = callback
    
    def set_profile_switch_callback(self, callback: Callable[[int], None]):
        """设置Profile切换回调"""
        self.profile_switch_callback = callback
    
    def start_listening(self) -> bool:
        """开始监听快捷键"""
        if self.is_listening:
            return True
        
        try:
            # 注册Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            successful_registrations = 0
            
            for i in range(10):
                hotkey_id = self.profile_hotkey_base + i
                vk_code = VK_0 + i
                
                # 尝试注册热键
                if self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code):
                    self.registered_hotkeys.add(hotkey_id)
                    successful_registrations += 1
                    print(f"[HOTKEY] Ctrl+{i} 注册成功")
                else:
                    error_code = ctypes.GetLastError()
                    print(f"[HOTKEY] Ctrl+{i} 注册失败 (错误: {error_code})")
                    
                    # 如果是权限问题，尝试其他方案
                    if error_code == 5:  # ERROR_ACCESS_DENIED
                        print(f"[HOTKEY] 权限不足，跳过 Ctrl+{i}")
                    elif error_code == 1409:  # ERROR_HOTKEY_ALREADY_REGISTERED
                        print(f"[HOTKEY] Ctrl+{i} 已被占用，尝试替代方案")
                        # 可以在这里实现替代的热键组合
            
            print(f"[HOTKEY] Profile热键注册完成: {successful_registrations}/10")
            
            # 启动双击Ctrl检测线程
            self.stop_event.clear()
            self.ctrl_check_thread = threading.Thread(target=self._ctrl_monitor_thread, daemon=True)
            self.ctrl_check_thread.start()
            
            # 启动消息循环线程
            self.message_thread = threading.Thread(target=self._message_loop, daemon=True)
            self.message_thread.start()
            
            self.is_listening = True
            return True
            
        except Exception as e:
            print(f"[ERROR] 热键监听启动失败: {e}")
            self.stop_listening()
            return False
    
    def stop_listening(self):
        """停止监听快捷键"""
        if not self.is_listening:
            return
        
        self.is_listening = False
        self.stop_event.set()
        
        # 注销热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()
        
        # 等待线程结束
        if self.ctrl_check_thread and self.ctrl_check_thread.is_alive():
            self.ctrl_check_thread.join(timeout=1.0)
        
        print("[HOTKEY] 热键监听已停止")
    
    def _message_loop(self):
        """Windows消息循环"""
        import ctypes.wintypes as wintypes
        
        msg = wintypes.MSG()
        
        while self.is_listening:
            try:
                # 获取消息
                result = self.user32.GetMessage(ctypes.byref(msg), None, 0, 0)
                
                if result == -1:  # 错误
                    break
                elif result == 0:  # WM_QUIT
                    break
                
                # 处理热键消息
                if msg.message == WM_HOTKEY:
                    hotkey_id = msg.wParam
                    self._handle_hotkey(hotkey_id)
                
                # 分发消息
                self.user32.TranslateMessage(ctypes.byref(msg))
                self.user32.DispatchMessage(ctypes.byref(msg))
                
            except Exception as e:
                print(f"[ERROR] 消息循环错误: {e}")
                break
    
    def _handle_hotkey(self, hotkey_id: int):
        """处理热键消息"""
        if self.profile_hotkey_base <= hotkey_id <= self.profile_hotkey_base + 9:
            # Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            profile_id = hotkey_id - self.profile_hotkey_base
            print(f"[HOTKEY] 检测到 Ctrl+{profile_id}")
            
            if self.profile_switch_callback:
                try:
                    self.profile_switch_callback(profile_id)
                except Exception as e:
                    print(f"[ERROR] Profile切换回调错误: {e}")
    
    def _ctrl_monitor_thread(self):
        """监控Ctrl键双击的线程"""
        last_state = False
        
        while not self.stop_event.is_set():
            try:
                # 检查Ctrl键状态
                ctrl_state = self.user32.GetAsyncKeyState(0x11)  # VK_CONTROL
                is_pressed = bool(ctrl_state & 0x8000)
                
                # 边沿触发：检测从未按下到按下的转换
                if is_pressed and not last_state:
                    current_time = time.time()
                    
                    # 如果有之前的按键记录，检查双击
                    if self.ctrl_press_count > 0:
                        time_diff = current_time - self.last_ctrl_press_time
                        
                        if time_diff <= 0.5:  # 双击间隔
                            # 双击Ctrl检测到
                            if self.toggle_callback:
                                try:
                                    self.toggle_callback()
                                    print("[HOTKEY] 双击Ctrl检测到")
                                except Exception as e:
                                    print(f"[ERROR] 切换回调错误: {e}")
                            
                            # 重置状态
                            self.ctrl_press_count = 0
                            self.last_ctrl_press_time = 0
                            last_state = is_pressed
                            continue
                    
                    # 记录新的按键
                    self.last_ctrl_press_time = current_time
                    self.ctrl_press_count = 1
                
                # 重置超时的计数
                current_time = time.time()
                if (self.ctrl_press_count > 0 and 
                    current_time - self.last_ctrl_press_time > 0.5):
                    self.ctrl_press_count = 0
                
                last_state = is_pressed
                
                # 短暂休眠
                time.sleep(0.05)
                
            except Exception as e:
                print(f"[ERROR] Ctrl监控错误: {e}")
                break
'''
        
        # 保存改进的热键管理器
        improved_file = project_root / "simple_desktop" / "core" / "improved_hotkey.py"
        
        with open(improved_file, 'w', encoding='utf-8') as f:
            f.write(improved_hotkey_code)
        
        print(f"✅ 改进的热键管理器已保存到: {improved_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建替代方案失败: {e}")
        return False

def test_profile_switching():
    """测试Profile切换功能"""
    print("\n🧪 测试Profile切换功能")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.core.improved_hotkey import ImprovedHotkeyManager
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        # 创建改进的热键管理器
        hotkey_manager = ImprovedHotkeyManager()
        
        # 创建测试回调
        profile_switches = []
        
        def test_profile_callback(profile_id):
            profile_switches.append(profile_id)
            print(f"[TEST] Profile切换到: {profile_id}")
        
        # 设置回调
        hotkey_manager.set_profile_switch_callback(test_profile_callback)
        
        # 启动热键监听
        success = hotkey_manager.start_listening()
        
        if success:
            print("✅ 改进的热键管理器启动成功")
            
            print("\n📝 测试说明:")
            print("1. 改进的热键管理器已启动")
            print("2. 请尝试按 Ctrl+0 到 Ctrl+9")
            print("3. 观察Profile切换是否正常工作")
            print("4. 测试将在10秒后自动结束")
            
            # 等待测试
            start_time = time.time()
            while time.time() - start_time < 10:
                app.processEvents()
                time.sleep(0.1)
            
            print(f"\n📊 测试结果:")
            print(f"   Profile切换次数: {len(profile_switches)}")
            if profile_switches:
                print(f"   切换的Profile: {profile_switches}")
                print("   ✅ Profile切换功能正常")
            else:
                print("   ⚠️ 未检测到Profile切换")
            
            # 清理
            hotkey_manager.stop_listening()
            
            return len(profile_switches) > 0
        else:
            print("❌ 改进的热键管理器启动失败")
            return False
        
    except Exception as e:
        print(f"❌ Profile切换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - Ctrl+数字键Profile切换修复")
    print("=" * 80)
    
    # 执行修复步骤
    steps = [
        ("诊断热键注册问题", diagnose_hotkey_registration),
        ("创建替代热键解决方案", create_alternative_hotkey_solution),
        ("测试Profile切换功能", test_profile_switching),
    ]
    
    passed_steps = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            if step_func():
                passed_steps += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 Ctrl+数字键Profile切换修复完成！")
    print(f"完成步骤: {passed_steps}/{total_steps}")
    
    if passed_steps >= 2:  # 至少诊断和创建解决方案成功
        print("\n💡 修复建议:")
        print("1. 使用改进的热键管理器替代原始版本")
        print("2. 改进版本包含更好的错误处理和诊断")
        print("3. 支持部分热键注册失败的情况")
        print("4. 提供详细的注册状态反馈")
        
        print("\n📋 下一步:")
        print("1. 在app.py中集成改进的热键管理器")
        print("2. 测试Profile切换功能")
        print("3. 验证UI更新是否正确")
    else:
        print(f"\n⚠️ 修复未完成，请检查相关问题")

if __name__ == "__main__":
    main()
