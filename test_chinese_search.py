# -*- coding: utf-8 -*-
"""
诊断中文文件名模糊查询问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_chinese_filename_search():
    """测试中文文件名搜索"""
    print("测试中文文件名搜索")
    print("=" * 60)
    
    target_filename = "光与影：33号远征队.url"
    print(f"目标文件名: {target_filename}")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        engine = FileSearchEngine(profile_id=0)
        sdk = get_everything_sdk()
        
        # 测试用例
        test_queries = [
            "33",           # 数字部分
            "远征队",        # 中文词汇
            "光与影",        # 文件名开头
            "33号",         # 数字+中文
            "远征",         # 部分中文
        ]
        
        print(f"\n测试搜索查询:")
        for query in test_queries:
            print(f"\n测试查询: '{query}'")
            
            # 测试1: 直接Everything SDK
            print("  Everything SDK直接测试:")
            sdk_results = sdk.search(
                query=query,
                max_results=20,
                match_case=False,
                match_whole_word=False,
                use_regex=False
            )
            
            found_target = False
            for result in sdk_results:
                if target_filename in result.filename:
                    print(f"    找到: {result.filename}")
                    found_target = True
                    break
            
            if not found_target:
                print(f"    未找到目标文件 (总结果数: {len(sdk_results)})")
                # 显示前几个结果
                for i, result in enumerate(sdk_results[:3]):
                    if "光与影" in result.filename or "33" in result.filename or "远征" in result.filename:
                        print(f"      {i+1}. {result.filename}")
            
            # 测试2: 新搜索引擎
            print("  新搜索引擎测试:")
            engine_results = engine.search(query, limit=20)
            
            found_target = False
            for result in engine_results:
                if target_filename in result.filename:
                    print(f"    找到: {result.filename}")
                    found_target = True
                    break
            
            if not found_target:
                print(f"    未找到目标文件 (总结果数: {len(engine_results)})")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_wildcard_queries():
    """测试通配符查询"""
    print(f"\n\n测试通配符查询")
    print("=" * 60)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试各种通配符查询
        wildcard_queries = [
            "*33*",
            "*远征队*",
            "*光与影*",
            "*33号*",
            "*远征*",
        ]
        
        for query in wildcard_queries:
            print(f"\n通配符查询: '{query}'")
            results = sdk.search(
                query=query,
                max_results=10,
                match_case=False,
                match_whole_word=False,
                use_regex=False
            )
            
            print(f"  结果数: {len(results)}")
            found_target = False
            
            for result in results:
                if "光与影" in result.filename and "33号远征队" in result.filename:
                    print(f"  找到目标: {result.filename}")
                    found_target = True
                    break
            
            if not found_target:
                print(f"  未找到目标文件")
                # 显示相关结果
                for i, result in enumerate(results[:3]):
                    if "光与影" in result.filename or "33" in result.filename or "远征" in result.filename:
                        print(f"    {i+1}. {result.filename}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("开始诊断中文文件名搜索问题")
    print("=" * 80)
    
    test_chinese_filename_search()
    test_wildcard_queries()
    
    print("\n" + "=" * 80)
    print("诊断完成")


if __name__ == "__main__":
    main()
