#!/usr/bin/env python3
"""
测试GUI功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui():
    """测试GUI组件"""
    print("=== 测试GUI组件 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from simple_desktop.ui.dialogs import FileTypeFilterDialog, PathManagementDialog, HelpDialog
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        print("✓ QApplication创建成功")
        
        # 测试搜索窗口
        search_window = FloatingSearchWindow()
        print("✓ 搜索窗口创建成功")
        
        # 测试对话框
        file_dialog = FileTypeFilterDialog()
        print("✓ 文件类型对话框创建成功")
        
        path_dialog = PathManagementDialog()
        print("✓ 路径管理对话框创建成功")
        
        help_dialog = HelpDialog()
        print("✓ 帮助对话框创建成功")
        
        # 显示搜索窗口进行快速测试
        search_window.show_window()
        print("✓ 搜索窗口显示成功")
        
        # 短暂显示后关闭
        from PySide6.QtCore import QTimer
        QTimer.singleShot(2000, app.quit)  # 2秒后退出
        
        print("GUI测试窗口将显示2秒...")
        app.exec()
        
        print("✓ GUI测试完成")
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("Simple Desktop GUI测试")
    print("=" * 30)
    
    test_gui()
    
    print("=" * 30)
    print("GUI测试完成！")

if __name__ == "__main__":
    main()
