"""
全局快捷键管理器
实现双击Ctrl和Profile切换快捷键
"""

import time
import threading
from typing import Optional, Callable
import ctypes
from ctypes import wintypes
import ctypes.wintypes as wintypes

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_CONTROL = 0x11
VK_0 = 0x30
VK_9 = 0x39

# 双击检测相关
DOUBLE_CLICK_INTERVAL = 0.5  # 双击间隔（秒）


class HotkeyManager:
    """全局快捷键管理器"""
    
    def __init__(self):
        """初始化快捷键管理器"""
        self.is_listening = False
        self.toggle_callback: Optional[Callable[[], None]] = None
        self.profile_switch_callback: Optional[Callable[[int], None]] = None
        
        # 双击Ctrl检测
        self.last_ctrl_press_time = 0
        self.ctrl_press_count = 0
        self.ctrl_check_thread = None
        self.stop_event = threading.Event()
        
        # Windows API函数
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
        # 注册的热键ID
        self.registered_hotkeys = set()
    
    def set_callback(self, callback: Callable[[], None]):
        """设置窗口切换回调"""
        self.toggle_callback = callback
    
    def set_profile_switch_callback(self, callback: Callable[[int], None]):
        """设置Profile切换回调"""
        self.profile_switch_callback = callback
    
    def start_listening(self) -> bool:
        """开始监听快捷键"""
        if self.is_listening:
            return True
        
        try:
            # 注册Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            for i in range(10):
                hotkey_id = 1000 + i
                vk_code = VK_0 + i
                
                if self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code):
                    self.registered_hotkeys.add(hotkey_id)
                else:
                    print(f"Failed to register hotkey Ctrl+{i}")
            
            # 启动双击Ctrl检测线程
            self.stop_event.clear()
            self.ctrl_check_thread = threading.Thread(target=self._ctrl_monitor_thread, daemon=True)
            self.ctrl_check_thread.start()
            
            # 启动消息循环线程
            self.message_thread = threading.Thread(target=self._message_loop, daemon=True)
            self.message_thread.start()
            
            self.is_listening = True
            return True
            
        except Exception as e:
            print(f"Failed to start hotkey listening: {e}")
            self.stop_listening()
            return False
    
    def stop_listening(self):
        """停止监听快捷键"""
        if not self.is_listening:
            return
        
        self.is_listening = False
        self.stop_event.set()
        
        # 注销热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()
        
        # 等待线程结束
        if self.ctrl_check_thread and self.ctrl_check_thread.is_alive():
            self.ctrl_check_thread.join(timeout=1.0)
        
        print("Hotkey listening stopped")
    
    def _message_loop(self):
        """Windows消息循环"""
        msg = wintypes.MSG()
        
        while self.is_listening:
            try:
                # 获取消息
                result = self.user32.GetMessage(ctypes.byref(msg), None, 0, 0)
                
                if result == -1:  # 错误
                    break
                elif result == 0:  # WM_QUIT
                    break
                
                # 处理热键消息
                if msg.message == WM_HOTKEY:
                    hotkey_id = msg.wParam
                    self._handle_hotkey(hotkey_id)
                
                # 分发消息
                self.user32.TranslateMessage(ctypes.byref(msg))
                self.user32.DispatchMessage(ctypes.byref(msg))
                
            except Exception as e:
                print(f"Message loop error: {e}")
                break
    
    def _handle_hotkey(self, hotkey_id: int):
        """处理热键消息"""
        if 1000 <= hotkey_id <= 1009:
            # Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            profile_id = hotkey_id - 1000
            if self.profile_switch_callback:
                try:
                    self.profile_switch_callback(profile_id)
                except Exception as e:
                    print(f"Profile switch callback error: {e}")
    
    def _ctrl_monitor_thread(self):
        """监控Ctrl键双击的线程 - 使用改进的检测逻辑"""
        last_state = False

        while not self.stop_event.is_set():
            try:
                # 检查Ctrl键状态
                ctrl_state = self.user32.GetAsyncKeyState(VK_CONTROL)
                is_pressed = bool(ctrl_state & 0x8000)

                # 边沿触发：检测从未按下到按下的转换
                if is_pressed and not last_state:
                    current_time = time.time()

                    # 如果有之前的按键记录，检查双击
                    if self.ctrl_press_count > 0:
                        time_diff = current_time - self.last_ctrl_press_time

                        if time_diff <= DOUBLE_CLICK_INTERVAL:
                            # 双击Ctrl检测到
                            if self.toggle_callback:
                                try:
                                    self.toggle_callback()
                                    print("Double Ctrl detected - callback executed")
                                except Exception as e:
                                    print(f"Toggle callback error: {e}")

                            # 重置状态
                            self.ctrl_press_count = 0
                            self.last_ctrl_press_time = 0
                            last_state = is_pressed
                            continue

                    # 记录新的按键
                    self.last_ctrl_press_time = current_time
                    self.ctrl_press_count = 1
                    print(f"Ctrl press detected at {current_time:.3f}")

                # 重置超时的计数
                current_time = time.time()
                if (self.ctrl_press_count > 0 and
                    current_time - self.last_ctrl_press_time > DOUBLE_CLICK_INTERVAL):
                    self.ctrl_press_count = 0

                last_state = is_pressed

                # 短暂休眠
                time.sleep(0.05)

            except Exception as e:
                print(f"Ctrl monitor error: {e}")
                break
    
    def is_hotkey_available(self, vk_code: int, modifiers: int = 0) -> bool:
        """检查热键是否可用"""
        test_id = 9999
        if self.user32.RegisterHotKey(None, test_id, modifiers, vk_code):
            self.user32.UnregisterHotKey(None, test_id)
            return True
        return False
    
    def get_registered_hotkeys(self) -> list:
        """获取已注册的热键列表"""
        hotkeys = []
        for hotkey_id in self.registered_hotkeys:
            if 1000 <= hotkey_id <= 1009:
                profile_id = hotkey_id - 1000
                hotkeys.append(f"Ctrl+{profile_id}")
        return hotkeys


# 全局快捷键管理器实例
hotkey_manager = HotkeyManager()
