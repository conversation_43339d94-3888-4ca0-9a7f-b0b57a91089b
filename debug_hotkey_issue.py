#!/usr/bin/env python3
"""
调试Simple Desktop全局热键功能问题
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def step1_check_hotkey_components():
    """步骤1：检查热键相关组件"""
    print("=" * 60)
    print("步骤1：检查热键相关组件")
    print("=" * 60)
    
    try:
        # 检查是否能导入热键相关模块
        print("🔍 检查热键模块导入:")
        
        try:
            from simple_desktop.core.hotkey import HotkeyManager
            print("  ✅ HotkeyManager 导入成功")
        except ImportError as e:
            print(f"  ❌ HotkeyManager 导入失败: {e}")
            return False
        
        try:
            from simple_desktop.ui.search_window import FloatingSearchWindow
            print("  ✅ FloatingSearchWindow 导入成功")
        except ImportError as e:
            print(f"  ❌ FloatingSearchWindow 导入失败: {e}")
            return False
        
        # 检查热键管理器的方法
        print("\n🔍 检查HotkeyManager方法:")
        hotkey_methods = [
            'set_callback',
            'set_profile_switch_callback',
            'start_listening',
            'stop_listening'
        ]

        for method in hotkey_methods:
            if hasattr(HotkeyManager, method):
                print(f"  ✅ {method} 方法存在")
            else:
                print(f"  ❌ {method} 方法缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 组件检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step2_test_hotkey_registration():
    """步骤2：测试热键注册"""
    print("\n" + "=" * 60)
    print("步骤2：测试热键注册")
    print("=" * 60)
    
    try:
        from simple_desktop.core.hotkey import HotkeyManager
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建热键管理器
        hotkey_manager = HotkeyManager()
        
        print("🔍 测试热键注册:")
        
        # 测试回调函数
        def test_callback():
            print("  🎯 热键回调函数被调用!")

        # 尝试设置回调和启动监听
        try:
            # 设置回调函数
            hotkey_manager.set_callback(test_callback)
            print("  ✅ 回调函数设置成功")

            # 开始监听
            success = hotkey_manager.start_listening()
            print(f"  热键监听启动结果: {'✅ 成功' if success else '❌ 失败'}")

            if success:
                print("  ✅ 双击Ctrl热键监听已启动")

                # 检查监听状态
                is_listening = hotkey_manager.is_listening
                print(f"  监听状态: {'✅ 正在监听' if is_listening else '❌ 未监听'}")

                return hotkey_manager
            else:
                print("  ❌ 热键监听启动失败")
                return None
                
        except Exception as e:
            print(f"  ❌ 热键注册异常: {e}")
            return None
        
    except Exception as e:
        print(f"❌ 热键注册测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def step3_test_window_show_hide():
    """步骤3：测试窗口显示/隐藏逻辑"""
    print("\n" + "=" * 60)
    print("步骤3：测试窗口显示/隐藏逻辑")
    print("=" * 60)
    
    try:
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        print("🔍 测试窗口显示/隐藏:")
        
        # 测试显示窗口
        print("  1. 测试显示窗口...")
        search_window.show_window()
        print(f"     窗口可见性: {'✅ 可见' if search_window.isVisible() else '❌ 不可见'}")
        
        # 等待一下
        app.processEvents()
        time.sleep(1)
        
        # 测试隐藏窗口
        print("  2. 测试隐藏窗口...")
        search_window.hide_window()
        print(f"     窗口可见性: {'✅ 隐藏' if not search_window.isVisible() else '❌ 仍可见'}")
        
        # 测试切换功能
        print("  3. 测试切换功能...")
        initial_visible = search_window.isVisible()
        search_window.toggle_window()
        final_visible = search_window.isVisible()
        
        if initial_visible != final_visible:
            print("     ✅ 窗口切换功能正常")
        else:
            print("     ❌ 窗口切换功能异常")
        
        # 检查窗口方法
        print("\n🔍 检查窗口方法:")
        window_methods = ['show_window', 'hide_window', 'toggle_window']
        
        for method in window_methods:
            if hasattr(search_window, method):
                print(f"  ✅ {method} 方法存在")
            else:
                print(f"  ❌ {method} 方法缺失")
        
        return search_window
        
    except Exception as e:
        print(f"❌ 窗口显示/隐藏测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def step4_test_hotkey_integration():
    """步骤4：测试热键与窗口的集成"""
    print("\n" + "=" * 60)
    print("步骤4：测试热键与窗口的集成")
    print("=" * 60)
    
    try:
        from simple_desktop.core.hotkey import HotkeyManager
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建组件
        search_window = FloatingSearchWindow()
        hotkey_manager = HotkeyManager()
        
        print("🔍 测试热键与窗口集成:")
        
        # 创建集成回调函数
        toggle_count = 0
        def integrated_callback():
            nonlocal toggle_count
            toggle_count += 1
            print(f"  🎯 热键触发 #{toggle_count}: 切换窗口状态")
            
            try:
                before_visible = search_window.isVisible()
                search_window.toggle_window()
                after_visible = search_window.isVisible()
                
                print(f"     窗口状态: {before_visible} -> {after_visible}")
                
                if before_visible != after_visible:
                    print("     ✅ 窗口切换成功")
                else:
                    print("     ❌ 窗口切换失败")
                    
            except Exception as e:
                print(f"     ❌ 窗口切换异常: {e}")
        
        # 设置集成回调并启动监听
        hotkey_manager.set_callback(integrated_callback)
        success = hotkey_manager.start_listening()

        if success:
            print("  ✅ 集成热键监听启动成功")
            
            # 显示测试说明
            print("\n📝 手动测试说明:")
            print("  1. 热键监听已启动")
            print("  2. 请尝试双击Ctrl键")
            print("  3. 观察窗口是否正确切换显示/隐藏状态")
            print("  4. 测试将在10秒后自动结束")
            
            # 显示窗口以便观察
            search_window.show_window()
            
            # 等待用户测试
            start_time = time.time()
            while time.time() - start_time < 10:
                app.processEvents()
                time.sleep(0.1)
            
            print(f"\n  测试结果: 热键触发了 {toggle_count} 次")
            
            # 清理
            hotkey_manager.stop_listening()
            search_window.hide_window()
            
            return toggle_count > 0
            
        else:
            print("  ❌ 集成热键注册失败")
            return False
        
    except Exception as e:
        print(f"❌ 热键集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step5_check_system_conflicts():
    """步骤5：检查系统冲突"""
    print("\n" + "=" * 60)
    print("步骤5：检查系统冲突")
    print("=" * 60)
    
    try:
        import psutil
        
        print("🔍 检查可能的热键冲突:")
        
        # 检查运行的进程
        print("  正在运行的可能冲突的应用程序:")
        
        conflict_apps = [
            "everything.exe",
            "wox.exe", 
            "launchy.exe",
            "alfred.exe",
            "keypirinha.exe"
        ]
        
        running_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                if any(app in proc_name for app in conflict_apps):
                    running_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if running_processes:
            print("  ⚠️ 发现可能冲突的应用程序:")
            for proc in running_processes:
                print(f"    - {proc['name']} (PID: {proc['pid']})")
        else:
            print("  ✅ 未发现明显的冲突应用程序")
        
        # 检查系统热键使用情况
        print("\n  系统热键使用建议:")
        print("    - Ctrl+Ctrl 是相对少见的组合键，冲突概率较低")
        print("    - 如果仍有冲突，可考虑更换为其他组合键")
        print("    - 建议的替代方案: Ctrl+Space, Alt+Space, Win+Space")
        
        return True
        
    except ImportError:
        print("  ⚠️ psutil模块未安装，跳过进程检查")
        return True
    except Exception as e:
        print(f"❌ 系统冲突检查失败: {e}")
        return False

def step6_comprehensive_diagnosis():
    """步骤6：综合诊断和解决方案"""
    print("\n" + "=" * 60)
    print("步骤6：综合诊断和解决方案")
    print("=" * 60)
    
    try:
        print("🔍 综合诊断:")
        
        # 检查主要组件
        components_ok = step1_check_hotkey_components()
        
        if not components_ok:
            print("\n❌ 问题诊断: 热键组件缺失或损坏")
            print("🛠️ 解决方案:")
            print("  1. 检查 simple_desktop.core.hotkey_manager 模块是否存在")
            print("  2. 确认所有必要的依赖项已正确安装")
            print("  3. 重新安装或修复Simple Desktop应用程序")
            return False
        
        # 检查热键注册
        hotkey_manager = step2_test_hotkey_registration()
        
        if hotkey_manager is None:
            print("\n❌ 问题诊断: 热键注册失败")
            print("🛠️ 解决方案:")
            print("  1. 检查是否有管理员权限（全局热键可能需要）")
            print("  2. 确认热键组合键格式正确")
            print("  3. 检查是否与其他应用程序冲突")
            return False
        
        # 检查窗口功能
        search_window = step3_test_window_show_hide()
        
        if search_window is None:
            print("\n❌ 问题诊断: 窗口显示/隐藏功能异常")
            print("🛠️ 解决方案:")
            print("  1. 检查UI组件是否正确初始化")
            print("  2. 确认窗口管理器功能正常")
            print("  3. 检查Qt应用程序事件循环")
            return False
        
        print("\n✅ 基础组件检查通过")
        print("💡 建议进行完整的应用程序测试以验证热键功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 Simple Desktop - 全局热键功能调试")
    print("=" * 80)
    
    # 执行调试步骤
    steps = [
        ("检查热键相关组件", step1_check_hotkey_components),
        ("测试热键注册", step2_test_hotkey_registration),
        ("测试窗口显示/隐藏逻辑", step3_test_window_show_hide),
        ("测试热键与窗口的集成", step4_test_hotkey_integration),
        ("检查系统冲突", step5_check_system_conflicts),
    ]
    
    passed_steps = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            if step_func():
                passed_steps += 1
                print(f"✅ {step_name} 通过")
            else:
                print(f"❌ {step_name} 失败")
                break  # 如果某个步骤失败，停止后续测试
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
            break
    
    # 最终诊断
    step6_comprehensive_diagnosis()
    
    print("\n" + "=" * 80)
    print("🎯 热键功能调试完成！")
    print(f"通过步骤: {passed_steps}/{total_steps}")
    
    if passed_steps == total_steps:
        print("\n🎉 热键功能基础组件正常")
        print("如果热键仍然不工作，请检查:")
        print("1. 应用程序是否以正确的权限运行")
        print("2. 是否与其他全局热键应用程序冲突")
        print("3. 操作系统的热键权限设置")
    else:
        print(f"\n⚠️ 发现 {total_steps - passed_steps} 个问题，请根据上述诊断信息进行修复")

if __name__ == "__main__":
    main()
