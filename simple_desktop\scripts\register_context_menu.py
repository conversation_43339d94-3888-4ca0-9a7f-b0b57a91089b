"""
管理员权限的右键菜单注册脚本
"""

import sys
import os
import ctypes
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def is_admin():
    """检查是否具有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except Exception:
        return False


def run_as_admin():
    """以管理员权限重新运行脚本"""
    try:
        if is_admin():
            return True
        
        # 以管理员权限重新运行
        ctypes.windll.shell32.ShellExecuteW(
            None, 
            "runas", 
            sys.executable, 
            " ".join(sys.argv), 
            None, 
            1
        )
        return False
    except Exception as e:
        print(f"Failed to run as admin: {e}")
        return False


def register_menu():
    """注册右键菜单"""
    try:
        from simple_desktop.core.context_menu import context_menu_manager
        
        print("正在注册右键菜单...")
        success = context_menu_manager.register_context_menu()
        
        if success:
            print("✓ 右键菜单注册成功")
            return True
        else:
            print("✗ 右键菜单注册失败")
            return False
            
    except Exception as e:
        print(f"注册失败: {e}")
        return False


def unregister_menu():
    """注销右键菜单"""
    try:
        from simple_desktop.core.context_menu import context_menu_manager
        
        print("正在注销右键菜单...")
        success = context_menu_manager.unregister_context_menu()
        
        if success:
            print("✓ 右键菜单注销成功")
            return True
        else:
            print("✗ 右键菜单注销失败")
            return False
            
    except Exception as e:
        print(f"注销失败: {e}")
        return False


def main():
    """主函数"""
    print("SimpleDesktop 右键菜单注册工具")
    print("=" * 40)
    
    # 检查管理员权限
    if not is_admin():
        print("需要管理员权限来修改注册表")
        print("正在请求管理员权限...")
        
        if not run_as_admin():
            print("无法获取管理员权限")
            input("按回车键退出...")
            sys.exit(1)
        else:
            sys.exit(0)  # 重新启动后退出当前进程
    
    print("✓ 已获得管理员权限")
    
    # 解析命令行参数
    action = "register"  # 默认操作
    if len(sys.argv) > 1:
        action = sys.argv[1].lower()
    
    if action == "register":
        success = register_menu()
    elif action == "unregister":
        success = unregister_menu()
    else:
        print(f"未知操作: {action}")
        print("用法: register_context_menu.py [register|unregister]")
        success = False
    
    if success:
        print("\n操作完成！")
    else:
        print("\n操作失败！")
    
    input("按回车键退出...")
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
