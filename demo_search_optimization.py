#!/usr/bin/env python3
"""
演示Simple Desktop搜索优化效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_search_optimization():
    """演示搜索优化效果"""
    print("🚀 Simple Desktop - 搜索优化效果演示")
    print("=" * 80)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        engine = FileSearchEngine(profile_id=0)
        window = FloatingSearchWindow()
        
        print("🎯 优化内容总结:")
        print("1. 扩展快捷方式定义：.exe、.lnk、.url、.bat、.cmd、.msi等")
        print("2. 优化排序逻辑：快速启动文件 > 文件夹 > 普通文件")
        print("3. 聚焦扫描目录：只保留桌面和程序菜单相关目录")
        print("4. 完善文件类型分类：所有快速启动文件都在executables分类中")
        
        # 显示当前配置
        print(f"\n📁 当前扫描目录配置:")
        scan_dirs = engine.get_scan_directories()
        for i, dir_path in enumerate(scan_dirs):
            exists = "✅" if os.path.exists(dir_path) else "❌"
            print(f"  {i+1}. {exists} {dir_path}")
        
        print(f"\n⚙️ 快速启动文件类型:")
        executables = engine.file_type_extensions.get("executables", [])
        for ext in executables:
            print(f"  {ext}")
        
        # 演示排序效果
        print(f"\n🔄 排序逻辑演示:")
        
        # 创建示例结果
        from simple_desktop.search.engine import SearchResult
        
        demo_results = [
            SearchResult("重要文档.txt", "C:\\Users\\<USER>\\重要文档.txt", "file", suffix=".txt"),
            SearchResult("程序文件夹", "C:\\Program Files\\程序文件夹", "folder"),
            SearchResult("应用程序.exe", "C:\\Program Files\\应用程序.exe", "file", suffix=".exe"),
            SearchResult("快捷方式.lnk", "C:\\Users\\<USER>\\快捷方式.lnk", "file", suffix=".lnk"),
            SearchResult("网站链接.url", "C:\\Users\\<USER>\\网站链接.url", "file", suffix=".url"),
            SearchResult("批处理.bat", "C:\\Users\\<USER>\\批处理.bat", "file", suffix=".bat"),
            SearchResult("图片.jpg", "C:\\Users\\<USER>\\图片.jpg", "file", suffix=".jpg"),
        ]
        
        print("  排序前:")
        for i, result in enumerate(demo_results):
            print(f"    {i+1}. {result.filename} ({result.item_type}, {result.suffix})")
        
        sorted_results = window.sort_search_results(demo_results)
        
        print("  排序后:")
        for i, result in enumerate(sorted_results):
            quick_launch_extensions = {".lnk", ".exe", ".url", ".bat", ".cmd", ".com", ".scr", ".msi"}
            
            if result.suffix.lower() in quick_launch_extensions:
                priority = "🥇"
                category = "快速启动"
            elif result.item_type == "folder":
                priority = "🥈"
                category = "文件夹"
            else:
                priority = "🥉"
                category = "普通文件"
            
            print(f"    {i+1}. {priority} {result.filename} ({category})")
        
        # 演示图标映射
        print(f"\n🎨 文件类型图标映射:")
        icon_mapping = {
            ".lnk": "🔗 快捷方式",
            ".exe": "⚙️ 可执行程序",
            ".url": "🌐 网址快捷方式",
            ".bat": "📜 批处理文件",
            ".cmd": "📜 命令文件",
            ".msi": "📦 安装包",
            ".com": "⚙️ DOS可执行文件",
            ".scr": "🖥️ 屏幕保护程序",
        }
        
        for ext, description in icon_mapping.items():
            print(f"  {ext} -> {description}")
        
        print(f"\n💡 优化效果:")
        print("✅ 搜索应用程序时，所有可直接启动的文件都排在最前面")
        print("✅ 减少了文档和下载文件的干扰，聚焦于快速启动")
        print("✅ 直观的图标显示，快速识别文件类型")
        print("✅ 更精准的搜索范围，提升搜索效率")
        
        # 实际搜索演示
        print(f"\n🔍 实际搜索演示:")
        test_query = "微信"
        
        print(f"搜索'{test_query}':")
        results = engine.search(test_query, limit=5)
        
        if results:
            sorted_results = window.sort_search_results(results)
            
            for i, result in enumerate(sorted_results):
                quick_launch_extensions = {".lnk", ".exe", ".url", ".bat", ".cmd", ".com", ".scr", ".msi"}
                
                if result.suffix.lower() in quick_launch_extensions:
                    icon = "🚀"
                    priority = "快速启动"
                elif result.item_type == "folder":
                    icon = "📁"
                    priority = "文件夹"
                else:
                    icon = "📄"
                    priority = "普通文件"
                
                print(f"  {i+1}. {icon} {result.filename} ({priority})")
                print(f"     {result.filepath}")
        else:
            print("  未找到相关结果")
        
        print(f"\n🎉 搜索优化演示完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    demo_search_optimization()

if __name__ == "__main__":
    main()
