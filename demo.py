#!/usr/bin/env python3
"""
Simple Desktop 演示脚本
展示主要功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """演示主函数"""
    print("🚀 Simple Desktop 演示")
    print("=" * 50)
    
    # 检查Everything可用性
    print("1. 检查Everything可用性...")
    try:
        from simple_desktop.core.everything_sdk import is_everything_available
        if is_everything_available():
            print("   ✓ Everything可用，搜索功能正常")
        else:
            print("   ⚠️ Everything不可用，请确保Everything正在运行")
    except Exception as e:
        print(f"   ✗ 检查失败: {e}")
    
    # 显示配置信息
    print("\n2. 当前配置信息...")
    try:
        from simple_desktop.core.config import config_manager
        from simple_desktop.core.profile_manager import profile_manager
        
        current_profile = config_manager.get_current_profile()
        if current_profile:
            print(f"   当前Profile: {current_profile.name}")
            print(f"   扫描目录: {len(current_profile.scan_directories)} 个")
            print(f"   默认行为: {config_manager.get_default_action()}")
    except Exception as e:
        print(f"   ✗ 配置读取失败: {e}")
    
    # 演示搜索功能
    print("\n3. 演示搜索功能...")
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        if engine.is_everything_available():
            # 搜索常见文件
            results = engine.search("txt", limit=3)
            print(f"   搜索'txt'找到 {len(results)} 个结果:")
            for i, result in enumerate(results):
                print(f"     {i+1}. {result.filename} ({result.item_type})")
        else:
            print("   ⚠️ 搜索引擎不可用")
    except Exception as e:
        print(f"   ✗ 搜索演示失败: {e}")
    
    # 启动GUI演示
    print("\n4. 启动GUI演示...")
    try:
        from PySide6.QtWidgets import QApplication
        from simple_desktop.ui.search_window import FloatingSearchWindow
        
        app = QApplication(sys.argv)
        
        # 创建搜索窗口
        search_window = FloatingSearchWindow()
        
        print("   ✓ 搜索窗口已创建")
        print("   📝 使用说明:")
        print("     - 在搜索框中输入文件名")
        print("     - 使用后缀框过滤文件类型")
        print("     - 点击'类型'按钮选择文件类型")
        print("     - 点击'目录'按钮管理扫描目录")
        print("     - Tab键切换打开方式")
        print("     - ESC键关闭窗口")
        print("\n   🎯 窗口特性:")
        print("     - 独立浮动窗口，可被其他应用覆盖")
        print("     - 磨砂玻璃效果和圆角设计")
        print("     - 淡入淡出动画")
        print("     - 10个Profile标签支持")
        
        # 显示窗口
        search_window.show_window()
        
        print("\n   🚀 演示窗口已启动！")
        print("   按Ctrl+C退出演示")
        
        # 运行应用
        sys.exit(app.exec())
        
    except KeyboardInterrupt:
        print("\n   演示已退出")
    except Exception as e:
        print(f"   ✗ GUI演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
