"""
基于独立线程的热键管理器 - 完全复制成功测试的方法
"""

import time
import threading
import ctypes
import ctypes.wintypes as wintypes
from typing import Optional, Callable
from PySide6.QtCore import QObject, Signal

# Windows API常量
WM_HOTKEY = 0x0312
MOD_CONTROL = 0x0002
VK_CONTROL = 0x11
VK_0 = 0x30
DOUBLE_CLICK_INTERVAL = 0.5

class ThreadHotkeyManager(QObject):
    """基于独立线程的热键管理器 - 完全复制成功测试"""
    
    # Qt信号
    profile_switch_signal = Signal(int)
    toggle_window_signal = Signal()
    
    def __init__(self):
        """初始化热键管理器"""
        super().__init__()
        
        self.is_listening = False
        self.toggle_callback: Optional[Callable[[], None]] = None
        self.profile_switch_callback: Optional[Callable[[int], None]] = None
        
        # 双击Ctrl检测
        self.last_ctrl_press_time = 0
        self.ctrl_press_count = 0
        self.ctrl_check_thread = None
        self.stop_event = threading.Event()
        
        # Windows API函数
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
        # 注册的热键ID
        self.registered_hotkeys = set()
        
        # 独立的热键检测线程
        self.hotkey_thread = None
        
        # 连接信号到回调
        self.profile_switch_signal.connect(self._handle_profile_switch)
        self.toggle_window_signal.connect(self._handle_toggle_window)
        
        # 调试统计
        self.message_check_count = 0
        self.hotkey_event_count = 0
        
        print("[THREAD] 基于独立线程的热键管理器已初始化")
    
    def set_callback(self, callback: Callable[[], None]):
        """设置窗口切换回调"""
        self.toggle_callback = callback
        print("[THREAD] 窗口切换回调已设置")
    
    def set_profile_switch_callback(self, callback: Callable[[int], None]):
        """设置Profile切换回调"""
        self.profile_switch_callback = callback
        print("[THREAD] Profile切换回调已设置")
    
    def start_listening(self) -> bool:
        """开始监听快捷键"""
        if self.is_listening:
            return True
        
        try:
            print("[THREAD] 开始注册Profile热键...")
            
            # 注册热键
            successful_registrations = 0
            
            for i in range(10):
                # 使用新的ID范围
                hotkey_id = 9000 + i
                vk_code = VK_0 + i
                
                # 尝试注册热键
                result = self.user32.RegisterHotKey(None, hotkey_id, MOD_CONTROL, vk_code)
                if result:
                    self.registered_hotkeys.add(hotkey_id)
                    successful_registrations += 1
                    print(f"[THREAD] Ctrl+{i} 注册成功 (ID: {hotkey_id})")
                else:
                    error_code = ctypes.GetLastError()
                    print(f"[THREAD] Ctrl+{i} 注册失败 (错误: {error_code})")
            
            print(f"[THREAD] Profile热键注册完成: {successful_registrations}/10")
            
            if successful_registrations == 0:
                print("[ERROR] 没有成功注册任何Profile热键")
                return False
            
            # 启动双击Ctrl检测线程
            self.stop_event.clear()
            self.ctrl_check_thread = threading.Thread(target=self._ctrl_monitor_thread, daemon=True)
            self.ctrl_check_thread.start()
            
            # 启动独立的热键检测线程（完全复制成功测试的方法）
            self.hotkey_thread = threading.Thread(target=self._hotkey_detection_thread, daemon=True)
            self.hotkey_thread.start()
            
            self.is_listening = True
            print("[THREAD] 基于独立线程的热键监听已启动")
            return True
            
        except Exception as e:
            print(f"[ERROR] 热键监听启动失败: {e}")
            self.stop_listening()
            return False
    
    def stop_listening(self):
        """停止监听快捷键"""
        if not self.is_listening:
            return
        
        print("[THREAD] 停止热键监听...")
        self.is_listening = False
        self.stop_event.set()
        
        # 注销热键
        for hotkey_id in self.registered_hotkeys:
            self.user32.UnregisterHotKey(None, hotkey_id)
        self.registered_hotkeys.clear()
        
        # 等待线程结束
        if self.ctrl_check_thread and self.ctrl_check_thread.is_alive():
            self.ctrl_check_thread.join(timeout=1.0)
        
        if self.hotkey_thread and self.hotkey_thread.is_alive():
            self.hotkey_thread.join(timeout=2.0)
        
        print("[THREAD] 基于独立线程的热键监听已停止")
        print(f"[THREAD] 总计检查消息次数: {self.message_check_count}")
        print(f"[THREAD] 总计热键事件次数: {self.hotkey_event_count}")
    
    def _hotkey_detection_thread(self):
        """独立的热键检测线程 - 完全复制成功测试的逻辑"""
        print("[THREAD] 热键检测线程已启动")
        
        msg = wintypes.MSG()
        
        while self.is_listening:
            try:
                self.message_check_count += 1
                
                # 完全复制成功测试的消息检查逻辑
                result = self.user32.PeekMessageW(ctypes.byref(msg), None, 0, 0, 1)  # PM_REMOVE
                
                if result:
                    if msg.message == WM_HOTKEY:
                        hotkey_id = msg.wParam
                        
                        # 检查是否是我们注册的热键
                        if hotkey_id in self.registered_hotkeys:
                            self.hotkey_event_count += 1
                            current_time = time.strftime('%H:%M:%S', time.localtime())
                            
                            print(f"[THREAD] 🎯 热键触发成功! ID={hotkey_id} - 时间: {current_time}")
                            print(f"[THREAD] 这是第 {self.hotkey_event_count} 个热键事件")
                            
                            self._handle_thread_hotkey(hotkey_id)
                    
                    # 分发消息（与成功测试相同）
                    self.user32.TranslateMessage(ctypes.byref(msg))
                    self.user32.DispatchMessageW(ctypes.byref(msg))
                else:
                    # 没有消息，短暂休眠（与成功测试相同）
                    time.sleep(0.01)
                    
            except Exception as e:
                print(f"[ERROR] 热键检测线程错误: {e}")
                time.sleep(0.1)
        
        print("[THREAD] 热键检测线程已退出")
    
    def _handle_thread_hotkey(self, hotkey_id: int):
        """处理线程热键消息"""
        current_time = time.strftime('%H:%M:%S', time.localtime())
        
        print(f"[THREAD] 开始处理热键: ID={hotkey_id} - 时间: {current_time}")
        
        if 9000 <= hotkey_id <= 9009:
            # Profile切换快捷键 (Ctrl+0 到 Ctrl+9)
            profile_id = hotkey_id - 9000
            print(f"[THREAD] 热键触发: Ctrl+{profile_id} (ID: {hotkey_id}) - 时间: {current_time}")
            print(f"[THREAD] 准备发射Qt信号: profile_switch_signal({profile_id})")
            
            # 发射Qt信号
            try:
                self.profile_switch_signal.emit(profile_id)
                print(f"[THREAD] Qt信号发射成功: profile_switch_signal({profile_id})")
            except Exception as e:
                print(f"[ERROR] Qt信号发射失败: {e}")
        else:
            print(f"[THREAD] 未知热键ID: {hotkey_id}")
            print(f"[THREAD] 期望范围: 9000-9009，实际: {hotkey_id}")
    
    def _handle_profile_switch(self, profile_id: int):
        """处理Profile切换信号"""
        current_time = time.strftime('%H:%M:%S', time.localtime())
        print(f"[THREAD] Qt信号接收: profile_switch_signal({profile_id}) - 时间: {current_time}")
        print(f"[THREAD] 处理Profile切换信号: Profile {profile_id}")
        
        if self.profile_switch_callback:
            try:
                print(f"[THREAD] 回调函数存在，准备执行: switch_profile({profile_id})")
                
                # 执行回调
                result = self.profile_switch_callback(profile_id)
                
                print(f"[THREAD] 回调执行: switch_profile({profile_id}) - 时间: {current_time}")
                print(f"[THREAD] 回调返回值: {result}")
                print(f"[THREAD] Profile切换回调执行完成")
            except Exception as e:
                print(f"[ERROR] Profile切换回调错误: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("[ERROR] Profile切换回调未设置！")
    
    def _handle_toggle_window(self):
        """处理窗口切换信号"""
        if self.toggle_callback:
            try:
                self.toggle_callback()
                print("[THREAD] 窗口切换回调执行完成")
            except Exception as e:
                print(f"[ERROR] 窗口切换回调错误: {e}")
    
    def _ctrl_monitor_thread(self):
        """监控Ctrl键双击的线程"""
        last_state = False
        
        while not self.stop_event.is_set():
            try:
                # 检查Ctrl键状态
                ctrl_state = self.user32.GetAsyncKeyState(VK_CONTROL)
                is_pressed = bool(ctrl_state & 0x8000)
                
                # 边沿触发：检测从未按下到按下的转换
                if is_pressed and not last_state:
                    current_time = time.time()
                    
                    # 如果有之前的按键记录，检查双击
                    if self.ctrl_press_count > 0:
                        time_diff = current_time - self.last_ctrl_press_time
                        
                        if time_diff <= DOUBLE_CLICK_INTERVAL:
                            # 双击Ctrl检测到，发射Qt信号
                            self.toggle_window_signal.emit()
                            print("[THREAD] 双击Ctrl检测到")
                            
                            # 重置状态
                            self.ctrl_press_count = 0
                            self.last_ctrl_press_time = 0
                            last_state = is_pressed
                            continue
                    
                    # 记录新的按键
                    self.last_ctrl_press_time = current_time
                    self.ctrl_press_count = 1
                
                # 重置超时的计数
                current_time = time.time()
                if (self.ctrl_press_count > 0 and 
                    current_time - self.last_ctrl_press_time > DOUBLE_CLICK_INTERVAL):
                    self.ctrl_press_count = 0
                
                last_state = is_pressed
                
                # 短暂休眠
                time.sleep(0.05)
                
            except Exception as e:
                print(f"[ERROR] Ctrl监控错误: {e}")
                break

# 创建全局实例
hotkey_manager = ThreadHotkeyManager()
