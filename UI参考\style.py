"""UI 样式和主题模块"""

import platform
from typing import Optional
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt
from PySide6.QtGui import QPalette, QColor

# Windows 平台的毛玻璃效果（可选）
if platform.system() == "Windows":
    try:
        import ctypes
        from ctypes import wintypes

        # Windows API 常量
        DWM_BB_ENABLE = 0x00000001
        DWM_BB_BLURREGION = 0x00000002
        DWM_BB_TRANSITIONONMAXIMIZED = 0x00000004

        class DWM_BLURBEHIND(ctypes.Structure):
            _fields_ = [
                ("dwFlags", wintypes.DWORD),
                ("fEnable", wintypes.BOOL),
                ("hRgnBlur", wintypes.HRGN),
                ("fTransitionOnMaximized", wintypes.BOOL),
            ]

        dwmapi = ctypes.windll.dwmapi
        user32 = ctypes.windll.user32

        def enable_blur_behind(hwnd: int) -> bool:
            """为窗口启用模糊背景效果"""
            try:
                blur_behind = DWM_BLURBEHIND()
                blur_behind.dwFlags = DWM_BB_ENABLE
                blur_behind.fEnable = True
                blur_behind.hRgnBlur = None
                blur_behind.fTransitionOnMaximized = False

                result = dwmapi.DwmEnableBlurBehindWindow(
                    wintypes.HWND(hwnd), ctypes.byref(blur_behind)
                )
                return result == 0
            except Exception as e:
                print(f"启用毛玻璃效果失败: {e}")
                return False

    except ImportError:

        def enable_blur_behind(hwnd: int) -> bool:
            return False

else:

    def enable_blur_behind(hwnd: int) -> bool:
        return False


def get_system_accent_color() -> QColor:
    """获取系统主题色（Windows accent color 或默认蓝色）"""
    if platform.system() == "Windows":
        try:
            import winreg

            # 读取 Windows 注册表中的主题色
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\DWM"
            ) as key:
                accent_color, _ = winreg.QueryValueEx(key, "AccentColor")
                # 转换 DWORD 到 QColor (ABGR 格式)
                r = accent_color & 0xFF
                g = (accent_color >> 8) & 0xFF
                b = (accent_color >> 16) & 0xFF
                return QColor(r, g, b)
        except Exception:
            pass

    # 默认蓝色
    return QColor(100, 150, 255)


def is_dark_theme() -> bool:
    """检测是否为深色主题"""
    palette = QPalette()
    window_color = palette.color(QPalette.ColorRole.Window)
    # 如果窗口背景颜色较暗，则认为是深色主题
    return window_color.lightness() < 128


def get_white_theme_stylesheet() -> str:
    """获取白色主题样式表 - 简化版本"""
    accent = get_system_accent_color()

    # 简化的白色主题配色
    bg_color = "rgb(255, 255, 255)"
    border_color = "rgb(200, 200, 200)"
    border_focus = f"rgb({accent.red()}, {accent.green()}, {accent.blue()})"
    text_color = "rgb(60, 60, 60)"
    button_bg = "rgb(245, 245, 245)"
    button_hover = "rgb(235, 235, 235)"
    list_bg = "rgb(250, 250, 250)"
    item_hover = "rgb(240, 240, 255)"
    item_selected = f"rgb({accent.red()}, {accent.green()}, {accent.blue()})"

    return f"""
    /* 主窗口 */
    QWidget {{
        background-color: {bg_color};
        border-radius: 16px;
        color: {text_color};
        font-family: "Microsoft YaHei", sans-serif;
        border: 1px solid {border_color};
    }}

    /* 搜索输入框 */
    QLineEdit {{
        background-color: white;
        border: 2px solid {border_color};
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 13px;
        color: {text_color};
        selection-background-color: {item_selected};
        selection-color: white;
    }}

    QLineEdit:focus {{
        border: 2px solid {border_focus};
        outline: none;
    }}

    QLineEdit:hover {{
        border-color: {border_focus};
    }}

    /* 控制按钮 */
    QPushButton {{
        background-color: {button_bg};
        border: 1px solid {border_color};
        border-radius: 6px;
        color: {text_color};
        font-size: 11px;
        font-weight: 500;
    }}

    QPushButton:hover {{
        background-color: {button_hover};
        border-color: {border_focus};
    }}

    QPushButton:pressed {{
        background-color: rgb(220, 220, 220);
    }}

    /* 行为切换按钮 */
    QPushButton[objectName="action_toggle"] {{
        background-color: {item_selected};
        border-color: {item_selected};
        color: white;
        font-weight: 600;
    }}

    QPushButton[objectName="action_toggle"]:hover {{
        background-color: rgba({accent.red()}, {accent.green()}, {accent.blue()}, 0.8);
    }}

    /* 结果列表 */
    QListWidget {{
        background-color: {list_bg};
        border: 1px solid {border_color};
        border-radius: 12px;
        outline: none;
        padding: 6px;
    }}

    QListWidget::item {{
        background-color: white;
        padding: 12px;
        border-radius: 8px;
        margin: 3px;
        min-height: 40px;
        color: {text_color};
        border: 1px solid rgb(230, 230, 230);
        font-size: 13px;
    }}

    QListWidget::item:selected {{
        background-color: {item_selected};
        color: white;
        border-color: {item_selected};
    }}

    QListWidget::item:hover {{
        background-color: {item_hover};
        border-color: {border_focus};
    }}

    /* 滚动条 */
    QScrollBar:vertical {{
        background: rgb(240, 240, 240);
        width: 8px;
        border-radius: 4px;
        margin: 0;
    }}

    QScrollBar::handle:vertical {{
        background: rgb(180, 180, 180);
        border-radius: 4px;
        min-height: 20px;
    }}

    QScrollBar::handle:vertical:hover {{
        background: rgb(150, 150, 150);
    }}

    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        border: none;
        background: none;
        height: 0px;
    }}

    QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
        background: none;
    }}
    """


def get_dialog_stylesheet() -> str:
    """获取对话框样式表 - 现代化白色主题"""
    accent = get_system_accent_color()
    accent_rgba = f"rgba({accent.red()}, {accent.green()}, {accent.blue()}, 0.8)"

    # 统一使用白色主题
    bg_color = "rgb(255, 255, 255)"
    text_color = "rgb(50, 50, 50)"
    button_bg = "rgb(248, 250, 252)"
    button_hover = "rgb(240, 245, 250)"
    checkbox_bg = "rgb(255, 255, 255)"
    border_color = "rgb(220, 220, 220)"
    border_focus = f"rgb({accent.red()}, {accent.green()}, {accent.blue()})"

    return f"""
    QDialog {{
        background-color: {bg_color};
        border-radius: 16px;
        border: 2px solid {border_color};
        font-family: "Segoe UI", "Microsoft YaHei", "PingFang SC", sans-serif;
    }}

    QLabel {{
        color: {text_color};
        background: transparent;
        font-weight: 400;
    }}

    QCheckBox {{
        color: {text_color};
        background: transparent;
        spacing: 10px;
        padding: 6px;
        font-weight: 500;
    }}

    QCheckBox::indicator {{
        width: 18px;
        height: 18px;
        border-radius: 6px;
        border: 2px solid {border_color};
        background-color: {checkbox_bg};
    }}

    QCheckBox::indicator:checked {{
        background-color: {accent_rgba};
        border-color: {accent_rgba};
    }}

    QCheckBox::indicator:hover {{
        border-color: {border_focus};
    }}

    QCheckBox::indicator:checked:hover {{
        background-color: rgba({accent.red()}, {accent.green()}, {accent.blue()}, 0.9);
    }}

    QPushButton {{
        background-color: {button_bg};
        border: 2px solid {border_color};
        border-radius: 10px;
        color: {text_color};
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 600;
        min-width: 80px;
    }}

    QPushButton:hover {{
        background-color: {button_hover};
        border-color: {border_focus};
    }}

    QPushButton:pressed {{
        background-color: rgb(230, 238, 248);
        border-color: {border_focus};
    }}

    QScrollArea {{
        background: transparent;
        border: 2px solid {border_color};
        border-radius: 12px;
    }}

    QScrollBar:vertical {{
        background: rgba(240, 240, 240, 0.8);
        width: 10px;
        border-radius: 5px;
        margin: 2px;
    }}

    QScrollBar::handle:vertical {{
        background: rgba({accent.red()}, {accent.green()}, {accent.blue()}, 0.4);
        border-radius: 5px;
        min-height: 30px;
        margin: 1px;
    }}

    QScrollBar::handle:vertical:hover {{
        background: rgba({accent.red()}, {accent.green()}, {accent.blue()}, 0.6);
    }}
    """


# 资源路径（图标）
ICON_SEARCH = "🔍"  # 使用 Unicode emoji 作为临时图标
ICON_FOLDER = "📁"
ICON_DOCUMENT = "📄"
ICON_IMAGE = "🖼️"
ICON_VIDEO = "🎬"
ICON_AUDIO = "🎵"
ICON_ARCHIVE = "📦"
ICON_CODE = "💻"
ICON_EXECUTABLE = "⚙️"
ICON_DEFAULT = "📋"

# 文件类型图标映射
FILE_TYPE_ICONS = {
    # 文档
    ".txt": ICON_DOCUMENT,
    ".doc": ICON_DOCUMENT,
    ".docx": ICON_DOCUMENT,
    ".pdf": ICON_DOCUMENT,
    ".rtf": ICON_DOCUMENT,
    ".odt": ICON_DOCUMENT,
    # 图片
    ".png": ICON_IMAGE,
    ".jpg": ICON_IMAGE,
    ".jpeg": ICON_IMAGE,
    ".gif": ICON_IMAGE,
    ".bmp": ICON_IMAGE,
    ".svg": ICON_IMAGE,
    ".webp": ICON_IMAGE,
    ".ico": ICON_IMAGE,
    # 视频
    ".mp4": ICON_VIDEO,
    ".avi": ICON_VIDEO,
    ".mkv": ICON_VIDEO,
    ".mov": ICON_VIDEO,
    ".wmv": ICON_VIDEO,
    ".flv": ICON_VIDEO,
    ".webm": ICON_VIDEO,
    # 音频
    ".mp3": ICON_AUDIO,
    ".wav": ICON_AUDIO,
    ".flac": ICON_AUDIO,
    ".aac": ICON_AUDIO,
    ".ogg": ICON_AUDIO,
    ".wma": ICON_AUDIO,
    # 压缩包
    ".zip": ICON_ARCHIVE,
    ".rar": ICON_ARCHIVE,
    ".7z": ICON_ARCHIVE,
    ".tar": ICON_ARCHIVE,
    ".gz": ICON_ARCHIVE,
    # 代码
    ".py": ICON_CODE,
    ".js": ICON_CODE,
    ".html": ICON_CODE,
    ".css": ICON_CODE,
    ".cpp": ICON_CODE,
    ".c": ICON_CODE,
    ".java": ICON_CODE,
    ".php": ICON_CODE,
    ".rb": ICON_CODE,
    ".go": ICON_CODE,
    ".rs": ICON_CODE,
    ".swift": ICON_CODE,
    # 可执行文件
    ".exe": ICON_EXECUTABLE,
    ".msi": ICON_EXECUTABLE,
    ".app": ICON_EXECUTABLE,
    ".deb": ICON_EXECUTABLE,
    ".rpm": ICON_EXECUTABLE,
}


def get_file_icon(file_extension: str) -> str:
    """根据文件扩展名获取对应图标"""
    return FILE_TYPE_ICONS.get(file_extension.lower(), ICON_DEFAULT)
