#!/usr/bin/env python3
"""
调试微信快捷方式搜索问题
"""

import sys
import os
import glob
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def step1_verify_file_existence():
    """步骤1：验证文件存在性"""
    print("=" * 60)
    print("步骤1：验证桌面上的微信相关文件")
    print("=" * 60)
    
    desktop_path = Path.home() / "Desktop"
    print(f"桌面路径: {desktop_path}")
    print(f"桌面路径存在: {desktop_path.exists()}")
    
    if not desktop_path.exists():
        print("❌ 桌面路径不存在！")
        return []
    
    # 查找所有.lnk文件
    lnk_files = list(desktop_path.glob("*.lnk"))
    print(f"\n桌面上的.lnk文件数量: {len(lnk_files)}")
    
    weixin_files = []
    
    print("\n所有.lnk文件:")
    for i, lnk_file in enumerate(lnk_files):
        print(f"  {i+1}. {lnk_file.name}")
        # 检查是否包含微信相关关键词
        filename_lower = lnk_file.name.lower()
        if any(keyword in filename_lower for keyword in ['weixin', '微信', 'wechat']):
            weixin_files.append(lnk_file)
            print(f"      ✅ 微信相关文件: {lnk_file}")
    
    print(f"\n找到的微信相关文件数量: {len(weixin_files)}")
    for wf in weixin_files:
        print(f"  - {wf.name} -> {wf}")
    
    return weixin_files

def step2_test_everything_sdk(weixin_files):
    """步骤2：测试Everything SDK"""
    print("\n" + "=" * 60)
    print("步骤2：测试Everything SDK直接搜索")
    print("=" * 60)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试不同的搜索关键词
        test_keywords = ["weixin", "微信", "wechat", ".lnk", "*.lnk"]
        
        for keyword in test_keywords:
            print(f"\n🔍 搜索关键词: '{keyword}'")
            try:
                results = sdk.search(keyword, max_results=10)
                print(f"   结果数量: {len(results)}")
                
                # 显示前5个结果
                for i, result in enumerate(results[:5]):
                    print(f"     {i+1}. {result.filename}")
                    print(f"        路径: {result.full_path}")
                    
                    # 检查是否是我们要找的微信文件
                    for wf in weixin_files:
                        if str(wf) == result.full_path:
                            print(f"        ✅ 匹配桌面微信文件!")
                
            except Exception as e:
                print(f"   ❌ 搜索失败: {e}")
        
        # 测试桌面路径限制搜索
        desktop_path = Path.home() / "Desktop"
        print(f"\n🔍 桌面路径限制搜索: '{desktop_path}'")
        
        desktop_query = f'"{desktop_path}\\" weixin'
        print(f"   查询语句: {desktop_query}")
        
        try:
            results = sdk.search(desktop_query, max_results=10)
            print(f"   结果数量: {len(results)}")
            
            for i, result in enumerate(results):
                print(f"     {i+1}. {result.filename} -> {result.full_path}")
                
        except Exception as e:
            print(f"   ❌ 桌面路径搜索失败: {e}")
        
        print("\n✅ Everything SDK测试完成")
        
    except Exception as e:
        print(f"❌ Everything SDK测试失败: {e}")
        import traceback
        traceback.print_exc()

def step3_check_profile_config():
    """步骤3：检查Profile配置"""
    print("\n" + "=" * 60)
    print("步骤3：检查Profile配置")
    print("=" * 60)
    
    try:
        from simple_desktop.core.profile_manager import profile_manager
        from simple_desktop.search.engine import FileSearchEngine
        
        # 检查Profile 0配置
        print("Profile 0配置:")
        from simple_desktop.core.config import config_manager
        profile = config_manager.get_profile(0)
        if profile:
            print(f"  名称: {profile.name}")
            print(f"  扫描目录数量: {len(profile.scan_directories)}")
            
            desktop_path = str(Path.home() / "Desktop")
            print(f"  期望桌面路径: {desktop_path}")
            
            for i, scan_dir in enumerate(profile.scan_directories):
                print(f"    {i+1}. {scan_dir}")
                if scan_dir.lower() == desktop_path.lower():
                    print(f"       ✅ 匹配桌面路径!")
                elif "desktop" in scan_dir.lower():
                    print(f"       ⚠️ 包含desktop但路径不完全匹配")
        
        # 检查搜索引擎配置
        engine = FileSearchEngine(profile_id=0)
        scan_dirs = engine.get_scan_directories()
        scan_info = engine.get_scan_directories_info()
        
        print(f"\n搜索引擎配置:")
        print(f"  扫描目录数量: {len(scan_dirs)}")
        print(f"  有效目录数量: {len(scan_info['valid_directories'])}")
        print(f"  无效目录数量: {len(scan_info['invalid_directories'])}")
        
        if scan_info['invalid_directories']:
            print("  无效目录:")
            for invalid_dir in scan_info['invalid_directories']:
                print(f"    - {invalid_dir}")
        
        print("\n✅ Profile配置检查完成")
        
    except Exception as e:
        print(f"❌ Profile配置检查失败: {e}")
        import traceback
        traceback.print_exc()

def step4_test_search_queries():
    """步骤4：测试搜索查询"""
    print("\n" + "=" * 60)
    print("步骤4：测试搜索查询构建")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        scan_dirs = engine.get_scan_directories()
        
        # 测试不同的查询构建
        test_queries = ["weixin", "微信", "wechat"]
        
        for query in test_queries:
            print(f"\n🔍 测试查询: '{query}'")
            
            # 构建Everything查询
            everything_query = engine._build_everything_query_with_directories(
                query, scan_dirs, file_types=None, include_folders=True
            )
            print(f"   构建的查询: {everything_query}")
            
            # 执行搜索
            try:
                results = engine.search(query, limit=10)
                print(f"   搜索结果数量: {len(results)}")
                
                for i, result in enumerate(results):
                    print(f"     {i+1}. {result.filename} ({result.item_type})")
                    print(f"        路径: {result.filepath}")
                    print(f"        后缀: {result.suffix}")
                    
                    if result.suffix.lower() == ".lnk":
                        print(f"        ✅ 快捷方式文件!")
                
            except Exception as e:
                print(f"   ❌ 搜索执行失败: {e}")
        
        print("\n✅ 搜索查询测试完成")
        
    except Exception as e:
        print(f"❌ 搜索查询测试失败: {e}")
        import traceback
        traceback.print_exc()

def step5_check_file_type_filtering():
    """步骤5：检查文件类型过滤"""
    print("\n" + "=" * 60)
    print("步骤5：检查文件类型过滤")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 检查.lnk文件是否在任何文件类型分类中
        file_types = engine.file_type_extensions
        
        print("文件类型分类:")
        lnk_found_in_category = False
        
        for category, extensions in file_types.items():
            print(f"  {category}: {extensions}")
            if ".lnk" in extensions:
                print(f"    ✅ .lnk文件在'{category}'分类中")
                lnk_found_in_category = True
        
        if not lnk_found_in_category:
            print("  ⚠️ .lnk文件不在任何预定义分类中")
        
        # 测试不同文件类型过滤的搜索
        print(f"\n测试文件类型过滤搜索:")
        
        # 无过滤搜索
        print("  1. 无文件类型过滤:")
        results1 = engine.search("weixin", limit=5, file_types=None)
        print(f"     结果数量: {len(results1)}")
        
        # 只搜索可执行文件（包含.lnk）
        print("  2. 只搜索可执行文件:")
        results2 = engine.search("weixin", limit=5, file_types=["executables"])
        print(f"     结果数量: {len(results2)}")
        
        # 搜索所有类型
        print("  3. 搜索所有文件类型:")
        all_types = list(file_types.keys()) + ["folders"]
        results3 = engine.search("weixin", limit=5, file_types=all_types)
        print(f"     结果数量: {len(results3)}")
        
        print("\n✅ 文件类型过滤检查完成")
        
    except Exception as e:
        print(f"❌ 文件类型过滤检查失败: {e}")
        import traceback
        traceback.print_exc()

def step6_comprehensive_test():
    """步骤6：综合测试"""
    print("\n" + "=" * 60)
    print("步骤6：综合测试和解决方案")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        # 直接测试Everything SDK能否找到微信
        sdk = get_everything_sdk()
        
        print("🔍 Everything SDK直接搜索测试:")
        direct_results = sdk.search("weixin", max_results=20)
        print(f"   'weixin'搜索结果: {len(direct_results)}")
        
        desktop_path = Path.home() / "Desktop"
        desktop_weixin_files = []
        
        for result in direct_results:
            if str(desktop_path).lower() in result.full_path.lower():
                desktop_weixin_files.append(result)
                print(f"   ✅ 桌面微信文件: {result.filename} -> {result.full_path}")
        
        if not desktop_weixin_files:
            print("   ⚠️ Everything SDK在桌面未找到微信相关文件")
            
            # 尝试更广泛的搜索
            print("\n🔍 尝试更广泛的搜索:")
            broader_results = sdk.search("微信", max_results=20)
            print(f"   '微信'搜索结果: {len(broader_results)}")
            
            for result in broader_results[:5]:
                print(f"     {result.filename} -> {result.full_path}")
        
        # 测试搜索引擎
        print(f"\n🔍 搜索引擎测试:")
        engine = FileSearchEngine(profile_id=0)
        
        engine_results = engine.search("weixin", limit=10)
        print(f"   搜索引擎'weixin'结果: {len(engine_results)}")
        
        for result in engine_results:
            print(f"     {result.filename} -> {result.filepath}")
        
        # 提供解决方案
        print(f"\n💡 问题分析和解决方案:")
        
        if not desktop_weixin_files:
            print("1. ❌ Everything SDK在桌面未找到微信文件")
            print("   可能原因:")
            print("   - 微信快捷方式文件名不包含'weixin'关键词")
            print("   - 文件可能使用中文名称")
            print("   - Everything索引可能未包含该文件")
            print("   解决方案:")
            print("   - 尝试搜索'微信'、'wechat'等其他关键词")
            print("   - 检查实际的快捷方式文件名")
            print("   - 重建Everything索引")
        else:
            print("1. ✅ Everything SDK能找到桌面微信文件")
            
            if not engine_results:
                print("2. ❌ 搜索引擎无法找到文件")
                print("   可能原因:")
                print("   - 路径过滤逻辑问题")
                print("   - 查询构建问题")
                print("   - 文件类型过滤问题")
            else:
                print("2. ✅ 搜索引擎能找到文件")
        
        print("\n✅ 综合测试完成")
        
    except Exception as e:
        print(f"❌ 综合测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 Simple Desktop - 微信快捷方式搜索问题调试")
    print("=" * 80)
    
    # 步骤1：验证文件存在性
    weixin_files = step1_verify_file_existence()
    
    # 步骤2：测试Everything SDK
    step2_test_everything_sdk(weixin_files)
    
    # 步骤3：检查Profile配置
    step3_check_profile_config()
    
    # 步骤4：测试搜索查询
    step4_test_search_queries()
    
    # 步骤5：检查文件类型过滤
    step5_check_file_type_filtering()
    
    # 步骤6：综合测试
    step6_comprehensive_test()
    
    print("\n" + "=" * 80)
    print("🎯 调试完成！请查看上述输出以确定问题原因。")

if __name__ == "__main__":
    main()
