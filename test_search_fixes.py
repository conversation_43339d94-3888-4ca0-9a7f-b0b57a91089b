"""
测试搜索修复的效果
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_suffix_filter_fix():
    """测试后缀筛选修复"""
    print("🔧 测试后缀筛选修复")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试1: 后缀筛选应该只返回指定扩展名的文件
        print("📝 测试1: 后缀筛选 - 微信 .lnk")
        results = engine.search("微信 ext:lnk", limit=10)
        print(f"结果数: {len(results)}")
        
        lnk_count = 0
        other_count = 0
        for result in results:
            if result.suffix == ".lnk":
                lnk_count += 1
                print(f"  ✅ {result.filename} ({result.suffix})")
            else:
                other_count += 1
                print(f"  ❌ {result.filename} ({result.suffix}) - 不应该出现")
        
        print(f"总结: .lnk文件 {lnk_count} 个, 其他文件 {other_count} 个")
        if other_count == 0:
            print("✅ 后缀筛选工作正常")
        else:
            print("❌ 后缀筛选仍有问题")
        
        # 测试2: search_with_suffix方法
        print("\n📝 测试2: search_with_suffix方法")
        results = engine.search_with_suffix("微信", ".lnk", limit=10)
        print(f"结果数: {len(results)}")
        
        all_lnk = all(result.suffix == ".lnk" for result in results)
        print(f"所有结果都是.lnk文件: {'✅' if all_lnk else '❌'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_mun_search_fix():
    """测试MUN文件搜索修复"""
    print("\n\n🔧 测试MUN文件搜索修复")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试1: 搜索"MUN"应该能找到MUN.md文件
        print("📝 测试1: 搜索'MUN'")
        results = engine.search("MUN", limit=20)
        print(f"结果数: {len(results)}")
        
        found_md = False
        found_folder = False
        for result in results:
            print(f"  {result.filename} ({result.item_type}) - {result.filepath}")
            if "MUN.md" in result.filename:
                found_md = True
                print("    ✅ 找到MUN.md文件!")
            if result.filename == "MUN" and result.item_type == "folder":
                found_folder = True
        
        print(f"找到MUN.md文件: {'✅' if found_md else '❌'}")
        print(f"找到MUN文件夹: {'✅' if found_folder else '❌'}")
        
        # 测试2: 测试增强查询构建
        print("\n📝 测试2: 增强查询构建")
        enhanced_query = engine._build_enhanced_query("MUN")
        print(f"增强查询: {enhanced_query}")
        
        # 直接用增强查询测试Everything SDK
        print("\n📝 测试3: Everything SDK增强查询")
        everything_results = engine.everything_sdk.search(
            query=enhanced_query,
            max_results=50,
            match_case=False,
            match_whole_word=False,
            use_regex=False
        )
        print(f"Everything SDK结果数: {len(everything_results)}")
        
        sdk_found_md = False
        for result in everything_results:
            if "MUN" in result.filename or "MUN" in result.full_path:
                print(f"  {result.filename} - {result.full_path}")
                if "MUN.md" in result.filename:
                    sdk_found_md = True
                    print("    ✅ Everything SDK找到MUN.md!")
        
        print(f"Everything SDK找到MUN.md: {'✅' if sdk_found_md else '❌'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_ui_integration_fix():
    """测试UI集成修复"""
    print("\n\n🔧 测试UI集成修复")
    print("=" * 50)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        
        engine = FileSearchEngine(profile_id=0)
        
        # 模拟UI中的各种搜索场景
        test_cases = [
            ("微信", ".lnk", "后缀筛选"),
            ("MUN", ".md", "文件名+后缀"),
            ("MUN", "", "纯文件名搜索"),
        ]
        
        for query, suffix, description in test_cases:
            print(f"\n📝 测试: {description}")
            print(f"查询: '{query}', 后缀: '{suffix}'")
            
            # 模拟UI逻辑
            if query:
                search_query = query
                if suffix:
                    if not suffix.startswith("."):
                        suffix = f".{suffix}"
                    search_query = f"{query} ext:{suffix[1:]}"
            
            print(f"构建的查询: '{search_query}'")
            
            results = engine.search(search_query, limit=10)
            print(f"结果数: {len(results)}")
            
            for i, result in enumerate(results[:5]):
                print(f"  {i+1}. {result.filename} ({result.suffix}) - {result.item_type}")
            
            # 验证结果
            if suffix:
                correct_suffix = all(result.suffix == suffix or result.item_type == "folder" for result in results)
                print(f"后缀过滤正确: {'✅' if correct_suffix else '❌'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("🚀 测试搜索修复效果")
    print("=" * 80)
    
    test_suffix_filter_fix()
    test_mun_search_fix()
    test_ui_integration_fix()
    
    print("\n" + "=" * 80)
    print("✅ 修复测试完成")


if __name__ == "__main__":
    main()
