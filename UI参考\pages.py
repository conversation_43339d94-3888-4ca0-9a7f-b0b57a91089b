"""页面式对话框模块 - 简化版本"""

import os
from PySide6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QCheckBox,
    QLabel,
    QPushButton,
    QGroupBox,
    QScrollArea,
    QWidget,
    QFileDialog,
    QButtonGroup,
    QRadioButton,
    QSpacerItem,
    QSizePolicy,
    QProgressDialog,
    QFrame,
    QMessageBox,
)
from PySide6.QtCore import Qt, Signal, QThread, QSize
from PySide6.QtGui import QFont

from src.core.config import config_manager


class IndexWorker(QThread):
    """后台索引构建线程"""

    progress = Signal(int)  # 已扫描文件数
    finished = Signal(dict)  # 构建结果

    def __init__(self, directory: str):
        super().__init__()
        self.directory = directory

    def run(self):
        # 在后台执行文件扫描
        from src.indexer.scanner import FileScanner  # 延迟导入避免循环

        scanner = FileScanner()

        def callback(count: int):
            self.progress.emit(count)

        result = scanner.build_index(self.directory, progress_callback=callback)
        self.finished.emit(result)


class FileTypeFilterPage(QDialog):
    """文件类型筛选对话框 - 简化版本"""

    # 自定义信号，传递选中的文件类型
    filter_changed = Signal(list)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("文件类型筛选")
        self.setFixedSize(350, 400)
        self.setWindowFlags(Qt.WindowType.Dialog)

        # 获取当前文件类型配置
        self.file_types = config_manager.get_file_types()
        self.selected_categories = []  # 默认全不选

        self.init_ui()
        self.setup_style()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("选择文件类型")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 快速操作按钮
        quick_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all)
        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(self.select_none)

        quick_layout.addWidget(select_all_btn)
        quick_layout.addWidget(select_none_btn)
        quick_layout.addStretch()
        layout.addLayout(quick_layout)

        # 文件类型列表
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(8)

        # 文件类型复选框
        self.checkboxes = {}

        # 定义类型显示名称
        type_names = {
            "folders": "📁 文件夹",
            "documents": "📄 文档",
            "images": "🖼️ 图片",
            "videos": "🎬 视频",
            "audios": "🎵 音频",
            "executables": "⚙️ 可执行文件",
            "archives": "📦 压缩包",
            "code": "💻 代码文件",
            "shortcuts": "🔗 快捷方式",
        }

        for category, extensions in self.file_types.items():
            checkbox = QCheckBox(type_names.get(category, category.capitalize()))
            checkbox.setChecked(False)  # 默认全不选
            checkbox.setFont(QFont("Microsoft YaHei", 11))

            # 添加扩展名提示
            if category == "folders":
                checkbox.setToolTip("显示文件夹类型的搜索结果")
            else:
                ext_text = ", ".join(extensions[:5])
                if len(extensions) > 5:
                    ext_text += f" 等 {len(extensions)} 种"
                checkbox.setToolTip(f"包含: {ext_text}")

            checkbox.stateChanged.connect(self.on_checkbox_changed)
            self.checkboxes[category] = checkbox
            scroll_layout.addWidget(checkbox)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)
        layout.addWidget(scroll_area)

        # 按钮区域
        button_layout = QHBoxLayout()

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.apply_filter)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.close)

        button_layout.addStretch()
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(
            """
            QDialog {
                background-color: white;
                border-radius: 8px;
                border: 1px solid rgb(200, 200, 200);
            }
            QLabel {
                color: rgb(60, 60, 60);
                background: transparent;
            }
            QCheckBox {
                color: rgb(60, 60, 60);
                background: transparent;
                spacing: 8px;
                padding: 4px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid rgb(180, 180, 180);
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: rgb(100, 150, 255);
                border-color: rgb(100, 150, 255);
            }
            QPushButton {
                background-color: rgb(245, 245, 245);
                border: 1px solid rgb(200, 200, 200);
                border-radius: 4px;
                color: rgb(60, 60, 60);
                padding: 6px 12px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: rgb(235, 235, 235);
            }
            QScrollArea {
                background: transparent;
                border: 1px solid rgb(200, 200, 200);
                border-radius: 4px;
            }
        """
        )

    def on_checkbox_changed(self):
        """复选框状态改变"""
        self.selected_categories = [
            category
            for category, checkbox in self.checkboxes.items()
            if checkbox.isChecked()
        ]
        # 实时发送信号
        self.filter_changed.emit(self.selected_categories)

    def select_all(self):
        """全选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(True)

    def select_none(self):
        """全不选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)

    def apply_filter(self):
        """应用筛选"""
        self.filter_changed.emit(self.selected_categories)
        self.close()

    def get_selected_categories(self):
        """获取选中的类别"""
        return self.selected_categories


class PathManagementPage(QDialog):
    """路径管理对话框 - 简化版本"""

    # 信号：路径被添加
    path_added = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("管理扫描目录")
        self.setFixedSize(500, 400)
        self.setWindowFlags(Qt.WindowType.Dialog)

        self.init_ui()
        self.load_current_paths()
        self.setup_style()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("扫描目录管理")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 添加目录按钮
        add_btn = QPushButton("添加目录")
        add_btn.clicked.connect(self.add_directory)
        layout.addWidget(add_btn)

        # 当前目录列表
        current_group = QGroupBox("当前扫描目录")
        current_layout = QVBoxLayout(current_group)

        self.scroll_area = QScrollArea()
        self.path_list_widget = QWidget()
        self.path_list_layout = QVBoxLayout(self.path_list_widget)

        self.scroll_area.setWidget(self.path_list_widget)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setMinimumHeight(200)

        current_layout.addWidget(self.scroll_area)
        layout.addWidget(current_group)

        # 关闭按钮
        button_layout = QHBoxLayout()
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)

        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(
            """
            QDialog {
                background-color: white;
                border-radius: 8px;
                border: 1px solid rgb(200, 200, 200);
            }
            QLabel {
                color: rgb(60, 60, 60);
                background: transparent;
            }
            QPushButton {
                background-color: rgb(245, 245, 245);
                border: 1px solid rgb(200, 200, 200);
                border-radius: 4px;
                color: rgb(60, 60, 60);
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: rgb(235, 235, 235);
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid rgb(200, 200, 200);
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QScrollArea {
                background: transparent;
                border: none;
            }
        """
        )

    def load_current_paths(self):
        """加载当前扫描路径"""
        # 清空现有项目
        while self.path_list_layout.count():
            child = self.path_list_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 添加当前路径
        current_paths = config_manager.get_scan_directories()

        if not current_paths:
            no_path_label = QLabel("暂无扫描目录，请添加目录以启用文件监听")
            no_path_label.setStyleSheet(
                "color: gray; font-style: italic; padding: 20px;"
            )
            no_path_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.path_list_layout.addWidget(no_path_label)
        else:
            for path in current_paths:
                self.add_path_item(path)

        # 添加弹性空间
        self.path_list_layout.addStretch()

    def add_path_item(self, path: str):
        """添加路径项目"""
        item_widget = QWidget()
        item_layout = QHBoxLayout(item_widget)
        item_layout.setContentsMargins(10, 5, 10, 5)

        # 路径标签
        path_label = QLabel(path)
        path_label.setWordWrap(True)

        # 删除按钮
        remove_btn = QPushButton("删除")
        remove_btn.setMaximumWidth(60)
        remove_btn.clicked.connect(lambda: self.remove_directory(path))

        item_layout.addWidget(path_label, 1)
        item_layout.addWidget(remove_btn)

        # 设置样式
        item_widget.setStyleSheet(
            """
            QWidget {
                background-color: rgb(248, 248, 248);
                border: 1px solid rgb(220, 220, 220);
                border-radius: 4px;
                margin: 2px;
            }
        """
        )

        self.path_list_layout.addWidget(item_widget)

    def add_directory(self):
        """添加目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择要扫描的目录", "", QFileDialog.Option.ShowDirsOnly
        )

        if directory:
            success = config_manager.add_scan_directory(directory)
            if success:
                # 显示进度对话框
                progress_dialog = QProgressDialog("正在扫描目录，请稍候...", None, 0, 0, self)
                progress_dialog.setWindowTitle("构建索引")
                progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                progress_dialog.setCancelButton(None)
                progress_dialog.show()

                # 启动后台线程
                worker = IndexWorker(directory)

                # 把进度条对象和线程保存到实例，防止被垃圾回收
                self._current_worker = worker
                self._progress_dialog = progress_dialog

                # 连接信号
                worker.progress.connect(
                    lambda c: progress_dialog.setLabelText(f"已扫描 {c} 个文件...")
                )

                def on_finished(result: dict):
                    progress_dialog.close()
                    self._current_worker = None
                    self._progress_dialog = None
                    # 通知外部路径已添加并完成索引
                    self.path_added.emit(directory)
                    self.load_current_paths()

                worker.finished.connect(on_finished)
                worker.start()

    def remove_directory(self, path: str):
        """删除目录"""
        # 确认删除操作
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除扫描目录吗？\n\n目录: {path}\n\n此操作将从索引中删除该目录下的所有文件记录，且无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            success = config_manager.remove_scan_directory(path)
            if success:
                self.load_current_paths()
                # 显示成功消息
                QMessageBox.information(
                    self, "删除成功", f"目录已成功移除：\n{path}\n\n相关的文件记录也已从索引中清理。"
                )
