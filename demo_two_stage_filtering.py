#!/usr/bin/env python3
"""
两阶段过滤功能演示脚本
展示新的搜索逻辑的实际效果
"""

import sys
import os
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_raw_vs_filtered_search():
    """演示原始搜索 vs 过滤后搜索的对比"""
    print("🔍 演示两阶段过滤效果")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        # 检查Everything是否可用
        sdk = get_everything_sdk()
        if not sdk.is_everything_running():
            print("❌ Everything未运行，请先启动Everything")
            return
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试查询
        query = "python"
        print(f"搜索关键词: '{query}'")
        print()
        
        # 阶段1：原始Everything搜索
        print("📊 阶段1：原始Everything SDK搜索")
        start_time = time.time()
        
        raw_results = sdk.search(
            query=query,
            max_results=100,
            match_case=False,
            match_whole_word=False,
            use_regex=False
        )
        
        stage1_time = time.time() - start_time
        print(f"   原始结果数量: {len(raw_results)}")
        print(f"   搜索时间: {stage1_time:.3f}秒")
        
        # 显示前5个原始结果
        print("   前5个原始结果:")
        for i, result in enumerate(raw_results[:5]):
            print(f"     {i+1}. {result.filename}")
            print(f"        路径: {result.full_path}")
            print(f"        类型: {'文件夹' if result.is_folder else '文件'}")
        print()
        
        # 阶段2：应用过滤器
        print("🔧 阶段2：应用程序化过滤")
        
        # 测试不同的过滤条件
        filter_tests = [
            {
                "name": "只要Python文件(.py)",
                "criteria": FilterCriteria(
                    extensions={".py"},
                    include_folders=False
                )
            },
            {
                "name": "文档类型文件",
                "criteria": FilterCriteria(
                    file_types=["documents"],
                    include_folders=False
                )
            },
            {
                "name": "包含文件夹",
                "criteria": FilterCriteria(
                    include_folders=True
                )
            }
        ]
        
        for test in filter_tests:
            print(f"   🎯 {test['name']}:")
            start_time = time.time()
            
            filtered_results = engine._search_with_filters(query, test['criteria'], limit=20)
            
            filter_time = time.time() - start_time
            print(f"      过滤后结果数量: {len(filtered_results)}")
            print(f"      总处理时间: {filter_time:.3f}秒")
            
            # 显示前3个过滤后的结果
            if filtered_results:
                print("      前3个结果:")
                for i, result in enumerate(filtered_results[:3]):
                    print(f"        {i+1}. {result.filename} ({result.suffix})")
            else:
                print("      没有匹配的结果")
            print()
        
        print("✅ 两阶段过滤演示完成")
        print()
        print("📝 总结:")
        print("   - 阶段1：向Everything发送简单关键词，获取大量原始结果")
        print("   - 阶段2：在应用程序中应用复杂过滤条件")
        print("   - 优势：避免查询长度限制，支持复杂的组合过滤")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def demo_complex_filtering():
    """演示复杂过滤条件的组合"""
    print("\n🎛️ 演示复杂过滤条件组合")
    print("=" * 60)
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        
        engine = FileSearchEngine(profile_id=0)
        
        # 复杂过滤条件：在特定目录下查找特定类型的文件
        user_home = str(Path.home())
        
        criteria = FilterCriteria(
            scan_directories=[user_home],  # 只在用户目录下搜索
            file_types=["documents", "images"],  # 只要文档和图片
            include_folders=False,  # 不包含文件夹
            # 可以添加更多条件...
        )
        
        query = "test"
        print(f"搜索条件:")
        print(f"   关键词: '{query}'")
        print(f"   目录限制: {user_home}")
        print(f"   文件类型: 文档和图片")
        print(f"   包含文件夹: 否")
        print()
        
        start_time = time.time()
        results = engine._search_with_filters(query, criteria, limit=10)
        total_time = time.time() - start_time
        
        print(f"🎯 复杂过滤结果:")
        print(f"   结果数量: {len(results)}")
        print(f"   处理时间: {total_time:.3f}秒")
        
        if results:
            print("   匹配的文件:")
            for i, result in enumerate(results):
                print(f"     {i+1}. {result.filename}")
                print(f"        路径: {result.filepath}")
                print(f"        类型: {result.item_type}")
                print(f"        扩展名: {result.suffix}")
        else:
            print("   没有找到匹配的文件")
        
        print("\n✅ 复杂过滤演示完成")
        
    except Exception as e:
        print(f"❌ 复杂过滤演示失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主演示函数"""
    print("🚀 两阶段过滤功能演示")
    print("展示新的搜索架构如何工作")
    print()
    
    demo_raw_vs_filtered_search()
    demo_complex_filtering()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("新的两阶段过滤方法已成功实现：")
    print("✅ 阶段1：简化的Everything查询，避免查询长度限制")
    print("✅ 阶段2：强大的程序化过滤，支持复杂条件组合")
    print("✅ 高性能：智能的搜索倍数计算和早期退出优化")
    print("✅ 错误处理：完善的异常处理和性能监控")

if __name__ == "__main__":
    main()
