"""
Everything SDK Python接口
基于ctypes调用Everything.dll实现文件搜索功能
"""

import ctypes
import os
import sys
from ctypes import wintypes
from typing import List, Optional, Tuple, Dict
from dataclasses import dataclass
from pathlib import Path


@dataclass
class SearchResult:
    """搜索结果数据类"""
    filename: str
    path: str
    full_path: str
    is_folder: bool
    size: int = 0
    date_modified: Optional[str] = None
    extension: str = ""


class EverythingSDK:
    """Everything SDK Python接口类"""
    
    # 错误代码常量
    EVERYTHING_OK = 0
    EVERYTHING_ERROR_MEMORY = 1
    EVERYTHING_ERROR_IPC = 2
    EVERYTHING_ERROR_REGISTERCLASSEX = 3
    EVERYTHING_ERROR_CREATEWINDOW = 4
    EVERYTHING_ERROR_CREATETHREAD = 5
    EVERYTHING_ERROR_INVALIDINDEX = 6
    EVERYTHING_ERROR_INVALIDCALL = 7
    EVERYTHING_ERROR_INVALIDREQUEST = 8
    EVERYTHING_ERROR_INVALIDPARAMETER = 9
    
    # 排序常量
    EVERYTHING_SORT_NAME_ASCENDING = 1
    EVERYTHING_SORT_NAME_DESCENDING = 2
    EVERYTHING_SORT_PATH_ASCENDING = 3
    EVERYTHING_SORT_PATH_DESCENDING = 4
    EVERYTHING_SORT_SIZE_ASCENDING = 5
    EVERYTHING_SORT_SIZE_DESCENDING = 6
    EVERYTHING_SORT_DATE_MODIFIED_ASCENDING = 13
    EVERYTHING_SORT_DATE_MODIFIED_DESCENDING = 14
    
    # 请求标志常量
    EVERYTHING_REQUEST_FILE_NAME = 0x00000001
    EVERYTHING_REQUEST_PATH = 0x00000002
    EVERYTHING_REQUEST_FULL_PATH_AND_FILE_NAME = 0x00000004
    EVERYTHING_REQUEST_EXTENSION = 0x00000008
    EVERYTHING_REQUEST_SIZE = 0x00000010
    EVERYTHING_REQUEST_DATE_MODIFIED = 0x00000040
    
    def __init__(self):
        """初始化Everything SDK"""
        self.dll = None
        self._load_dll()
        self._setup_functions()
        
    def _load_dll(self):
        """加载Everything DLL"""
        # 确定DLL路径
        current_dir = Path(__file__).parent.parent.parent
        dll_dir = current_dir / "Everything-SDK" / "dll"
        
        # 根据系统架构选择DLL
        if sys.maxsize > 2**32:
            dll_path = dll_dir / "Everything64.dll"
        else:
            dll_path = dll_dir / "Everything32.dll"
            
        if not dll_path.exists():
            raise FileNotFoundError(f"Everything DLL not found: {dll_path}")
            
        try:
            self.dll = ctypes.WinDLL(str(dll_path))
        except Exception as e:
            raise RuntimeError(f"Failed to load Everything DLL: {e}")
    
    def _setup_functions(self):
        """设置DLL函数签名"""
        if not self.dll:
            return
            
        # 设置搜索
        self.dll.Everything_SetSearchW.argtypes = [wintypes.LPCWSTR]
        self.dll.Everything_SetSearchW.restype = None
        
        # 设置选项
        self.dll.Everything_SetMatchCase.argtypes = [wintypes.BOOL]
        self.dll.Everything_SetMatchCase.restype = None
        
        self.dll.Everything_SetMatchWholeWord.argtypes = [wintypes.BOOL]
        self.dll.Everything_SetMatchWholeWord.restype = None
        
        self.dll.Everything_SetRegex.argtypes = [wintypes.BOOL]
        self.dll.Everything_SetRegex.restype = None
        
        self.dll.Everything_SetMax.argtypes = [wintypes.DWORD]
        self.dll.Everything_SetMax.restype = None
        
        self.dll.Everything_SetOffset.argtypes = [wintypes.DWORD]
        self.dll.Everything_SetOffset.restype = None
        
        self.dll.Everything_SetSort.argtypes = [wintypes.DWORD]
        self.dll.Everything_SetSort.restype = None
        
        self.dll.Everything_SetRequestFlags.argtypes = [wintypes.DWORD]
        self.dll.Everything_SetRequestFlags.restype = None
        
        # 执行查询
        self.dll.Everything_QueryW.argtypes = [wintypes.BOOL]
        self.dll.Everything_QueryW.restype = wintypes.BOOL
        
        # 获取结果数量
        self.dll.Everything_GetNumResults.argtypes = []
        self.dll.Everything_GetNumResults.restype = wintypes.DWORD
        
        # 获取结果信息
        self.dll.Everything_GetResultFileNameW.argtypes = [wintypes.DWORD]
        self.dll.Everything_GetResultFileNameW.restype = wintypes.LPCWSTR
        
        self.dll.Everything_GetResultPathW.argtypes = [wintypes.DWORD]
        self.dll.Everything_GetResultPathW.restype = wintypes.LPCWSTR
        
        self.dll.Everything_GetResultFullPathNameW.argtypes = [wintypes.DWORD, wintypes.LPWSTR, wintypes.DWORD]
        self.dll.Everything_GetResultFullPathNameW.restype = wintypes.DWORD
        
        self.dll.Everything_IsFolderResult.argtypes = [wintypes.DWORD]
        self.dll.Everything_IsFolderResult.restype = wintypes.BOOL
        
        self.dll.Everything_IsFileResult.argtypes = [wintypes.DWORD]
        self.dll.Everything_IsFileResult.restype = wintypes.BOOL
        
        # 获取文件大小
        self.dll.Everything_GetResultSize.argtypes = [wintypes.DWORD, ctypes.POINTER(wintypes.LARGE_INTEGER)]
        self.dll.Everything_GetResultSize.restype = wintypes.BOOL
        
        # 获取扩展名
        self.dll.Everything_GetResultExtensionW.argtypes = [wintypes.DWORD]
        self.dll.Everything_GetResultExtensionW.restype = wintypes.LPCWSTR
        
        # 错误处理
        self.dll.Everything_GetLastError.argtypes = []
        self.dll.Everything_GetLastError.restype = wintypes.DWORD
        
        # 清理
        self.dll.Everything_Reset.argtypes = []
        self.dll.Everything_Reset.restype = None
        
        self.dll.Everything_CleanUp.argtypes = []
        self.dll.Everything_CleanUp.restype = None
    
    def search(self, query: str, max_results: int = 100, match_case: bool = False,
               match_whole_word: bool = False, use_regex: bool = False) -> List[SearchResult]:
        """
        执行搜索
        
        Args:
            query: 搜索查询字符串
            max_results: 最大结果数量
            match_case: 是否区分大小写
            match_whole_word: 是否匹配整个单词
            use_regex: 是否使用正则表达式
            
        Returns:
            搜索结果列表
        """
        if not self.dll:
            return []
            
        try:
            # 设置搜索参数
            self.dll.Everything_SetSearchW(query)
            self.dll.Everything_SetMax(max_results)
            self.dll.Everything_SetMatchCase(match_case)
            self.dll.Everything_SetMatchWholeWord(match_whole_word)
            self.dll.Everything_SetRegex(use_regex)
            
            # 设置请求标志
            request_flags = (
                self.EVERYTHING_REQUEST_FILE_NAME |
                self.EVERYTHING_REQUEST_PATH |
                self.EVERYTHING_REQUEST_FULL_PATH_AND_FILE_NAME |
                self.EVERYTHING_REQUEST_EXTENSION |
                self.EVERYTHING_REQUEST_SIZE
            )
            self.dll.Everything_SetRequestFlags(request_flags)
            
            # 设置排序
            self.dll.Everything_SetSort(self.EVERYTHING_SORT_NAME_ASCENDING)
            
            # 执行查询
            if not self.dll.Everything_QueryW(True):
                error_code = self.dll.Everything_GetLastError()
                print(f"Everything query failed with error code: {error_code}")
                return []
            
            # 获取结果
            num_results = self.dll.Everything_GetNumResults()
            results = []
            
            for i in range(num_results):
                result = self._get_result_at_index(i)
                if result:
                    results.append(result)
                    
            return results
            
        except Exception as e:
            print(f"Search error: {e}")
            return []

    def search_all_results(self, query: str, match_case: bool = False,
                          match_whole_word: bool = False, use_regex: bool = False,
                          max_safe_limit: int = 50000) -> List[SearchResult]:
        """
        动态获取Everything SDK的所有搜索结果

        这个方法不预设结果数量限制，而是通过智能策略获取SDK能返回的所有结果

        Args:
            query: 搜索查询字符串
            match_case: 是否区分大小写
            match_whole_word: 是否匹配整个单词
            use_regex: 是否使用正则表达式
            max_safe_limit: 安全限制，防止系统过载（默认50000）

        Returns:
            完整的搜索结果列表
        """
        if not self.dll:
            return []

        try:
            # 使用智能策略：直接尝试获取大量结果，然后检查是否被截断
            return self._smart_fetch_all_results(query, match_case, match_whole_word, use_regex, max_safe_limit)

        except Exception as e:
            print(f"Dynamic search error: {e}")
            return []

    def _smart_fetch_all_results(self, query: str, match_case: bool, match_whole_word: bool,
                                use_regex: bool, max_safe_limit: int) -> List[SearchResult]:
        """
        智能获取所有结果的策略
        """
        try:
            # 设置搜索参数
            self.dll.Everything_SetSearchW(query)
            self.dll.Everything_SetMax(max_safe_limit)  # 直接设置为安全限制
            self.dll.Everything_SetMatchCase(match_case)
            self.dll.Everything_SetMatchWholeWord(match_whole_word)
            self.dll.Everything_SetRegex(use_regex)

            # 设置请求标志
            request_flags = (
                self.EVERYTHING_REQUEST_FILE_NAME |
                self.EVERYTHING_REQUEST_PATH |
                self.EVERYTHING_REQUEST_FULL_PATH_AND_FILE_NAME |
                self.EVERYTHING_REQUEST_EXTENSION |
                self.EVERYTHING_REQUEST_SIZE
            )
            self.dll.Everything_SetRequestFlags(request_flags)

            # 设置排序
            self.dll.Everything_SetSort(self.EVERYTHING_SORT_NAME_ASCENDING)

            # 执行查询
            if not self.dll.Everything_QueryW(True):
                error_code = self.dll.Everything_GetLastError()
                print(f"Smart fetch query failed with error code: {error_code}")
                return []

            # 获取结果
            num_results = self.dll.Everything_GetNumResults()
            results = []

            if num_results > 0:
                print(f"Fetching {num_results} results dynamically...")

                for i in range(num_results):
                    result = self._get_result_at_index(i)
                    if result:
                        results.append(result)

                    # 每5000个结果显示一次进度
                    if (i + 1) % 5000 == 0:
                        print(f"Processed {i + 1}/{num_results} results...")

                print(f"Successfully fetched {len(results)} results")

                # 检查是否可能被截断
                if len(results) == max_safe_limit:
                    print(f"Warning: Results may be truncated at {max_safe_limit} limit")

            return results

        except Exception as e:
            print(f"Smart fetch error: {e}")
            return []

    def _probe_result_count(self, query: str, match_case: bool, match_whole_word: bool, use_regex: bool) -> int:
        """
        探测查询的实际结果数量
        使用大数值来获取总计数，然后只读取计数而不处理结果
        """
        try:
            # 设置搜索参数
            self.dll.Everything_SetSearchW(query)
            self.dll.Everything_SetMax(1000000)  # 设置一个很大的数值来获取所有结果的计数
            self.dll.Everything_SetMatchCase(match_case)
            self.dll.Everything_SetMatchWholeWord(match_whole_word)
            self.dll.Everything_SetRegex(use_regex)

            # 只设置最小的请求标志来提高性能
            self.dll.Everything_SetRequestFlags(self.EVERYTHING_REQUEST_FILE_NAME)

            # 执行查询
            if not self.dll.Everything_QueryW(True):
                error_code = self.dll.Everything_GetLastError()
                print(f"Probe query failed with error code: {error_code}")
                return 0

            # 获取实际结果数量
            actual_count = self.dll.Everything_GetNumResults()
            print(f"Probe found {actual_count} total results for query '{query}'")
            return actual_count

        except Exception as e:
            print(f"Probe error: {e}")
            return 0


    
    def _get_result_at_index(self, index: int) -> Optional[SearchResult]:
        """获取指定索引的搜索结果"""
        try:
            # 获取文件名
            filename_ptr = self.dll.Everything_GetResultFileNameW(index)
            filename = ctypes.wstring_at(filename_ptr) if filename_ptr else ""
            
            # 获取路径
            path_ptr = self.dll.Everything_GetResultPathW(index)
            path = ctypes.wstring_at(path_ptr) if path_ptr else ""
            
            # 获取完整路径
            buffer = ctypes.create_unicode_buffer(260)
            self.dll.Everything_GetResultFullPathNameW(index, buffer, 260)
            full_path = buffer.value
            
            # 检查是否为文件夹
            is_folder = bool(self.dll.Everything_IsFolderResult(index))
            
            # 获取文件大小
            size = 0
            if not is_folder:
                large_int = wintypes.LARGE_INTEGER()
                if self.dll.Everything_GetResultSize(index, ctypes.byref(large_int)):
                    size = large_int.value
            
            # 获取扩展名
            ext_ptr = self.dll.Everything_GetResultExtensionW(index)
            extension = ctypes.wstring_at(ext_ptr) if ext_ptr else ""
            
            return SearchResult(
                filename=filename,
                path=path,
                full_path=full_path,
                is_folder=is_folder,
                size=size,
                extension=extension
            )
            
        except Exception as e:
            print(f"Error getting result at index {index}: {e}")
            return None
    
    def is_everything_running(self) -> bool:
        """检查Everything是否正在运行"""
        if not self.dll:
            return False

        try:
            # 尝试执行一个简单的查询来检查Everything是否可用
            self.dll.Everything_SetSearchW("")
            self.dll.Everything_SetMax(1)
            result = self.dll.Everything_QueryW(True)

            if not result:
                error_code = self.dll.Everything_GetLastError()
                if error_code == self.EVERYTHING_ERROR_IPC:
                    return False

            return True
        except Exception:
            return False

    def get_version_info(self) -> Dict[str, int]:
        """获取Everything版本信息"""
        if not self.dll:
            return {}

        try:
            # 设置版本信息函数
            self.dll.Everything_GetMajorVersion.argtypes = []
            self.dll.Everything_GetMajorVersion.restype = wintypes.DWORD

            self.dll.Everything_GetMinorVersion.argtypes = []
            self.dll.Everything_GetMinorVersion.restype = wintypes.DWORD

            self.dll.Everything_GetRevision.argtypes = []
            self.dll.Everything_GetRevision.restype = wintypes.DWORD

            self.dll.Everything_GetBuildNumber.argtypes = []
            self.dll.Everything_GetBuildNumber.restype = wintypes.DWORD

            return {
                "major": self.dll.Everything_GetMajorVersion(),
                "minor": self.dll.Everything_GetMinorVersion(),
                "revision": self.dll.Everything_GetRevision(),
                "build": self.dll.Everything_GetBuildNumber()
            }
        except Exception as e:
            print(f"Failed to get version info: {e}")
            return {}

    def get_error_message(self, error_code: int) -> str:
        """获取错误消息"""
        error_messages = {
            self.EVERYTHING_OK: "No error",
            self.EVERYTHING_ERROR_MEMORY: "Out of memory",
            self.EVERYTHING_ERROR_IPC: "Everything search client is not running",
            self.EVERYTHING_ERROR_REGISTERCLASSEX: "Unable to register window class",
            self.EVERYTHING_ERROR_CREATEWINDOW: "Unable to create listening window",
            self.EVERYTHING_ERROR_CREATETHREAD: "Unable to create listening thread",
            self.EVERYTHING_ERROR_INVALIDINDEX: "Invalid index",
            self.EVERYTHING_ERROR_INVALIDCALL: "Invalid call",
            self.EVERYTHING_ERROR_INVALIDREQUEST: "Invalid request data",
            self.EVERYTHING_ERROR_INVALIDPARAMETER: "Bad parameter"
        }
        return error_messages.get(error_code, f"Unknown error: {error_code}")

    def reset(self):
        """重置Everything状态"""
        if self.dll:
            self.dll.Everything_Reset()

    def cleanup(self):
        """清理资源"""
        if self.dll:
            self.dll.Everything_CleanUp()


# 全局实例
_everything_sdk = None

def get_everything_sdk() -> EverythingSDK:
    """获取Everything SDK全局实例"""
    global _everything_sdk
    if _everything_sdk is None:
        _everything_sdk = EverythingSDK()
    return _everything_sdk

def is_everything_available() -> bool:
    """检查Everything是否可用"""
    try:
        sdk = get_everything_sdk()
        return sdk.is_everything_running()
    except Exception:
        return False
