
from PySide6.QtCore import QObject, Signal
import threading

class ThreadSafeHotkeyBridge(QObject):
    """线程安全的热键桥接器"""
    toggle_window_signal = Signal()
    
    def __init__(self, search_window):
        super().__init__()
        self.search_window = search_window
        self.toggle_window_signal.connect(self.safe_toggle_window)
    
    def safe_toggle_window(self):
        """线程安全的窗口切换方法"""
        try:
            print(f"[HOTKEY] 安全切换窗口 - 当前状态: {self.search_window.isVisible()}")

            if self.search_window.isVisible():
                # 立即隐藏，不使用动画（避免线程问题）
                self.search_window.hide()
                print("   窗口已隐藏")
            else:
                # 显示窗口
                self.search_window.show_window()
                print("   窗口已显示")

        except Exception as e:
            print(f"   [ERROR] 安全切换失败: {e}")
    
    def hotkey_callback(self):
        """热键回调函数（可能在其他线程中调用）"""
        thread_id = threading.get_ident()
        print(f"[HOTKEY] 热键回调被触发 (线程ID: {thread_id})")
        self.toggle_window_signal.emit()
