#!/usr/bin/env python3
"""
测试查询长度限制
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_query_length():
    """测试查询长度限制"""
    print("🔍 测试查询长度限制")
    print("=" * 50)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试不同长度的查询
        test_queries = [
            # 短查询
            '"C:\\Users\\<USER>\\Desktop\\" 微信 ext:lnk',
            
            # 中等长度查询
            '"C:\\Users\\<USER>\\Desktop\\" 微信 ext:lnk | "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\" 微信 ext:lnk',
            
            # 长查询（模拟当前生成的查询）
            '"C:\\Users\\<USER>\\Desktop\\" 微信 ext:lnk | "C:\\Users\\<USER>\\Desktop\\" 微信 ext:lnk | "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\" 微信 ext:lnk',
        ]
        
        for i, query in enumerate(test_queries):
            print(f"\n🔍 测试查询 {i+1}:")
            print(f"   长度: {len(query)} 字符")
            print(f"   查询: {query}")
            
            try:
                results = sdk.search(query, max_results=5)
                print(f"   结果数量: {len(results)}")
                
                lnk_count = 0
                for j, result in enumerate(results):
                    icon = "🔗" if result.filename.lower().endswith(".lnk") else "📁" if result.is_folder else "📄"
                    print(f"     {j+1}. {icon} {result.filename}")
                    print(f"        路径: {result.full_path}")
                    
                    if result.filename.lower().endswith(".lnk"):
                        lnk_count += 1
                
                if lnk_count > 0:
                    print(f"   ✅ 找到 {lnk_count} 个快捷方式")
                else:
                    print(f"   ❌ 未找到快捷方式")
                
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        # 测试当前生成的完整查询
        print(f"\n🔍 测试当前生成的完整查询:")
        
        from simple_desktop.search.engine import FileSearchEngine
        engine = FileSearchEngine(profile_id=0)
        scan_dirs = engine.get_scan_directories()
        
        full_query = engine._build_everything_query_with_directories(
            "微信", scan_dirs, ["executables"], True
        )
        
        print(f"   长度: {len(full_query)} 字符")
        print(f"   查询: {full_query[:200]}..." if len(full_query) > 200 else f"   查询: {full_query}")
        
        try:
            results = sdk.search(full_query, max_results=5)
            print(f"   结果数量: {len(results)}")
            
            if len(results) == 0:
                print("   ❌ 查询太长，可能超出Everything限制")
            else:
                print("   ✅ 查询成功")
                
        except Exception as e:
            print(f"   ❌ 查询失败: {e}")
        
        print("\n✅ 查询长度测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 查询长度限制测试")
    print("=" * 60)
    
    test_query_length()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")

if __name__ == "__main__":
    main()
