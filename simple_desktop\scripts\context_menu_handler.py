"""
SimpleDesktop右键菜单处理脚本
"""

import sys
import os
import json
import tempfile
import subprocess
from pathlib import Path

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: context_menu_handler.py <folder_path>")
        return
    
    folder_path = sys.argv[1]
    
    # 验证路径
    if not os.path.isdir(folder_path):
        print(f"Invalid folder path: {folder_path}")
        return
    
    print(f"Processing folder: {folder_path}")
    
    try:
        # 启动Profile选择对话框
        script_dir = Path(__file__).parent.parent
        dialog_script = script_dir / "scripts" / "profile_selector.py"
        
        # 使用subprocess启动对话框
        result = subprocess.run([
            sys.executable, 
            str(dialog_script), 
            folder_path
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("Folder added successfully")
        else:
            print(f"Failed to add folder: {result.stderr}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
