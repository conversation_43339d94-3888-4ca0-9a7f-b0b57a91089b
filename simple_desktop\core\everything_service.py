"""
Everything 服务管理模块
提供 Everything 服务的启动、停止、状态检查等功能
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path
from typing import Optional, Callable, Dict, Any
import ctypes
from ctypes import wintypes

class EverythingServiceManager:
    """Everything 服务管理器"""
    
    def __init__(self):
        """初始化服务管理器"""
        self.service_name = "Everything"
        self.everything_exe_path = self._find_everything_exe()
        self._status_callbacks = []
        self._monitoring = False
        self._monitor_thread = None
        
    def _find_everything_exe(self) -> Optional[str]:
        """查找 Everything.exe 的路径"""
        # 方法1: 检查应用程序目录
        app_dir = Path(__file__).parent.parent.parent
        everything_exe = app_dir / "everything.exe"
        if everything_exe.exists():
            return str(everything_exe)
        
        # 方法2: 检查打包后的目录结构
        if getattr(sys, 'frozen', False):
            # 如果是打包的exe
            exe_dir = Path(sys.executable).parent
            everything_exe = exe_dir / "everything.exe"
            if everything_exe.exists():
                return str(everything_exe)
        
        # 方法3: 检查系统PATH
        try:
            result = subprocess.run(
                ["where", "everything.exe"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                return result.stdout.strip().split('\n')[0]
        except Exception:
            pass
        
        return None
    
    def is_service_installed(self) -> bool:
        """检查 Everything 服务是否已安装"""
        try:
            result = subprocess.run(
                ["sc", "query", self.service_name],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def is_service_running(self) -> bool:
        """检查 Everything 服务是否正在运行"""
        try:
            result = subprocess.run(
                ["sc", "query", self.service_name],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout.lower()
                return "running" in output
            
            return False
        except Exception:
            return False
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取详细的服务状态信息"""
        status = {
            "installed": False,
            "running": False,
            "state": "UNKNOWN",
            "pid": None,
            "error": None
        }
        
        try:
            result = subprocess.run(
                ["sc", "query", self.service_name],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                status["installed"] = True
                output = result.stdout
                
                # 解析服务状态
                for line in output.split('\n'):
                    line = line.strip()
                    if "STATE" in line:
                        if "RUNNING" in line:
                            status["running"] = True
                            status["state"] = "RUNNING"
                        elif "STOPPED" in line:
                            status["state"] = "STOPPED"
                        elif "PENDING" in line:
                            status["state"] = "PENDING"
                    elif "PID" in line:
                        try:
                            pid_str = line.split(":")[-1].strip()
                            status["pid"] = int(pid_str)
                        except (ValueError, IndexError):
                            pass
            else:
                status["error"] = f"Service query failed: {result.stderr}"
                
        except Exception as e:
            status["error"] = str(e)
        
        return status
    
    def start_service(self) -> bool:
        """启动 Everything 服务"""
        try:
            print("正在启动 Everything 服务...")
            
            # 方法1: 使用 sc start 命令
            result = subprocess.run(
                ["sc", "start", self.service_name],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print("Everything 服务启动成功")
                return True
            
            # 如果 sc start 失败，尝试使用 everything.exe -start-service
            if self.everything_exe_path:
                print("尝试使用 everything.exe 启动服务...")
                result = subprocess.run(
                    [self.everything_exe_path, "-start-service"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    print("Everything 服务启动成功")
                    return True
                else:
                    print(f"启动服务失败: {result.stderr}")
            
            return False
            
        except Exception as e:
            print(f"启动 Everything 服务时发生错误: {e}")
            return False
    
    def stop_service(self) -> bool:
        """停止 Everything 服务"""
        try:
            print("正在停止 Everything 服务...")
            
            result = subprocess.run(
                ["sc", "stop", self.service_name],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print("Everything 服务停止成功")
                return True
            else:
                print(f"停止服务失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"停止 Everything 服务时发生错误: {e}")
            return False
    
    def install_service(self) -> bool:
        """安装 Everything 服务"""
        if not self.everything_exe_path:
            print("错误: 找不到 everything.exe")
            return False
        
        try:
            print("正在安装 Everything 服务...")
            
            result = subprocess.run(
                [self.everything_exe_path, "-install-service"],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print("Everything 服务安装成功")
                return True
            else:
                print(f"安装服务失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"安装 Everything 服务时发生错误: {e}")
            return False
    
    def ensure_service_running(self) -> bool:
        """确保 Everything 服务正在运行"""
        print("检查 Everything 服务状态...")
        
        # 检查服务是否已安装
        if not self.is_service_installed():
            print("Everything 服务未安装，尝试安装...")
            if not self.install_service():
                print("安装 Everything 服务失败")
                return False
            
            # 等待安装完成
            time.sleep(2)
        
        # 检查服务是否正在运行
        if not self.is_service_running():
            print("Everything 服务未运行，尝试启动...")
            if not self.start_service():
                print("启动 Everything 服务失败")
                return False
            
            # 等待服务启动
            for i in range(10):  # 最多等待10秒
                time.sleep(1)
                if self.is_service_running():
                    print("Everything 服务启动成功")
                    return True
                print(f"等待服务启动... ({i+1}/10)")
            
            print("服务启动超时")
            return False
        
        print("Everything 服务已在运行")
        return True
    
    def add_status_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加状态变化回调函数"""
        if callback not in self._status_callbacks:
            self._status_callbacks.append(callback)
    
    def remove_status_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """移除状态变化回调函数"""
        if callback in self._status_callbacks:
            self._status_callbacks.remove(callback)
    
    def start_monitoring(self, interval: float = 30.0) -> bool:
        """开始监控服务状态"""
        if self._monitoring:
            return True

        self._monitoring = True
        self._restart_attempts = 0
        self._max_restart_attempts = 3

        def monitor_loop():
            last_status = None
            consecutive_failures = 0

            while self._monitoring:
                try:
                    current_status = self.get_service_status()

                    # 检查状态是否发生变化
                    if current_status != last_status:
                        print(f"Everything 服务状态变化: {current_status}")
                        last_status = current_status

                        # 调用回调函数
                        for callback in self._status_callbacks:
                            try:
                                callback(current_status)
                            except Exception as e:
                                print(f"状态回调错误: {e}")

                    # 检查服务是否需要重启
                    if not current_status.get("running", False):
                        consecutive_failures += 1
                        print(f"检测到 Everything 服务停止 (连续失败: {consecutive_failures})")

                        # 避免频繁重启，等待一定次数后再尝试
                        if consecutive_failures >= 2 and self._restart_attempts < self._max_restart_attempts:
                            print(f"尝试重新启动服务 (第 {self._restart_attempts + 1}/{self._max_restart_attempts} 次)")
                            if self.ensure_service_running():
                                self._restart_attempts = 0
                                consecutive_failures = 0
                                print("服务重启成功")
                            else:
                                self._restart_attempts += 1
                                print(f"服务重启失败，已尝试 {self._restart_attempts} 次")
                    else:
                        # 服务正常运行，重置计数器
                        consecutive_failures = 0
                        if self._restart_attempts > 0:
                            print("服务已恢复正常，重置重启计数器")
                            self._restart_attempts = 0

                    time.sleep(interval)

                except Exception as e:
                    print(f"服务监控错误: {e}")
                    time.sleep(interval * 2)

        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()

        print(f"开始监控 Everything 服务状态 (间隔: {interval}秒)")
        return True
    
    def stop_monitoring(self):
        """停止监控服务状态"""
        self._monitoring = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)
        print("停止监控 Everything 服务状态")


# 全局实例
_service_manager = None

def get_service_manager() -> EverythingServiceManager:
    """获取 Everything 服务管理器全局实例"""
    global _service_manager
    if _service_manager is None:
        _service_manager = EverythingServiceManager()
    return _service_manager
