@echo off
chcp 65001 >nul
echo ================================================
echo Simple Desktop 可执行文件测试
echo ================================================
echo.

echo 检查生成的文件...
if exist "dist\SimpleDesktop.exe" (
    echo ✓ SimpleDesktop.exe 存在
) else (
    echo ✗ SimpleDesktop.exe 不存在
    goto :end
)

if exist "dist\Everything64.dll" (
    echo ✓ Everything64.dll 存在
) else (
    echo ✗ Everything64.dll 不存在
)

if exist "dist\ic.ico" (
    echo ✓ ic.ico 存在
) else (
    echo ✗ ic.ico 不存在
)

if exist "dist\install.bat" (
    echo ✓ install.bat 存在
) else (
    echo ✗ install.bat 不存在
)

echo.
echo 检查文件大小...
for %%f in (dist\SimpleDesktop.exe) do echo SimpleDesktop.exe: %%~zf 字节
for %%f in (dist\Everything64.dll) do echo Everything64.dll: %%~zf 字节

echo.
echo 测试可执行文件...
echo 注意：程序将启动，请手动关闭以继续测试
echo 按任意键启动程序...
pause >nul

start "" "dist\SimpleDesktop.exe"

echo.
echo 程序已启动，请检查：
echo 1. 程序是否正常启动
echo 2. 图标是否正确显示
echo 3. 搜索功能是否正常
echo 4. 热键是否工作
echo.

:end
echo 测试完成！
pause
