# Simple Desktop ESC键新行为示例

## 启动应用程序
python main.py

## ESC键行为
- 按ESC键：完全退出Simple Desktop应用程序
- 不再需要Shift+ESC组合键
- 退出时会自动：
  * 停止热键监听
  * 隐藏系统托盘图标
  * 关闭搜索窗口
  * 完全退出应用程序

## 其他热键功能保持不变
- 双击Ctrl键：切换搜索窗口显示/隐藏
- Ctrl+0~9：切换Profile（如果可用）

## 代码实现
在 simple_desktop/ui/search_window.py 的 keyPressEvent 方法中：

```python
def keyPressEvent(self, event):
    if event.key() == Qt.Key.Key_Escape:
        # ESC键: 退出整个应用程序
        if self.app_manager:
            print("[HOTKEY] ESC 检测到，退出应用程序")
            self.app_manager.quit_app()
        else:
            print("[HOTKEY] ESC 检测到，退出应用程序")
            QApplication.instance().quit()
```