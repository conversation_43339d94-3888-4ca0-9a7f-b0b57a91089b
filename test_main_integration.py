#!/usr/bin/env python3
"""
测试main.py集成的热键修复功能
"""

import sys
import time
import subprocess
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_main_app():
    """测试main.py应用程序"""
    print("🧪 测试main.py应用程序集成")
    print("=" * 60)
    
    try:
        # 启动main.py应用程序
        print("1. 启动main.py应用程序...")
        
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print(f"   ✅ 应用程序已启动 (PID: {process.pid})")
        
        # 等待应用程序启动
        print("2. 等待应用程序初始化...")
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("   ✅ 应用程序正在运行")
        else:
            print("   ❌ 应用程序已退出")
            stdout, stderr = process.communicate()
            print(f"   标准输出: {stdout}")
            print(f"   错误输出: {stderr}")
            return False
        
        print("\n📝 测试说明:")
        print("1. Simple Desktop应用程序已启动")
        print("2. 请尝试以下操作:")
        print("   - 双击Ctrl键：切换搜索窗口显示/隐藏")
        print("   - ESC键：隐藏搜索窗口")
        print("   - Shift+ESC键：退出整个应用程序")
        print("   - 右键点击系统托盘图标：查看菜单")
        print("3. 测试将在15秒后自动结束")
        
        # 等待测试时间
        start_time = time.time()
        while time.time() - start_time < 15:
            if process.poll() is not None:
                print("\n🔥 应用程序已退出")
                break
            time.sleep(0.5)
        
        # 如果进程还在运行，终止它
        if process.poll() is None:
            print("\n⏹️ 终止应用程序...")
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=5)
                print("   ✅ 应用程序已正常终止")
            except subprocess.TimeoutExpired:
                print("   ⚠️ 强制终止应用程序")
                process.kill()
                process.wait()
        
        # 获取输出
        try:
            stdout, stderr = process.communicate(timeout=2)
            
            print("\n📊 应用程序输出:")
            if stdout:
                print("标准输出:")
                for line in stdout.strip().split('\n'):
                    if line.strip():
                        print(f"  {line}")
            
            if stderr:
                print("错误输出:")
                for line in stderr.strip().split('\n'):
                    if line.strip():
                        print(f"  {line}")
        except subprocess.TimeoutExpired:
            print("   ⚠️ 获取输出超时")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_hotkey_bridge_availability():
    """检查热键桥接器是否可用"""
    print("\n🔍 检查热键桥接器可用性")
    print("=" * 60)
    
    try:
        from simple_desktop.core.hotkey_bridge import ThreadSafeHotkeyBridge
        print("✅ 热键桥接器可用")
        
        # 测试桥接器创建
        from simple_desktop.ui.search_window import FloatingSearchWindow
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        search_window = FloatingSearchWindow()
        bridge = ThreadSafeHotkeyBridge(search_window)
        
        print("✅ 热键桥接器创建成功")
        print(f"   桥接器类型: {type(bridge)}")
        print(f"   搜索窗口类型: {type(search_window)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 热键桥接器不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ 热键桥接器测试失败: {e}")
        return False

def check_app_integration():
    """检查应用程序集成"""
    print("\n🔍 检查应用程序集成")
    print("=" * 60)
    
    try:
        from simple_desktop.app import SimpleDesktopApp
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序实例（不运行）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        desktop_app = SimpleDesktopApp()
        
        print("✅ SimpleDesktopApp创建成功")
        
        # 检查热键桥接器是否已设置
        if hasattr(desktop_app, 'hotkey_bridge'):
            print("✅ 热键桥接器已集成")
            print(f"   桥接器类型: {type(desktop_app.hotkey_bridge)}")
        else:
            print("⚠️ 热键桥接器未集成，使用原始热键管理器")
        
        # 检查搜索窗口
        if desktop_app.search_window:
            print("✅ 搜索窗口已创建")
            print(f"   窗口类型: {type(desktop_app.search_window)}")
            
            # 检查app_manager引用
            if desktop_app.search_window.app_manager:
                print("✅ app_manager引用已设置")
            else:
                print("❌ app_manager引用未设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用程序集成检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - main.py集成测试")
    print("=" * 80)
    
    # 执行检查步骤
    checks = [
        ("检查热键桥接器可用性", check_hotkey_bridge_availability),
        ("检查应用程序集成", check_app_integration),
        ("测试main.py应用程序", test_main_app),
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            if check_func():
                passed_checks += 1
                print(f"✅ {check_name} 通过")
            else:
                print(f"❌ {check_name} 失败")
        except Exception as e:
            print(f"❌ {check_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 main.py集成测试完成！")
    print(f"通过检查: {passed_checks}/{total_checks}")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查都通过！")
        print("\n💡 使用说明:")
        print("1. 运行 python main.py 启动应用程序")
        print("2. 双击Ctrl键切换搜索窗口")
        print("3. ESC键隐藏窗口")
        print("4. Shift+ESC键退出应用程序")
        print("5. 右键系统托盘图标查看更多选项")
    else:
        print(f"\n⚠️ 有 {total_checks - passed_checks} 项检查失败")
        print("请检查相关配置和依赖")

if __name__ == "__main__":
    main()
