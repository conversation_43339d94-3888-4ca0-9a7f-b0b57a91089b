#!/usr/bin/env python3
"""
测试简单查询语法
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_simple_syntax():
    """测试简单查询语法"""
    print("🔍 测试简单查询语法")
    print("=" * 50)
    
    try:
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        sdk = get_everything_sdk()
        
        # 测试不同的查询语法
        test_queries = [
            # 基础语法
            '微信 ext:lnk',
            
            # 路径限制语法
            'path:"C:\\Users\\<USER>\\Desktop" 微信',
            'path:"C:\\Users\\<USER>\\Desktop" ext:lnk',
            'path:"C:\\Users\\<USER>\\Desktop" 微信 ext:lnk',
            
            # 多路径语法
            '(path:"C:\\Users\\<USER>\\Desktop" | path:"C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs") 微信',
            '(path:"C:\\Users\\<USER>\\Desktop" | path:"C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs") ext:lnk',
            
            # 组合语法
            '微信 ext:lnk path:"C:\\Users\\<USER>\\Desktop"',
            'ext:lnk 微信 path:"C:\\Users\\<USER>\\Desktop"',
        ]
        
        for i, query in enumerate(test_queries):
            print(f"\n🔍 测试查询 {i+1}: {query}")
            print(f"   长度: {len(query)} 字符")
            
            try:
                results = sdk.search(query, max_results=5)
                print(f"   结果数量: {len(results)}")
                
                lnk_count = 0
                for j, result in enumerate(results):
                    icon = "🔗" if result.filename.lower().endswith(".lnk") else "📁" if result.is_folder else "📄"
                    print(f"     {j+1}. {icon} {result.filename}")
                    print(f"        路径: {result.full_path}")
                    
                    if result.filename.lower().endswith(".lnk"):
                        lnk_count += 1
                
                if lnk_count > 0:
                    print(f"   ✅ 找到 {lnk_count} 个快捷方式")
                else:
                    print(f"   ❌ 未找到快捷方式")
                
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        print("\n✅ 简单查询语法测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🛠️ Simple Desktop - 简单查询语法测试")
    print("=" * 60)
    
    test_simple_syntax()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")

if __name__ == "__main__":
    main()
