#!/usr/bin/env python3
"""
更新Profile配置，移除Documents和Downloads目录
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def update_profile_directories():
    """更新Profile 0的扫描目录配置"""
    print("🔧 更新Profile 0的扫描目录配置")
    print("=" * 50)
    
    try:
        from simple_desktop.core.config import config_manager
        
        # 获取当前扫描目录
        current_dirs = config_manager.get_scan_directories(0)
        print("当前扫描目录:")
        for i, dir_path in enumerate(current_dirs):
            exists = "✅" if os.path.exists(dir_path) else "❌"
            print(f"  {i+1}. {exists} {dir_path}")
        
        # 定义需要移除的目录
        unwanted_dirs = [
            str(Path.home() / "Documents"),
            str(Path.home() / "Downloads")
        ]
        
        # 移除不需要的目录
        removed_count = 0
        for unwanted_dir in unwanted_dirs:
            # 查找匹配的目录（不区分大小写）
            for current_dir in current_dirs:
                if unwanted_dir.lower() == current_dir.lower():
                    success = config_manager.remove_scan_directory(current_dir, 0)
                    if success:
                        print(f"✅ 已移除: {current_dir}")
                        removed_count += 1
                    else:
                        print(f"❌ 移除失败: {current_dir}")
                    break
        
        if removed_count == 0:
            print("ℹ️ 没有找到需要移除的目录")
        
        # 显示更新后的目录列表
        updated_dirs = config_manager.get_scan_directories(0)
        print(f"\n更新后的扫描目录 ({len(updated_dirs)}个):")
        for i, dir_path in enumerate(updated_dirs):
            exists = "✅" if os.path.exists(dir_path) else "❌"
            print(f"  {i+1}. {exists} {dir_path}")
        
        # 验证优化效果
        print(f"\n📊 优化效果:")
        print(f"   移除目录数量: {removed_count}")
        print(f"   当前目录数量: {len(updated_dirs)}")
        
        # 检查是否包含期望的目录
        expected_dirs = [
            str(Path.home() / "Desktop"),
            "C:\\Users\\<USER>\\Desktop",
            "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs"
        ]
        
        print(f"\n✅ 期望目录验证:")
        for expected_dir in expected_dirs:
            found = any(expected_dir.lower() in scan_dir.lower() for scan_dir in updated_dirs)
            status = "✅" if found else "❌"
            print(f"   {status} {expected_dir}")
        
        # 检查是否还有不期望的目录
        print(f"\n🚫 不期望目录验证:")
        for unwanted_dir in unwanted_dirs:
            found = any(unwanted_dir.lower() in scan_dir.lower() for scan_dir in updated_dirs)
            status = "❌" if found else "✅"
            print(f"   {status} {unwanted_dir} {'(仍存在)' if found else '(已移除)'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新Profile配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ Simple Desktop - Profile配置更新")
    print("=" * 60)
    
    success = update_profile_directories()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎯 Profile配置更新完成！")
        print("\n💡 优化效果:")
        print("- 移除了Documents和Downloads目录")
        print("- 聚焦于桌面和程序菜单相关目录")
        print("- 减少搜索结果中不相关的文档和下载文件")
        print("\n建议重新运行搜索优化测试以验证效果")
    else:
        print("❌ Profile配置更新失败")

if __name__ == "__main__":
    main()
