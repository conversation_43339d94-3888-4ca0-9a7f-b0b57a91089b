#!/usr/bin/env python3
"""
两阶段过滤功能测试脚本
验证新的搜索逻辑是否正常工作
"""

import sys
import os
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_search():
    """测试基本搜索功能"""
    print("🔍 测试基本搜索功能...")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.core.everything_sdk import get_everything_sdk
        
        # 检查Everything是否可用
        sdk = get_everything_sdk()
        if not sdk.is_everything_running():
            print("❌ Everything未运行，请先启动Everything")
            return False
        
        # 创建搜索引擎
        engine = FileSearchEngine(profile_id=0)
        
        # 测试简单搜索
        results = engine.search("test", limit=10)
        print(f"   搜索'test'结果数量: {len(results)}")
        
        if results:
            print("   前3个结果:")
            for i, result in enumerate(results[:3]):
                print(f"     {i+1}. {result.filename} ({result.item_type})")
                print(f"        路径: {result.filepath}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本搜索测试失败: {e}")
        return False

def test_directory_filtering():
    """测试目录过滤功能"""
    print("\n📁 测试目录过滤功能...")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试目录过滤
        desktop_path = str(Path.home() / "Desktop")
        criteria = FilterCriteria(
            scan_directories=[desktop_path],
            include_folders=True
        )
        
        results = engine._search_with_filters("txt", criteria, limit=10)
        print(f"   桌面目录下的'txt'搜索结果: {len(results)}")
        
        # 验证所有结果都在桌面目录下
        all_in_desktop = True
        for result in results:
            if not result.filepath.startswith(desktop_path):
                all_in_desktop = False
                print(f"   ❌ 发现不在桌面目录的结果: {result.filepath}")
                break
        
        if all_in_desktop and results:
            print("   ✅ 所有结果都在指定目录下")
        elif not results:
            print("   ℹ️ 桌面目录下没有找到匹配的文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 目录过滤测试失败: {e}")
        return False

def test_file_type_filtering():
    """测试文件类型过滤功能"""
    print("\n📄 测试文件类型过滤功能...")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试文档类型过滤
        criteria = FilterCriteria(
            file_types=["documents"],
            include_folders=False
        )
        
        results = engine._search_with_filters("test", criteria, limit=10)
        print(f"   文档类型的'test'搜索结果: {len(results)}")
        
        # 验证结果都是文档类型
        doc_extensions = {'.txt', '.doc', '.docx', '.pdf', '.rtf', '.odt'}
        all_documents = True
        
        for result in results:
            if result.suffix.lower() not in doc_extensions:
                all_documents = False
                print(f"   ❌ 发现非文档类型: {result.filename} ({result.suffix})")
                break
        
        if all_documents and results:
            print("   ✅ 所有结果都是文档类型")
        elif not results:
            print("   ℹ️ 没有找到匹配的文档文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件类型过滤测试失败: {e}")
        return False

def test_extension_filtering():
    """测试扩展名过滤功能"""
    print("\n🔧 测试扩展名过滤功能...")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试特定扩展名过滤
        criteria = FilterCriteria(
            extensions={".txt", ".log"},
            include_folders=False
        )
        
        results = engine._search_with_filters("test", criteria, limit=10)
        print(f"   .txt和.log扩展名的'test'搜索结果: {len(results)}")
        
        # 验证结果都是指定扩展名
        allowed_extensions = {'.txt', '.log'}
        all_correct_ext = True
        
        for result in results:
            if result.suffix.lower() not in allowed_extensions:
                all_correct_ext = False
                print(f"   ❌ 发现错误扩展名: {result.filename} ({result.suffix})")
                break
        
        if all_correct_ext and results:
            print("   ✅ 所有结果都是指定扩展名")
        elif not results:
            print("   ℹ️ 没有找到匹配的扩展名文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 扩展名过滤测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n⚡ 测试搜索性能...")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试大结果集的性能
        start_time = time.time()
        
        criteria = FilterCriteria(
            file_types=["documents", "images"],
            include_folders=True
        )
        
        results = engine._search_with_filters("a", criteria, limit=100)
        
        end_time = time.time()
        search_time = end_time - start_time
        
        print(f"   搜索'a'并过滤文档和图片类型:")
        print(f"   结果数量: {len(results)}")
        print(f"   搜索时间: {search_time:.2f}秒")
        
        if search_time < 5.0:
            print("   ✅ 性能良好")
        else:
            print("   ⚠️ 性能可能需要优化")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 两阶段过滤功能测试开始")
    print("=" * 50)
    
    tests = [
        test_basic_search,
        test_directory_filtering,
        test_file_type_filtering,
        test_extension_filtering,
        test_performance
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！两阶段过滤功能正常工作")
    else:
        print("⚠️ 部分测试失败，请检查实现")

if __name__ == "__main__":
    main()
