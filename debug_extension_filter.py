#!/usr/bin/env python3
"""
调试扩展名过滤器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_extension_filter():
    """调试扩展名过滤器"""
    print("🔧 调试扩展名过滤器...")
    
    try:
        from simple_desktop.search.engine import FileSearchEngine
        from simple_desktop.search.filters import FilterCriteria
        
        engine = FileSearchEngine(profile_id=0)
        
        # 测试特定扩展名过滤
        criteria = FilterCriteria(
            extensions={".txt", ".log"},
            include_folders=False
        )
        
        results = engine._search_with_filters("test", criteria, limit=10)
        print(f"   .txt和.log扩展名的'test'搜索结果: {len(results)}")
        
        # 详细检查每个结果
        for i, result in enumerate(results):
            print(f"   {i+1}. 文件名: {result.filename}")
            print(f"      路径: {result.filepath}")
            print(f"      扩展名: '{result.suffix}'")
            print(f"      类型: {result.item_type}")
            print()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_extension_filter()
